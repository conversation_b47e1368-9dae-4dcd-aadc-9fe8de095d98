import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../../infra/config';
import { AppModel } from '../../base/AppModel';


export type survey_questionPk = "id";
export type survey_questionId = survey_question[survey_questionPk];

export class survey_question extends AppModel {
  id!: number;
  code?: string;
  principle?: string;
  enabler?: string;
  title?: string;
  detail?: string;
  applicable_survey_type?: string;
  detail_self?: string;
  lang_code?: string;
  question_type?: string;
  rating_scale_id?: number;
  order_number?: number;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof survey_question {
    this.HasSoftDelete = true;
    survey_question.init({
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true
      },
      code: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      survey_master_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      principle: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      enabler: {
        type: DataTypes.STRING,
        allowNull: true
      },
      title: {
        type: DataTypes.STRING,
        allowNull: true
      },
      detail: {
        type: DataTypes.STRING,
        allowNull: true
      },
      applicable_survey_type: {
        type: DataTypes.STRING,
        allowNull: true
      },
      detail_self: {
        type: DataTypes.STRING,
        allowNull: true
      },
      lang_code: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      question_type: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      rating_scale_id: {
        type: DataTypes.NUMBER,
        allowNull: true
      },
      order_number: {
        type: DataTypes.NUMBER,
        allowNull: true
      },
      active: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      deleted: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      created_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      created_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      modified_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      modified_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
    }, {
      sequelize,
      tableName: 'survey_question',
      schema: env.database.schema_exec,
      timestamps: false,
      indexes: [
        {
          name: "survey_question_pk",
          unique: true,
          fields: [
            { name: "id" },
          ]
        },
      ]
    });
    return survey_question;
  }
}
