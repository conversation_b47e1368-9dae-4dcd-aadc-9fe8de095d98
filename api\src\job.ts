import { container } from "./container";
import { SyncTask } from "./infra/jobs/synctask";

process.on('unhandledRejection', (err) => {
    console.error(err instanceof Error ? err.message : err);
    process.exit(-1);
});

const job: SyncTask = container.resolve('syncTaskJob');

(async() => {
    console.log('before job start');
    try {
        await job.start();
     }
    catch (ex) {
        console.log(ex);
    }
    console.log('after job end');
})();

