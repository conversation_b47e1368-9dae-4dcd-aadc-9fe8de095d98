import { LoggerService } from "../infra/services/loggerservice";
import { CacheManager } from "../infra/cachemanager";
import { Config } from "../infra/config/model";
import _ from "lodash";
import { activeNonDeletedQuery, greaterOrequalQuery, inQuery, lessOrequalQuery, lessQuery, navQuery, nonDeletedQuery, notInQuery, notQuery, notequalQuery, orQuery, textSearchQuery, textSearchQueryLike, usernameQuery } from "../infra/database/query/general";
import { EmailTemplatesSurvey, EntityTypes, NavType, PermissionType, SurveyType, UserStatus, UserStatus_Cohort, WorkflowScreenURL, WorkflowStatus, WorkflowStatus_Cohort } from "../shared/enum/general";
import { env } from "../infra/config";
import { WorkContextUser } from "../shared/context/workcontext";
import { survey_instanceRepo } from "../infra/database/repository/survey_instance.repo";
import { Attachment, DTOSurveyInstance, InitiateSurvey, ProgrammDetails, SurveyInstance, SurveyInstanceCohort, SurveyInstanceDetail, SurveyQuestion, SurveySection, UserSurvey, filters } from "../web/routes/app/survey/model";
import { survey_instance } from "../domain/entities/survey_instance";
import { EmployeeDataService } from "./employeeData.service";
import { survey_sectionRepo } from "../infra/database/repository/survey_section.repo";
import { survey_questionRepo } from "../infra/database/repository/survey_question.repo";
import { option_listRepo } from "../infra/database/repository/option_list.repo";
import { survey_instance_detail } from "../domain/entities/survey_instance_detail";
import { CommonService } from "./common.service";
import { survey_instance_detailRepo } from "../infra/database/repository/survey_instance_detail.repo";
import { ReportService } from "./report.service";
import moment from "moment";
import excel from 'exceljs';
import path from "path";
import { survey_masterRepo } from "../infra/database/repository/survey_master.repo";
import { survey_instance_cohortRepo } from "../infra/database/repository/survey_instance_cohort.repo";
import { survey_instance_cohort } from "../domain/entities/survey_instance_cohort";
import { UserSearchService } from "../infra/services/graphapi/usersearch.service";

export class SurveyService {
    _logger: LoggerService;
    _cacheManager: CacheManager;
    _tennantId: string;
    _appId: string;
    _survey_instanceRepo: survey_instanceRepo;
    _survey_instance_detailRepo: survey_instance_detailRepo;
    _survey_sectionRepo: survey_sectionRepo;
    _survey_questionRepo: survey_questionRepo;
    _option_listRepo: option_listRepo;
    _employeeDataService: EmployeeDataService;
    _commonService: CommonService;
    _reportService: ReportService;
    _survey_masterRepo: survey_masterRepo;
    _survey_instance_cohortRepo: survey_instance_cohortRepo;
    _userSearchService: UserSearchService;

    private templatepath = path.join(__dirname, '..', 'templates');

    constructor(config: Config, logger: LoggerService, cacheManager: CacheManager, survey_instanceRepo: survey_instanceRepo,
        employeeDataService: EmployeeDataService, survey_sectionRepo: survey_sectionRepo, survey_questionRepo: survey_questionRepo,
        option_listRepo: option_listRepo, commonService: CommonService, survey_instance_detailRepo: survey_instance_detailRepo,
        reportService: ReportService, survey_masterRepo: survey_masterRepo, survey_instance_cohortRepo: survey_instance_cohortRepo, userSearchService: UserSearchService) {
        this._logger = logger;
        this._cacheManager = cacheManager;
        this._tennantId = config.tennantId;
        this._appId = config.applicationId;
        this._survey_instanceRepo = survey_instanceRepo;
        this._survey_instance_detailRepo = survey_instance_detailRepo;
        this._employeeDataService = employeeDataService;
        this._survey_sectionRepo = survey_sectionRepo;
        this._survey_questionRepo = survey_questionRepo;
        this._option_listRepo = option_listRepo;
        this._commonService = commonService;
        this._reportService = reportService;
        this._survey_masterRepo = survey_masterRepo;
        this._survey_instance_cohortRepo = survey_instance_cohortRepo;
        this._userSearchService = userSearchService;
    }

    public async createSurveyinstanceCohort(request: SurveyInstanceCohort, workcontext: WorkContextUser) {
        var cohort = survey_instance_cohort.build();
        cohort.root_entity_code = request.root_entity_code;
        cohort.root_entity_id = request.root_entity_id;
        cohort.title = request.title;
        cohort.detail = request.detail;
        cohort.due_date = request.due_date;
        cohort.post_program_survey_start_date = request.post_program_survey_start_date;
        cohort.post_program_survey_end_date = request.post_program_survey_end_date;
        cohort.master_id = request.master_id;
        cohort.user_status = UserStatus_Cohort.Open;
        cohort.workflow_status = WorkflowStatus_Cohort.Open;
        let survey_cohort = await this._survey_instance_cohortRepo.create(cohort, workcontext.userName);
        return survey_cohort;
    }

    public async editSurveyinstanceCohort(request: SurveyInstanceCohort, workcontext: WorkContextUser) {
        let survey_instance_cohort = await this._survey_instance_cohortRepo.getById(request.id);
        survey_instance_cohort.title = request.title;
        survey_instance_cohort.detail = request.detail;
        if (survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.Open) {
            survey_instance_cohort.due_date = request.due_date;
        }
        if (survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.Open ||
            survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.SurveyCompleted) {
            survey_instance_cohort.post_program_survey_start_date = request.post_program_survey_start_date;
        }
        if (survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.Open ||
            survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.SurveyCompleted ||
            survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.PostSurvey) {
            survey_instance_cohort.post_program_survey_end_date = request.post_program_survey_end_date;
        }
        await this._survey_instance_cohortRepo.update(survey_instance_cohort, workcontext.userName);
        return survey_instance_cohort;
    }

    public async getSurveyInstanceCohortById(id: number) {
        let survey_instance = await this._survey_instance_cohortRepo.getById(id);
        survey_instance.due_date = this.dateWithoutTimezone(survey_instance.due_date);
        survey_instance.post_program_survey_start_date = this.dateWithoutTimezone(survey_instance.post_program_survey_start_date);
        survey_instance.post_program_survey_end_date = this.dateWithoutTimezone(survey_instance.post_program_survey_end_date);
        return survey_instance;
    }

    public async getSurveyInstanceCohortByIds(cohort_ids: number[]) {
        let survey_cohorts = await this._survey_instance_cohortRepo.find({
            ...activeNonDeletedQuery,
            ...{ "id": inQuery(cohort_ids) }
        });
        return survey_cohorts;
    }

    public async getAllSurveyInstanceCohorts(filter: filters, workcontext: WorkContextUser) {
        var location_codes = await this._commonService.getLocationCodeByPermission(PermissionType.SurveyInitiate, workcontext.userName);
        if (!location_codes || location_codes?.length === 0) {
            return { "data": [], "count": 0 };
        }

        var data: any;
        var total_count = 0;
        var res = [];

        if (location_codes.find(q => q?.toLowerCase() === filter.entity_name?.toLowerCase())) {
            var query = { ...nonDeletedQuery, ...{ "workflow_status": notequalQuery(WorkflowStatus.Archived) } };

            if (filter.entity_name?.toLowerCase() !== 'group') {
                query = { ...query, ...textSearchQueryLike("root_entity_code", filter.entity_name) }
            }

            if (filter.survey_master_id) {
                query = { ...query, ...{ "master_id": filter.survey_master_id } }
            }

            if (filter.page_size && filter.page_size > 0) {
                data = await this._survey_instance_cohortRepo.find(query, [['created_on', 'DESC']], filter.page_size, filter.page_size * filter.page_number);
                total_count = await this._survey_instance_cohortRepo.count(query);
            }
            else {
                data = await this._survey_instance_cohortRepo.find(query, null, null, null);
                total_count = await this._survey_instance_cohortRepo.count(query);
            }

            if (data?.length > 0) {
                for await (const resp of data) {
                    var item = new SurveyInstanceCohort();
                    item.id = resp.id;
                    item.root_entity_code = resp.root_entity_code;
                    item.root_entity_id = resp.root_entity_id;
                    item.title = resp.title;
                    item.detail = resp.detail;
                    item.due_date = this.dateWithoutTimezone(resp.due_date);
                    item.user_status = resp.user_status;
                    item.workflow_status = resp.workflow_status;
                    item.master_id = resp.master_id;
                    item.post_program_survey_start_date = this.dateWithoutTimezone(resp.post_program_survey_start_date);
                    item.post_program_survey_end_date = this.dateWithoutTimezone(resp.post_program_survey_end_date);
                    res.push(item);
                }
            }
            return { "data": res, "total_count": total_count };
        }
        return { "data": res, "total_count": total_count };
    }

    public dateWithoutTimezone = (date: Date) => {
        const tzoffset = date.getTimezoneOffset() * 60000; //offset in milliseconds
        const withoutTimezone = new Date(date.valueOf() - tzoffset)
            .toISOString()
            .slice(0, -1);
        return withoutTimezone;
    };

    public async getAllSurveyInstance(filter: filters, workcontext: WorkContextUser) {
        // var location_codes = await this._commonService.getLocationCodeByPermission(PermissionType.SurveyAccess, workcontext.userName);
        // if (!location_codes || location_codes?.length === 0) {
        //     return { "data": [], "count": 0 };
        // }

        var data: any;
        var total_count = 0;
        var res = [];

        // if (location_codes.find(q => q?.toLowerCase() === filter.entity_name?.toLowerCase())) {
        var query = { ...nonDeletedQuery, ...{ "survey_type": SurveyType.Self } };

        if (filter.cohort_id) {
            query = { ...query, ...{ "cohort_id": filter.cohort_id } }
        }

        query = { ...query, ...{ "is_post_survey": false } };

        if (filter.page_size && filter.page_size > 0) {
            data = await this._survey_instanceRepo.find(query, [['created_on', 'DESC']], filter.page_size, filter.page_size * filter.page_number);
            total_count = await this._survey_instanceRepo.count(query);
        }
        else {
            data = await this._survey_instanceRepo.find(query, null, null, null);
            total_count = await this._survey_instanceRepo.count(query);
        }

        if (data?.length > 0) {
            var unique_assign_to = _.uniq(data.map(x => x.assigned_to));
            var query1 = { ...nonDeletedQuery, ...{ "survey_type": SurveyType.Self }, ...{ "assigned_to": inQuery(unique_assign_to), ...{ "is_post_survey": true }, ...{ "cohort_id": filter.cohort_id } } };

            var post_survey_data = await this._survey_instanceRepo.find(query1, null, null, null);

            var cohort_ids = _.uniq(data.map(x => x.cohort_id));
            var cohorts = await this._survey_instance_cohortRepo.find({ ...{ "id": inQuery(cohort_ids) } });

            var grouped_user_data = _.groupBy(data, x => x.assigned_to);
            const surveyInstances = Object.keys(grouped_user_data);

            const processSurveyInstance = async (element) => {
                const pre_instance = grouped_user_data[element].find(x => !x.is_post_survey || x.is_post_survey === false);
                const post_instance = post_survey_data.find(x => x.assigned_to?.toLowerCase() === element?.toLowerCase() && (x.is_post_survey || x.is_post_survey === true));

                const item = new SurveyInstance();
                item.assigned_for = pre_instance.assigned_for;
                item.assigned_for_data = pre_instance.assigned_for_data;
                item.cohort_id = pre_instance.cohort_id;

                const cohort = cohorts?.find(x => Number(x.id) === pre_instance.cohort_id);
                item.survey_master_id = cohort ? Number(cohort.master_id) : 0;

                const createProgramDetails = async (instance) => {
                    const programDetails = new ProgrammDetails();
                    programDetails.id = instance.id;
                    programDetails.user_status = instance.user_status;
                    const userCompletedReport = await this._reportService.getUserCompletedSurvey(instance.id);
                    programDetails.total_reponses = userCompletedReport ? userCompletedReport.length : 0;
                    programDetails.show_report = userCompletedReport && userCompletedReport.length >= 5;
                    const survey_instance = await this._reportService.getUserSurveyInstances(instance.id);
                    programDetails.total_invitees = survey_instance ? survey_instance.length : 0;
                    const submitted_invitees = survey_instance?.filter((x: any) => x.workflow_status === WorkflowStatus.Submitted || x.workflow_status === WorkflowStatus.Completed || x.workflow_status === WorkflowStatus.Archived);
                    programDetails.total_submitted = submitted_invitees ? submitted_invitees.length : 0;
                    return programDetails;
                };

                item.pre_programme_detail = await createProgramDetails(pre_instance);

                if (post_instance) {
                    item.post_programme_detail = await createProgramDetails(post_instance);
                }

                return item;
            };

            const res = await Promise.all(surveyInstances.map(processSurveyInstance));
            return { "data": res, "total_count": total_count };
        }
        return { "data": res, "total_count": total_count };
    }

    public async getSurveyInstanceById(id: number) {
        let survey_instance = await this._survey_instanceRepo.getById(id);
        // var cohort = await this._survey_instance_cohortRepo.findFirst({ ...{ "id": survey_instance?.cohort_id } });
        // survey_instance['cohort_title'] = cohort ? cohort.title : "";
        return survey_instance;
    }

    public async getSurveyInstanceDetailById(id: number) {
        let survey_instance = await this._survey_instanceRepo.findFirst({ "id": id });
        var cohort = await this._survey_instance_cohortRepo.findFirst({ "id": survey_instance?.cohort_id });
        survey_instance['cohort_title'] = cohort ? cohort.title : "";
        survey_instance['survey_master_id'] = cohort ? Number(cohort.master_id) : 0;
        return survey_instance;
    }

    public async getSurveyInstanceDetails(id: number, username: any) {
        let survey_instance: any;
        if (id > 0) {
            survey_instance = await this._survey_instanceRepo.findFirst({ "id": id });
        } else {
            let user_survey = await this._survey_instanceRepo.find({
                ...activeNonDeletedQuery, ...textSearchQuery('assigned_to', username), ...textSearchQuery('assigned_for', username), ...{
                    'survey_type': SurveyType.Self
                }
            });
            user_survey = _.orderBy(user_survey, x => Number(x.id), 'desc');
            survey_instance = user_survey && user_survey.length > 0 ? user_survey[0] : null;
        }

        if (survey_instance) {
            var cohort = await this._survey_instance_cohortRepo.findFirst({ "id": survey_instance.cohort_id });
            let pre_instance: any = null;
            let post_instance: any = null;

            if (survey_instance.is_post_survey) {
                post_instance = survey_instance;
                pre_instance = await this._survey_instanceRepo.findFirst({
                    ...activeNonDeletedQuery, ...textSearchQuery('assigned_to', post_instance.assigned_for), ...textSearchQuery('assigned_for', post_instance.assigned_for), "survey_type": SurveyType.Self,
                    "is_post_survey": false, "cohort_id": post_instance.cohort_id
                });
            } else {
                pre_instance = survey_instance;
                post_instance = await this._survey_instanceRepo.findFirst({
                    ...activeNonDeletedQuery, ...textSearchQuery('assigned_to', pre_instance.assigned_for), ...textSearchQuery('assigned_for', pre_instance.assigned_for), "survey_type": SurveyType.Self,
                    "is_post_survey": true, "cohort_id": pre_instance.cohort_id
                });
            }

            const item = new SurveyInstance();
            item.assigned_for = pre_instance.assigned_for;
            item.assigned_for_data = pre_instance.assigned_for_data;
            item.assigned_to = pre_instance.assigned_to;
            item.assigned_to_data = pre_instance.assigned_to_data;
            item.cohort_id = pre_instance.cohort_id;
            item.cohort_title = cohort ? cohort.title : null;
            item.survey_master_id = cohort ? Number(cohort.master_id) : 0;
            item.is_post_survey = survey_instance.is_post_survey;

            const createProgramDetails = async (instance) => {
                const programDetails = new ProgrammDetails();
                programDetails.id = Number(instance.id);
                programDetails.user_status = instance.user_status;
                programDetails.workflow_status = instance.workflow_status;
                const userCompletedReport = await this._reportService.getUserCompletedSurvey(instance.id);
                programDetails.total_reponses = userCompletedReport ? userCompletedReport.length : 0;
                programDetails.show_report = userCompletedReport && userCompletedReport.length >= 5;
                return programDetails;
            };

            item.pre_programme_detail = await createProgramDetails(pre_instance);

            if (post_instance) {
                item.post_programme_detail = await createProgramDetails(post_instance);
            }

            return item;
        } else {
            return null;
        }
    }

    public async getSurveyMasters() {
        var query = { ...activeNonDeletedQuery };
        let survey_master = await this._survey_masterRepo.find(query, null, null, null, null);
        return survey_master;
    }

    public async initiateSurvey(request: InitiateSurvey, workcontext: WorkContextUser) {
        for await (let a of request.users) {
            if (this._commonService.isValidEmail(a.login_id)) {
                var inprogress_survey = await this.getUserInprogressSurvey(a.login_id, request.cohort_id);
                if (!inprogress_survey) {
                    // await this.markArchived(a.login_id, workcontext.userName);
                    var self_instance_id = await this.createSurveyInstance(a.login_id, a.login_id, a, a, SurveyType.Self,
                        UserStatus.InProgress, WorkflowStatus.InProgress, workcontext.userName, request.cohort_id, false, true);

                    if (a.line_manager_work_email) {
                        var linemanager = await this._employeeDataService.getEmployeesLineManager(a.line_manager_work_email);
                        if (linemanager) {
                            var linemanager_data = await this._userSearchService.SearchUserByUserNameAndEmail((linemanager.login_id ? linemanager.login_id : 'null'), (linemanager.work_email ? linemanager.work_email : 'null'));
                            if (linemanager_data != null) {
                                var lm_user = this.createUserData(linemanager);
                                lm_user.login_id = linemanager_data.userPrincipalName;
                                await this.createSurveyInstance(lm_user.login_id, a.login_id, lm_user, a, SurveyType.Manager,
                                    UserStatus.Draft, WorkflowStatus.Draft, workcontext.userName, request.cohort_id, false, false, self_instance_id);
                            }
                        }
                    }

                    if (a.person_number) {
                        var directs = await this._employeeDataService.getEmployeesDirects(a.person_number);
                        for await (let b of directs) {
                            var _direct_data = await this._userSearchService.SearchUserByUserNameAndEmail((b.login_id ? b.login_id : 'null'), (b.work_email ? b.work_email : 'null'));
                            if (_direct_data != null) {
                                var d_user = this.createUserData(b);
                                d_user.login_id = _direct_data.userPrincipalName;
                                await this.createSurveyInstance(d_user.login_id, a.login_id, d_user, a, SurveyType.Direct,
                                    UserStatus.Draft, WorkflowStatus.Draft, workcontext.userName, request.cohort_id, false, false, self_instance_id);
                            }
                        }
                    }

                    if (a.line_manager_person_number) {
                        var peers = await this._employeeDataService.getEmployeesDirects(a.line_manager_person_number);
                        peers = peers?.filter((q: any) => q.login_id !== a.login_id);
                        for await (let c of peers) {
                            var _peer_data = await this._userSearchService.SearchUserByUserNameAndEmail((c.login_id ? c.login_id : 'null'), (c.work_email ? c.work_email : 'null'));
                            if (_peer_data != null) {
                                var p_user = this.createUserData(c);
                                p_user.login_id = _peer_data.userPrincipalName;
                                await this.createSurveyInstance(p_user.login_id, a.login_id, p_user, a, SurveyType.Peer,
                                    UserStatus.Draft, WorkflowStatus.Draft, workcontext.userName, request.cohort_id, false, false, self_instance_id);
                            }
                        }
                    }
                }
            }
        };
        var url = env.baseappurl + WorkflowScreenURL.InitiatedSurvey;
        var user_names = request.users?.map(t => t.login_id);
        if (user_names?.length > 0) {
            var cohort = await this.getSurveyInstanceCohortById(request.cohort_id);
            var survey_master = await this._survey_masterRepo.getById(cohort.master_id);
            var data = new SurveyInstance();
            data.id = 1;
            data.assigned_for_data = request.users[0];
            var attachments = [];
            var attachment1 = new Attachment();
            attachment1.name = '360 Guidebook Portrait.pdf';
            attachment1.data = this._commonService.GetDocument(attachment1.name);
            attachments.push(attachment1);
            var attachment2 = new Attachment();
            attachment2.name = '360 User Manual.pdf';
            attachment2.data = this._commonService.GetDocument(attachment2.name);
            attachments.push(attachment2);
            data['SurveyMasterTitle'] = survey_master != null ? survey_master.title : "";
            data['AwarenessSessionLink'] = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EXhgzzMExhxImgVfmZqlFjABEX4EZbk-MJvRY-N4CEj-jw?e=07TCr4';

            await this._commonService.AddNotification(data, EmailTemplatesSurvey.InitiatedSurvey, user_names, EntityTypes.SurveyInstance, url, workcontext, [], attachments);
        }
        return true;
    }

    private async createSurveyInstance(assigned_to: string, assigned_for: string, assigned_to_data: any, assigned_for_data: any, survey_type: string,
        user_status: string, workflow_status: string, username: string, cohort_id: number, is_post_survey: boolean, update_last_notification_sent: boolean, master_id?: number) {
        var survey = survey_instance.build();
        survey.assigned_to = assigned_to;
        survey.assigned_for = assigned_for;
        survey.survey_type = survey_type;
        survey.user_status = user_status;
        survey.workflow_status = workflow_status;
        survey.assigned_to_data = assigned_to_data;
        survey.assigned_for_data = assigned_for_data;
        survey.cohort_id = cohort_id;
        survey.is_post_survey = is_post_survey;
        if (update_last_notification_sent) {
            survey.last_notification_sent = new Date();
        }

        if (master_id) {
            survey.master_id = master_id;
        }
        var result = await this._survey_instanceRepo.create(survey, username);
        if (!master_id) {
            var surevy_instance = await this._survey_instanceRepo.getById(Number(result.id));
            surevy_instance.master_id = result.id;
            await this._survey_instanceRepo.update(surevy_instance, username);
        }
        return result.id;
    }

    private async markArchived(assigned_for: string, user_name: string) {
        let completed_survey_instances = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', assigned_for),
            ...{ 'workflow_status': WorkflowStatus.Completed },
        });

        await Promise.all(completed_survey_instances?.map(async (item: any) => {
            var survey_instance = await this._survey_instanceRepo.getById(item.id);
            survey_instance.user_status = UserStatus.Archived;
            survey_instance.workflow_status = WorkflowStatus.Archived;
            await this._survey_instanceRepo.update(survey_instance, user_name);
        }));

        let exired_survey_instances = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', assigned_for),
            ...{ 'workflow_status': WorkflowStatus.Expired },
        });

        await Promise.all(exired_survey_instances?.map(async (item: any) => {
            var survey_instance = await this._survey_instanceRepo.getById(item.id);
            survey_instance.user_status = UserStatus.ArchivedExpired;
            survey_instance.workflow_status = WorkflowStatus.ArchivedExpired;
            await this._survey_instanceRepo.update(survey_instance, user_name);
        }));
    }

    private createUserData(user: any) {
        var user_data = {
            login_id: user.login_id,
            full_name: user.full_name,
            job_position: user.job_position,
            work_email: user.work_email,
            department: user.department,
            person_number: user.person_number,
            line_manager_work_email: user.line_manager_work_email,
            line_manager_person_number: user.line_manager_person_number
        };
        return user_data;
    }

    public async getUserInprogressSurvey(username: string, cohort_id: number) {
        if (username) {
            let user_survey = await this._survey_instanceRepo.find({
                ...activeNonDeletedQuery, ...textSearchQuery('assigned_to', username), ...{
                    'survey_type': SurveyType.Self,
                    'cohort_id': cohort_id
                },
                ...orQuery([textSearchQuery('workflow_status', WorkflowStatus.InProgress), textSearchQuery('workflow_status', WorkflowStatus.Submitted)])
            });

            return user_survey && user_survey.length > 0;
        }
        return false;
    }

    public async getSelfInprogressSurvey(workcontext: WorkContextUser) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{
                'survey_type': SurveyType.Self
            },
            ...textSearchQuery('assigned_to', workcontext.userName),
            ...orQuery([textSearchQuery('workflow_status', WorkflowStatus.InProgress), textSearchQuery('workflow_status', WorkflowStatus.Submitted), textSearchQuery('workflow_status', WorkflowStatus.Completed), textSearchQuery('workflow_status', WorkflowStatus.Expired)])
        });

        var cohort_ids = _.uniq(user_survey.map(x => x.cohort_id));
        var cohorts = await this._survey_instance_cohortRepo.find({ ...{ "id": inQuery(cohort_ids) } });

        user_survey?.forEach((item: any) => {
            var cohort = cohorts.find((x: { id: any; }) => Number(x.id) === item.cohort_id);
            item['cohort_title'] = cohort ? cohort.title : "";
            item['survey_master_id'] = cohort ? Number(cohort.master_id) : 0;
        });

        user_survey = _.orderBy(user_survey, x => Number(x.id), 'desc');

        return user_survey;
    }

    public async getSelfCompletedSurveyCount(workcontext: WorkContextUser) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{ "workflow_status": WorkflowStatus.Completed },
            ...textSearchQuery('assigned_for', workcontext.userName)
        });
        return user_survey ? user_survey.length : 0;
    }

    public async getSelfManagerSurvey(master_id: number, workcontext: WorkContextUser) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{
                'survey_type': SurveyType.Manager,
                'master_id': master_id
            },
            ...textSearchQuery('assigned_for', workcontext.userName),
            ...orQuery([textSearchQuery('workflow_status', WorkflowStatus.Draft), textSearchQuery('workflow_status', WorkflowStatus.InProgress), textSearchQuery('workflow_status', WorkflowStatus.Submitted), textSearchQuery('workflow_status', WorkflowStatus.Completed), textSearchQuery('workflow_status', WorkflowStatus.Cancelled)])
        });
        user_survey?.forEach(q => {
            q.user_status = q.user_status === UserStatus.Draft ? q.user_status : UserStatus.InProgress;
            q.workflow_status = q.workflow_status === WorkflowStatus.Draft ? q.workflow_status : WorkflowStatus.InProgress;
        });
        return user_survey;
    }

    public async getSelfDirectSurvey(master_id: number, workcontext: WorkContextUser) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{
                'survey_type': SurveyType.Direct,
                'master_id': master_id
            },
            ...textSearchQuery('assigned_for', workcontext.userName),
            ...orQuery([textSearchQuery('workflow_status', WorkflowStatus.Draft), textSearchQuery('workflow_status', WorkflowStatus.InProgress), textSearchQuery('workflow_status', WorkflowStatus.Submitted), textSearchQuery('workflow_status', WorkflowStatus.Completed), textSearchQuery('workflow_status', WorkflowStatus.Cancelled)])
        });
        user_survey?.forEach(q => {
            q.user_status = q.user_status === UserStatus.Draft ? q.user_status : UserStatus.InProgress;
            q.workflow_status = q.workflow_status === WorkflowStatus.Draft ? q.workflow_status : WorkflowStatus.InProgress;
        });
        return user_survey;
    }

    public async getSelfPeerSurvey(master_id: number, workcontext: WorkContextUser) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{
                'survey_type': SurveyType.Peer,
                'master_id': master_id
            },
            ...textSearchQuery('assigned_for', workcontext.userName),
            ...orQuery([textSearchQuery('workflow_status', WorkflowStatus.Draft), textSearchQuery('workflow_status', WorkflowStatus.InProgress), textSearchQuery('workflow_status', WorkflowStatus.Submitted), textSearchQuery('workflow_status', WorkflowStatus.Completed), textSearchQuery('workflow_status', WorkflowStatus.Cancelled)])
        });
        user_survey?.forEach(q => {
            q.user_status = q.user_status === UserStatus.Draft ? q.user_status : UserStatus.InProgress;
            q.workflow_status = q.workflow_status === WorkflowStatus.Draft ? q.workflow_status : WorkflowStatus.InProgress;
        });
        return user_survey;
    }

    public async getAssignedSurvey(workcontext: WorkContextUser) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{
                'workflow_status': WorkflowStatus.InProgress,
            },
            ...textSearchQuery('assigned_to', workcontext.userName),
            ...{ 'survey_type': notequalQuery(SurveyType.Self) }
        });
        return user_survey;
    }

    public async getSurveySectionQuestionsByType(survey_instance: any, survey_master_id: number, only_pre_post_application: boolean) {
        var section_list = new Array<SurveySection>();

        var sections = await this._survey_sectionRepo.find({ ...activeNonDeletedQuery, ...textSearchQueryLike('applicable_survey', survey_instance.survey_type) });
        var query = {
            ...activeNonDeletedQuery, ...textSearchQueryLike('applicable_survey', survey_instance.survey_type),
            ...{ 'survey_master_id': survey_master_id }
        };
        if (only_pre_post_application) {
            if (survey_instance.is_post_survey) {
                query = { ...query, ...{ 'applicable_post_survey': true } };
            } else {
                query = { ...query, ...{ 'applicable_pre_survey': true } };
            }
        }

        var questions = await this._survey_questionRepo.find(query);
        var option_list = await this._option_listRepo.find({ ...activeNonDeletedQuery });

        sections?.forEach((x: any) => {
            var _section = new SurveySection();
            _section.title = x.title;
            _section.detail = x.detail;
            _section.order_number = x.order_number;

            var section_questions = new Array<SurveyQuestion>();
            var _questions = questions?.filter((a: any) => a.section_id === x.id);
            _questions?.forEach((b: any) => {
                var _question = new SurveyQuestion();
                _question.id = b.id;
                _question.title = b.title;
                _question.detail = b.detail;
                _question.order_number = b.order_number;
                _question.question_type = b.question_type;
                _question.max_option_selection = b.max_option_selection;
                _question.option_data = option_list?.find((c: any) => c.id === b.option_list_id)?.option_data;
                section_questions.push(_question);
            });
            section_questions = _.orderBy(section_questions, w => w.order_number, 'asc');
            _section.questions = section_questions;
            if (_section.questions?.length > 0) {
                section_list.push(_section);
            }
        });
        section_list = _.orderBy(section_list, w => w.order_number, 'asc');
        return section_list;
    }

    public async getSurveySectionQuestions(master_id: number) {
        var section_list = new Array<SurveySection>();
        var sections = await this._survey_sectionRepo.find({ ...activeNonDeletedQuery });
        var questions = await this._survey_questionRepo.find({
            ...activeNonDeletedQuery, ...{ 'survey_master_id': master_id }
        });
        var option_list = await this._option_listRepo.find({ ...activeNonDeletedQuery });

        sections?.forEach((x: any) => {
            var _section = new SurveySection();
            _section.title = x.title;
            _section.detail = x.detail;
            _section.order_number = x.order_number;

            var section_questions = new Array<SurveyQuestion>();
            var _questions = questions?.filter((a: any) => a.section_id === x.id);
            _questions?.forEach((b: any) => {
                var _question = new SurveyQuestion();
                _question.id = b.id;
                _question.title = b.title;
                _question.detail = b.detail;
                _question.order_number = b.order_number;
                _question.question_type = b.question_type;
                _question.max_option_selection = b.max_option_selection;
                _question.option_data = option_list?.find((c: any) => c.id === b.option_list_id)?.option_data;
                section_questions.push(_question);
            });
            section_questions = _.orderBy(section_questions, w => w.order_number, 'asc');
            _section.questions = section_questions;
            section_list.push(_section);
        });
        section_list = _.orderBy(section_list, w => w.order_number, 'asc');
        return section_list;
    }

    public async submitSurvey(request: SurveyInstanceDetail[], survey_instance: any, workcontext: WorkContextUser) {
        for await (let a of request) {
            var data = survey_instance_detail.build();
            data.survey_instance_id = a.survey_instance_id;
            data.survey_question_id = a.survey_question_id;
            data.question_type = a.question_type;
            data.answer_data = a.answer_data;
            await this._survey_instance_detailRepo.create(data, workcontext.userName);
        };

        survey_instance.user_status = UserStatus.Submitted;
        survey_instance.workflow_status = WorkflowStatus.Submitted;
        await this._survey_instance_detailRepo.update(survey_instance, workcontext.userName);
    }

    public async sendSurvey(request: any, workcontext: WorkContextUser) {
        let valid_user = [];
        let invalid_user = [];
        if (request?.length > 0) {
            var master_data = await this._survey_instanceRepo.getById(request[0].id);
            var cohort = await this.getSurveyInstanceCohortById(master_data.cohort_id);
            if (master_data) {
                var all_instance = await this._survey_instanceRepo.find({
                    ...activeNonDeletedQuery, ...{
                        'master_id': master_data.master_id,
                    }
                });

                if (all_instance?.length > 0) {
                    for await (let x of request) {
                        var survey_instance = await this._survey_instanceRepo.getById(x.id);
                        if (survey_instance != null && survey_instance.workflow_status === WorkflowStatus.Draft
                            && survey_instance.assigned_for?.toLowerCase() === workcontext.userName?.toLowerCase()) {
                            var inprogress_user_survey = all_instance.find(w => w.assigned_to?.toLowerCase() === survey_instance.assigned_to?.toLowerCase()
                                && w.workflow_status !== WorkflowStatus.Draft);

                            if (!inprogress_user_survey) {
                                survey_instance.last_notification_sent = new Date();
                                survey_instance.user_status = UserStatus.InProgress;
                                survey_instance.workflow_status = WorkflowStatus.InProgress;
                                await this._survey_instanceRepo.update(survey_instance, workcontext.userName);

                                var url = env.baseappurl + WorkflowScreenURL.SubmitSurvey.replace('{#Id#}', survey_instance.id);
                                var attachments = [];
                                var attachment1 = new Attachment();
                                attachment1.name = '360 Guidebook Portrait.pdf';
                                attachment1.data = this._commonService.GetDocument(attachment1.name);
                                attachments.push(attachment1);
                                var attachment2 = new Attachment();
                                attachment2.name = '360 User Manual.pdf';
                                attachment2.data = this._commonService.GetDocument(attachment2.name);
                                attachments.push(attachment2);
                                survey_instance['due_date'] = cohort.due_date;
                                survey_instance['post_program_due_date'] = cohort.post_program_survey_end_date;
                                var survey_master = await this._survey_masterRepo.getById(cohort.master_id);
                                survey_instance['SurveyMasterTitle'] = survey_master != null ? survey_master.title : "";
                                survey_instance['AwarenessSessionLink'] = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EbtHHp7fWw9OvHc2zK-T3dwBoI6G2XkKN47vxW-xPefoHA?e=L1Kh4J';
                                var email_template = survey_instance.is_post_survey ? EmailTemplatesSurvey.PostSubmitSurvey : EmailTemplatesSurvey.SubmitSurvey;
                                await this._commonService.AddNotification(survey_instance, email_template, [survey_instance.assigned_to], EntityTypes.SurveyInstance, url, workcontext, [], attachments);

                                valid_user.push(survey_instance.assigned_to_data?.full_name);
                            } else {
                                invalid_user.push(inprogress_user_survey.assigned_to_data?.full_name);
                            }
                        } else {
                            invalid_user.push(survey_instance.assigned_to_data?.full_name);
                        }
                    }
                };
            }
        }
        return { valid_user: valid_user, invalid_user: invalid_user };
    }

    public async sendManagerSurvey(request: any, survey_instance: any, workcontext: WorkContextUser) {
        let valid_user = [];
        let invalid_user = [];
        var all_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{
                'master_id': survey_instance.master_id,
            }
        });
        var cohort = await this.getSurveyInstanceCohortById(survey_instance.cohort_id);

        if (all_instance?.length) {
            var inprogress_user_survey = all_instance.find(w => w.assigned_to?.toLowerCase() === request.assigned_to?.toLowerCase()
                && w.workflow_status !== WorkflowStatus.Draft);

            if (!inprogress_user_survey) {
                if (request?.assigned_to?.toLowerCase() !== survey_instance?.assigned_to?.toLowerCase()) {
                    survey_instance.assigned_to = request.assigned_to?.login_id;
                    survey_instance.assigned_to_data = request.assigned_to_data;
                }
                survey_instance.last_notification_sent = new Date();
                survey_instance.user_status = UserStatus.InProgress;
                survey_instance.workflow_status = WorkflowStatus.InProgress;
                await this._survey_instanceRepo.update(survey_instance, workcontext.userName);
                var url = env.baseappurl + WorkflowScreenURL.SubmitSurvey.replace('{#Id#}', survey_instance.id);
                var attachments = [];
                var attachment1 = new Attachment();
                attachment1.name = '360 Guidebook Portrait.pdf';
                attachment1.data = this._commonService.GetDocument(attachment1.name);
                attachments.push(attachment1);
                var attachment2 = new Attachment();
                attachment2.name = '360 User Manual.pdf';
                attachment2.data = this._commonService.GetDocument(attachment2.name);
                attachments.push(attachment2);
                survey_instance['due_date'] = cohort.due_date;
                survey_instance['post_program_due_date'] = cohort.post_program_survey_end_date;
                var survey_master = await this._survey_masterRepo.getById(cohort.master_id);
                survey_instance['SurveyMasterTitle'] = survey_master != null ? survey_master.title : "";
                survey_instance['AwarenessSessionLink'] = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EbtHHp7fWw9OvHc2zK-T3dwBoI6G2XkKN47vxW-xPefoHA?e=L1Kh4J';
                var email_template = survey_instance.is_post_survey ? EmailTemplatesSurvey.PostSubmitSurvey : EmailTemplatesSurvey.SubmitSurvey;
                await this._commonService.AddNotification(survey_instance, email_template, [survey_instance.assigned_to], EntityTypes.SurveyInstance, url, workcontext, [], attachments);

                valid_user.push(survey_instance.assigned_to_data?.full_name);
            } else {
                invalid_user.push(inprogress_user_survey.assigned_to_data?.full_name);
            }
        }

        return { valid_user: valid_user, invalid_user: invalid_user };
    }

    public async deleteSurveyInstance(id: number, workcontext: WorkContextUser) {
        await this._survey_instanceRepo.delete(id, workcontext.userName);
        return true;
    }

    public async createSurvey(request: any, workcontext: WorkContextUser) {
        let valid_user = [];
        let invalid_user = [];

        let user_survey_self = await this._survey_instanceRepo.findFirst({
            ...activeNonDeletedQuery, ...{ 'survey_type': SurveyType.Self },
            ...textSearchQuery('assigned_to', workcontext.userName),
            ...orQuery([textSearchQuery('workflow_status', WorkflowStatus.InProgress), textSearchQuery('workflow_status', WorkflowStatus.Submitted), textSearchQuery('workflow_status', WorkflowStatus.Completed)]),
            ...{ 'cohort_id': request.cohort_id }, ...{ 'is_post_survey': request.is_post_survey }
        });

        var all_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...{
                'master_id': user_survey_self.master_id,
                'cohort_id': request.cohort_id
            }
        });

        if (all_instance?.length) {
            var inprogress_user_survey = all_instance.find(w => w.assigned_to?.toLowerCase() === request.assigned_to?.toLowerCase()
                && w.workflow_status !== WorkflowStatus.Draft);

            if (user_survey_self && !inprogress_user_survey) {
                await this.createSurveyInstance(request.assigned_to,
                    user_survey_self.assigned_for,
                    request.assigned_to_data,
                    user_survey_self.assigned_for_data,
                    request.survey_type,
                    UserStatus.Draft, WorkflowStatus.Draft, workcontext.userName,
                    user_survey_self.cohort_id,
                    request.is_post_survey,
                    false,
                    user_survey_self.id);
                valid_user.push(request.assigned_to_data?.full_name);
            } else {
                invalid_user.push(inprogress_user_survey.assigned_to_data?.full_name);
            }
        }

        return { valid_user: valid_user, invalid_user: invalid_user };
    }

    public async getAllInvitees(id: number) {
        var survey_instance = await this._survey_instanceRepo.getById(id);
        if (survey_instance != null) {
            let invitees = await this._survey_instanceRepo.find({
                ...activeNonDeletedQuery, ...{ 'master_id': survey_instance.master_id },
                ...notQuery("workflow_status", WorkflowStatus.Draft)
            });

            return invitees;
        }
        return null;
    }

    public async getUserArchivedReports(workcontext: WorkContextUser) {
        let survey_instances = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', workcontext.userName),
            ...textSearchQuery('assigned_to', workcontext.userName),
            ...orQuery([textSearchQuery('workflow_status', WorkflowStatus.Archived), textSearchQuery('workflow_status', WorkflowStatus.ArchivedExpired)]),
        });

        var cohort_ids = _.uniq(survey_instances.map(x => x.cohort_id));
        var cohorts = await this._survey_instance_cohortRepo.find({ ...{ "id": inQuery(cohort_ids) } });

        var archived_reports = new Array<SurveyInstance>();
        survey_instances?.forEach((item: any) => {
            let _cohort = cohorts.find((x: { id: any; }) => x.id === item.cohort_id);
            var archived_report = new SurveyInstance();
            archived_report.id = item.id;
            archived_report.title = _cohort ? _cohort.title : "" + " Report - " + moment.utc(item.due_date).format("MMM YYYY").toLocaleString();
            archived_report.cohort_id = item.survey_master_id;
            archived_report.assigned_for_data = item.assigned_for_data;
            archived_reports.push(archived_report);
        });

        return archived_reports;
    }

    public async editSurvey(request: any, survey_instance: any, workcontext: WorkContextUser) {
        survey_instance.due_date = request.completion_date;
        await this._survey_instanceRepo.update(survey_instance, workcontext.userName);
        return true;
    }

    public async updateAssignedTo(request: any, survey_instance: any, workcontext: WorkContextUser) {
        survey_instance.assigned_to = request.assigned_to;
        survey_instance.assigned_to_data = request.assigned_to_data;
        await this._survey_instanceRepo.update(survey_instance, workcontext.userName);
        return true;
    }

    public async downloadInviteesBySurveyId(survey_id: number) {
        const excelWorkbook = new excel.Workbook();
        await excelWorkbook.xlsx.readFile(this.templatepath + '/Survey-Invitees.xlsx');

        var data_response = await this.getAllInvitees(survey_id);

        const worksheet = excelWorkbook.getWorksheet('Survey-Invitees');
        worksheet.columns = [
            { header: 'Name', key: 'user_name', width: 40 },
            { header: 'Email', key: 'user_email', width: 40 },
            { header: 'Survey Type', key: 'survey_type', width: 15 },
            { header: 'Status', key: 'user_status', width: 20 }
        ];

        var grouped_data = _.groupBy(data_response, x => x.survey_type);

        Object.keys(grouped_data).forEach((survey_type, index) => {
            var result = data_response?.filter(q => q.survey_type === survey_type);
            result?.forEach((x: any) => {
                var data = {
                    user_name: x.assigned_to_data?.full_name,
                    user_email: x.assigned_to_data?.work_email,
                    survey_type: x.survey_type,
                    user_status: (x.user_status === UserStatus.InProgress || x.user_status === UserStatus.Cancelled) ? "Not Submitted" : "Submitted"
                }
                worksheet.addRow(data);
            });
        });

        return await excelWorkbook.xlsx.writeBuffer();
    }

    public async getUserCohorts(workcontext: WorkContextUser) {
        let survey_instances = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', workcontext.userName),
            ...textSearchQuery('assigned_to', workcontext.userName),
            ...{ "survey_type": SurveyType.Self }
        });

        var cohort_ids = _.uniq(survey_instances.map(x => x.cohort_id));
        var cohorts = await this._survey_instance_cohortRepo.find({ ...{ "id": inQuery(cohort_ids) } });

        var res_cohorts = [];

        if (cohorts?.length > 0) {
            for await (const resp of cohorts) {
                var item = new SurveyInstanceCohort();
                item.id = resp.id;
                item.root_entity_code = resp.root_entity_code;
                item.root_entity_id = resp.root_entity_id;
                item.title = resp.title;
                item.detail = resp.detail;
                item.due_date = resp.due_date;
                item.user_status = resp.user_status;
                item.workflow_status = resp.workflow_status;
                item.master_id = Number(resp.master_id);
                item.self_instance = survey_instances.find(x => x.cohort_id === Number(resp.id));
                item.is_survey_inprogress = item.self_instance?.workflow_status === WorkflowStatus.InProgress;
                var user_completed_report = await this._reportService.getUserCompletedSurvey(item.self_instance.id);
                item.total_reponses = user_completed_report ? user_completed_report.length : 0;
                res_cohorts.push(item);
            }
        }

        return res_cohorts;
    }

    public async getSelfReportByCohortId(cohort_id: number, workcontext: WorkContextUser) {
        let survey_instances = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', workcontext.userName),
            ...textSearchQuery('assigned_to', workcontext.userName),
            ...{ "survey_type": SurveyType.Self },
            ...{ "cohort_id": cohort_id }
        });

        return survey_instances != null ? survey_instances[0] : null;
    }

    public async deleteParticipant(id: number, is_post_survey: boolean, workcontext: WorkContextUser) {
        let survey_instances = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...{ 'is_post_survey': is_post_survey },
            ...{ "master_id": id }
        });

        let survey_instance_ids = survey_instances.map((x: any) => x.id);
        var survey_instance_details = await this._survey_instance_detailRepo.find({ ...{ "survey_instance_id": inQuery(survey_instance_ids) } })

        for await (const survey_instance of survey_instances) {
            await this._survey_instanceRepo.delete(survey_instance.id, workcontext.userName);
        }

        for await (const survey_instance_detail of survey_instance_details) {
            await this._survey_instance_detailRepo.delete(survey_instance_detail.id, workcontext.userName);
        }

        return true;
    }

    public async deleteCohort(id: number, workcontext: WorkContextUser) {
        var cohort = await this._survey_instance_cohortRepo.getById(id);
        if (cohort != null) {
            let survey_instances = await this._survey_instanceRepo.find({
                ...activeNonDeletedQuery,
                ...{ 'cohort_id': id },
            });

            let survey_instance_ids = survey_instances.map((x: any) => x.id);
            var survey_instance_details = await this._survey_instance_detailRepo.find({ ...{ "survey_instance_id": inQuery(survey_instance_ids) } })

            for await (const survey_instance of survey_instances) {
                await this._survey_instanceRepo.delete(survey_instance.id, workcontext.userName);
            }

            for await (const survey_instance_detail of survey_instance_details) {
                await this._survey_instance_detailRepo.delete(survey_instance_detail.id, workcontext.userName);
            }

            cohort.active = false;
            cohort.deleted = true;
            await this._survey_instance_cohortRepo.update(cohort, workcontext.userName);
        }

        return true;
    }

    public async getCohotsForMove(survey_instance_cohort: any, workcontext: WorkContextUser) {
        var location_codes = await this._commonService.getLocationCodeByPermission(PermissionType.SurveyInitiate, workcontext.userName);
        if (!location_codes || location_codes?.length === 0) {
            return { "data": [], "count": 0 };
        }

        var query = {
            ...activeNonDeletedQuery,
            ...{ "master_id": survey_instance_cohort.master_id },
            ...notQuery('id', survey_instance_cohort.id)
        }

        if (survey_instance_cohort.root_entity_code !== 'GROUP') {
            query = { ...query, ...textSearchQuery('root_entity_code', survey_instance_cohort.root_entity_code) };
        }

        let survey_cohorts = await this._survey_instance_cohortRepo.find(query);

        return survey_cohorts;
    }

    public async moveParticipant(new_cohort: any, old_cohort: any, survey_instance_id: number, workcontext: WorkContextUser) {
        var query = {
            ...activeNonDeletedQuery,
            ...{ 'cohort_id': old_cohort.id },
        };

        if (survey_instance_id > 0) {
            query = { ...query, ...{ 'master_id': survey_instance_id } };
        }

        let survey_instances = await this._survey_instanceRepo.find(query);

        for await (const survey_instance of survey_instances) {
            var item = await this._survey_instanceRepo.getById(survey_instance.id);
            item.cohort_id = new_cohort.id;
            await this._survey_instanceRepo.update(item, workcontext.userName);
        }

        return true;
    }

    public async reopenSurvey(survey_instance_cohort: any, due_date: any, is_post_survey: boolean, survey_instance_id: number, workcontext: WorkContextUser) {

        var query = {
            ...activeNonDeletedQuery,
            ...{ 'cohort_id': survey_instance_cohort.id },
            ...{ 'is_post_survey': is_post_survey }
        };

        if (survey_instance_id > 0) {
            query = { ...query, ...{ 'master_id': survey_instance_id } };
        }

        let survey_instances = await this._survey_instanceRepo.find(query);

        // self expired to in-progress
        var self_exipred = survey_instances.find((x: any) => x.survey_type === SurveyType.Self && x.workflow_status === WorkflowStatus.Expired);
        if (self_exipred) {
            var item = await this._survey_instanceRepo.getById(self_exipred.id);
            item.user_status = UserStatus.InProgress;
            item.workflow_status = WorkflowStatus.InProgress;
            await this._survey_instanceRepo.update(item, workcontext.userName);
        }

        //all cancelled to in-progress
        var all_cancelled = survey_instances.filter((x: any) => x.workflow_status === WorkflowStatus.Cancelled);
        for await (const survey_instance of all_cancelled) {
            var item = await this._survey_instanceRepo.getById(survey_instance.id);
            item.user_status = UserStatus.InProgress;
            item.workflow_status = WorkflowStatus.InProgress;
            await this._survey_instanceRepo.update(item, workcontext.userName);
        }

        //all completed to submitted
        var all_completed = survey_instances.filter((x: any) => x.workflow_status === WorkflowStatus.Completed);
        for await (const survey_instance1 of all_completed) {
            var item = await this._survey_instanceRepo.getById(survey_instance1.id);
            item.user_status = UserStatus.Submitted;
            item.workflow_status = WorkflowStatus.Submitted;
            await this._survey_instanceRepo.update(item, workcontext.userName);
        }

        //update cohort status
        var cohort = await this._survey_instance_cohortRepo.getById(survey_instance_cohort.id);
        if (is_post_survey) {
            cohort.user_status = UserStatus_Cohort.PostSurvey;
            cohort.workflow_status = WorkflowStatus_Cohort.PostSurvey;
            cohort.post_program_survey_end_date = due_date;
        } else {
            cohort.user_status = UserStatus_Cohort.Open;
            cohort.workflow_status = WorkflowStatus_Cohort.Open;
            cohort.due_date = due_date;
        }
        await this._survey_instance_cohortRepo.update(cohort, workcontext.userName);

        return true;
    }

    //Scheduler Services

    public async SurveyJob() {
        await this.SendReminderNotification();
        await this.completePreSurveys();
        await this.createPostSurveyInstance();
        await this.completePostSurveys();
    }

    public async completePreSurveys() {
        let deadline_completed_cohorts = await this._survey_instance_cohortRepo.find({
            ...activeNonDeletedQuery, ...{
                'workflow_status': WorkflowStatus_Cohort.Open,
            },
            ...{ 'due_date': lessQuery((new Date().toISOString().split('T')[0]) + 'T00:00:00.000Z') }
        });

        deadline_completed_cohorts?.forEach(s => {
            s.due_date = this.dateWithoutTimezone(s.due_date);
            s.post_program_survey_start_date = this.dateWithoutTimezone(s.post_program_survey_start_date);
            s.post_program_survey_end_date = this.dateWithoutTimezone(s.post_program_survey_end_date);
        });


        for await (const cohort of deadline_completed_cohorts) {
            let all_surveys = await this._survey_instanceRepo.find({
                ...activeNonDeletedQuery,
                ...{ 'cohort_id': cohort.id },
                ...{ 'is_post_survey': false }
            });

            let submitted_surveys = all_surveys.filter((x: any) => x.workflow_status === WorkflowStatus.Submitted);

            for await (const submitted_survey of submitted_surveys) {
                var survey_instance = await this._survey_instanceRepo.getById(Number(submitted_survey.id));
                survey_instance.user_status = UserStatus.Completed;
                survey_instance.workflow_status = WorkflowStatus.Completed;
                await this._survey_instanceRepo.update(survey_instance, 'System');
            }

            await this.cancelSurveyReports(all_surveys);

            var self_submitted_reports = submitted_surveys.filter((x: any) => x.survey_type === SurveyType.Self);
            for await (const self_submitted_report of self_submitted_reports) {
                var survey_master = await this._survey_masterRepo.getById(Number(cohort.master_id));
                var data = new SurveyInstance();
                data.id = self_submitted_report.id;
                data.assigned_for_data = self_submitted_report.assigned_for_data;
                data.due_date = cohort != null ? cohort.due_date : null;
                data.post_program_due_date = cohort != null ? cohort.post_program_survey_end_date : null;
                var attachments = [];
                var attachment1 = new Attachment();
                attachment1.name = '360 Guidebook Portrait.pdf';
                attachment1.data = this._commonService.GetDocument(attachment1.name);
                attachments.push(attachment1);
                data['SurveyMasterTitle'] = survey_master != null ? survey_master.title : "";
                data['AwarenessSessionLink'] = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EbtHHp7fWw9OvHc2zK-T3dwBoI6G2XkKN47vxW-xPefoHA?e=L1Kh4J';

                var url = "";
                if (Number(cohort.master_id) === 2) {
                    url = env.baseappurl + WorkflowScreenURL.UserReport.replace('{#Id#}', self_submitted_report.id);
                } else {
                    url = env.baseappurl + WorkflowScreenURL.UserReportWay2.replace('{#Id#}', self_submitted_report.id);
                }

                const userCompletedReport = await this._reportService.getUserCompletedSurvey(self_submitted_report.id);
                try {
                    if (userCompletedReport?.length >= 5) {
                        await this._commonService.AddNotification(data, EmailTemplatesSurvey.SurveyFinalReport, [self_submitted_report.assigned_for], EntityTypes.SurveyInstance, url, null, [], attachments);
                    } else {
                        await this._commonService.AddNotification(data, EmailTemplatesSurvey.SurveyReportNotGenerated, [self_submitted_report.assigned_for], EntityTypes.SurveyInstance, url, null, [], attachments);
                    }
                }
                catch (ex) {
                    this._logger.error(ex, ex);
                }

            }

            var _cohort = await this._survey_instance_cohortRepo.getById(cohort.id);
            _cohort.user_status = UserStatus_Cohort.SurveyCompleted;
            _cohort.workflow_status = WorkflowStatus_Cohort.SurveyCompleted;
            await this._survey_instance_cohortRepo.update(_cohort, 'System');
        }
    }

    public async cancelSurveyReports(all_surveys: any) {
        let self_inprogress_surveys = all_surveys.filter((x: any) => x.survey_type === SurveyType.Self &&
            (x.workflow_status === WorkflowStatus.Draft || x.workflow_status === WorkflowStatus.InProgress));

        for await (const item of self_inprogress_surveys) {
            var survey_instance = await this._survey_instanceRepo.getById(Number(item.id));
            survey_instance.user_status = UserStatus.Expired;
            survey_instance.workflow_status = WorkflowStatus.Expired;
            await this._survey_instanceRepo.update(survey_instance, 'System');
        }

        let other_inprogress_surveys = all_surveys.filter((x: any) => x.survey_type !== SurveyType.Self &&
            x.workflow_status === WorkflowStatus.InProgress);

        for await (const item of other_inprogress_surveys) {
            var survey_instance = await this._survey_instanceRepo.getById(Number(item.id));
            survey_instance.user_status = UserStatus.Cancelled;
            survey_instance.workflow_status = WorkflowStatus.Cancelled;
            await this._survey_instanceRepo.update(survey_instance, 'System');
        }

        let other_draft_surveys = all_surveys.filter((x: any) => x.survey_type !== SurveyType.Self &&
            x.workflow_status === WorkflowStatus.Draft);

        for await (const item of other_draft_surveys) {
            var survey_instance = await this._survey_instanceRepo.getById(Number(item.id));
            survey_instance.active = false;
            survey_instance.deleted = true;
            await this._survey_instanceRepo.update(survey_instance, 'System');
        }
    }

    public async createPostSurveyInstance() {
        var cohorts = await this._survey_instance_cohortRepo.find({
            ...activeNonDeletedQuery,
            ...textSearchQuery('workflow_status', WorkflowStatus.Completed),
            ...{ 'post_program_survey_start_date': lessOrequalQuery((new Date().toISOString().split('T')[0]) + 'T00:00:00.000Z') }
        });

        cohorts?.forEach(s => {
            s.due_date = this.dateWithoutTimezone(s.due_date);
            s.post_program_survey_start_date = this.dateWithoutTimezone(s.post_program_survey_start_date);
            s.post_program_survey_end_date = this.dateWithoutTimezone(s.post_program_survey_end_date);
        });

        var cohort_ids = cohorts.map((x: any) => x.id);

        let all_cohort_self_pre_surveys = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...{ "survey_type": SurveyType.Self },
            ...textSearchQuery('workflow_status', WorkflowStatus.Completed),
            ...{ 'is_post_survey': false },
            ...{ "cohort_id": inQuery(cohort_ids) }
        });

        for await (const cohort of cohorts) {
            var survey_masters = await this._survey_masterRepo.find({ ...activeNonDeletedQuery });
            var self_pre_surveys = all_cohort_self_pre_surveys.filter(e => e.cohort_id === Number(cohort.id));

            for await (const self_pre_survey of self_pre_surveys) {
                var self_post_survey_id = await this.createSurveyInstance(self_pre_survey.assigned_to, self_pre_survey.assigned_for, self_pre_survey.assigned_to_data, self_pre_survey.assigned_for_data, SurveyType.Self,
                    UserStatus.InProgress, WorkflowStatus.InProgress, 'System', self_pre_survey.cohort_id, true, true);

                let all_other_pre_surveys = await this._survey_instanceRepo.find({
                    ...activeNonDeletedQuery,
                    ...{ "survey_type": notequalQuery(SurveyType.Self) },
                    ...{ 'is_post_survey': false },
                    ...{ "master_id": self_pre_survey.id }
                });

                for await (const all_other_pre_survey of all_other_pre_surveys) {
                    await this.createSurveyInstance(all_other_pre_survey.assigned_to, all_other_pre_survey.assigned_for, all_other_pre_survey.assigned_to_data, all_other_pre_survey.assigned_for_data, all_other_pre_survey.survey_type,
                        UserStatus.Draft, WorkflowStatus.Draft, 'System', all_other_pre_survey.cohort_id, true, false, self_post_survey_id);
                }

                //Send notification to self
                var survey_master = survey_masters.find((q: any) => Number(q.id) === Number(cohort.master_id));
                var data = new SurveyInstance();
                data.id = self_post_survey_id;
                data.assigned_for_data = self_pre_survey.assigned_for_data;
                data.due_date = cohort != null ? cohort.due_date : null;
                data.post_program_due_date = cohort != null ? cohort.post_program_survey_end_date : null;
                var attachments = [];
                var attachment1 = new Attachment();
                attachment1.name = '360 Guidebook Portrait.pdf';
                attachment1.data = this._commonService.GetDocument(attachment1.name);
                attachments.push(attachment1);
                var attachment2 = new Attachment();
                attachment2.name = '360 User Manual.pdf';
                attachment2.data = this._commonService.GetDocument(attachment2.name);
                attachments.push(attachment2);
                data['SurveyMasterTitle'] = survey_master != null ? survey_master.title : "";
                data['AwarenessSessionLink'] = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EXhgzzMExhxImgVfmZqlFjABEX4EZbk-MJvRY-N4CEj-jw?e=07TCr4';
                var url = env.baseappurl + WorkflowScreenURL.SubmitSurvey.replace('{#Id#}', self_post_survey_id);

                try {
                    await this._commonService.AddNotification(data, EmailTemplatesSurvey.PostInitiatedSurvey, [self_pre_survey.assigned_for], EntityTypes.SurveyInstance, url, null, [], attachments);
                }
                catch (ex) {
                    this._logger.error(ex, ex);
                }

            }

            var _cohort = await this._survey_instance_cohortRepo.getById(cohort.id);
            _cohort.user_status = UserStatus_Cohort.PostSurvey;
            _cohort.workflow_status = WorkflowStatus_Cohort.PostSurvey;
            await this._survey_instance_cohortRepo.update(_cohort, 'System');
        }
    }

    public async completePostSurveys() {
        let deadline_completed_cohorts = await this._survey_instance_cohortRepo.find({
            ...activeNonDeletedQuery, ...{ 'workflow_status': WorkflowStatus_Cohort.PostSurvey },
            ...{ 'post_program_survey_end_date': lessQuery((new Date().toISOString().split('T')[0]) + 'T00:00:00.000Z') }
        });

        deadline_completed_cohorts?.forEach(s => {
            s.due_date = this.dateWithoutTimezone(s.due_date);
            s.post_program_survey_start_date = this.dateWithoutTimezone(s.post_program_survey_start_date);
            s.post_program_survey_end_date = this.dateWithoutTimezone(s.post_program_survey_end_date);
        });

        for await (const cohort of deadline_completed_cohorts) {
            let all_surveys = await this._survey_instanceRepo.find({
                ...activeNonDeletedQuery,
                ...{ 'cohort_id': cohort.id },
                ...{ 'is_post_survey': true }
            });

            let submitted_surveys = all_surveys.filter((x: any) => x.workflow_status === WorkflowStatus.Submitted);

            for await (const submitted_survey of submitted_surveys) {
                var survey_instance = await this._survey_instanceRepo.getById(Number(submitted_survey.id));
                survey_instance.user_status = UserStatus.Completed;
                survey_instance.workflow_status = WorkflowStatus.Completed;
                await this._survey_instanceRepo.update(survey_instance, 'System');
            }

            await this.cancelSurveyReports(all_surveys);

            var self_submitted_reports = submitted_surveys.filter((x: any) => x.survey_type === SurveyType.Self);
            for await (const self_submitted_report of self_submitted_reports) {
                var survey_master = await this._survey_masterRepo.getById(Number(cohort.master_id));
                var data = new SurveyInstance();
                data.id = self_submitted_report.id;
                data.assigned_for_data = self_submitted_report.assigned_for_data;
                data.due_date = cohort != null ? cohort.due_date : null;
                data.post_program_due_date = cohort != null ? cohort.post_program_survey_end_date : null;
                var attachments = [];
                var attachment1 = new Attachment();
                attachment1.name = '360 Guidebook Portrait.pdf';
                attachment1.data = this._commonService.GetDocument(attachment1.name);
                attachments.push(attachment1);
                data['SurveyMasterTitle'] = survey_master != null ? survey_master.title : "";
                data['AwarenessSessionLink'] = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EbtHHp7fWw9OvHc2zK-T3dwBoI6G2XkKN47vxW-xPefoHA?e=L1Kh4J';

                var url = "";
                if (Number(cohort.master_id) === 2) {
                    url = env.baseappurl + WorkflowScreenURL.UserReport.replace('{#Id#}', self_submitted_report.id);
                } else {
                    url = env.baseappurl + WorkflowScreenURL.UserReportWay2.replace('{#Id#}', self_submitted_report.id);
                }

                const userCompletedReport = await this._reportService.getUserCompletedSurvey(self_submitted_report.id);

                try {
                    if (userCompletedReport?.length >= 5) {
                        await this._commonService.AddNotification(data, EmailTemplatesSurvey.PostSurveyFinalReport, [self_submitted_report.assigned_for], EntityTypes.SurveyInstance, url, null, [], attachments);
                    } else {
                        await this._commonService.AddNotification(data, EmailTemplatesSurvey.PostSurveyReportNotGenerated, [self_submitted_report.assigned_for], EntityTypes.SurveyInstance, url, null, [], attachments);
                    }
                }
                catch (ex) {
                    this._logger.error(ex, ex);
                }
            }

            var _cohort = await this._survey_instance_cohortRepo.getById(cohort.id);
            _cohort.user_status = UserStatus_Cohort.Closed;
            _cohort.workflow_status = WorkflowStatus_Cohort.Closed;
            await this._survey_instance_cohortRepo.update(_cohort, 'System');
        }
    }

    public async SendReminderNotification() {

        var cohorts = await this._survey_instance_cohortRepo.find({ ...activeNonDeletedQuery });

        cohorts?.forEach(w => {
            w.due_date = this.dateWithoutTimezone(w.due_date);
            w.post_program_survey_start_date = this.dateWithoutTimezone(w.post_program_survey_start_date);
            w.post_program_survey_end_date = this.dateWithoutTimezone(w.post_program_survey_end_date);
        });

        let all_surveys = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...textSearchQuery('workflow_status', WorkflowStatus.InProgress),
        });

        for await (const item of all_surveys) {
            if (item.last_notification_sent == null || this.addDaysToDate(item.last_notification_sent, 3) <= new Date()) {
                try {
                    var cohort = cohorts.find((x: any) => Number(x.id) === item.cohort_id);
                    var survey_master = await this._survey_masterRepo.getById(Number(cohort.master_id));
                    var data = new SurveyInstance();
                    data.id = item.id;
                    data.assigned_for_data = item.assigned_for_data;
                    data.due_date = cohort != null ? cohort.due_date : null;
                    data.post_program_due_date = cohort != null ? cohort.post_program_survey_end_date : null;
                    var attachments = [];
                    var attachment1 = new Attachment();
                    attachment1.name = '360 Guidebook Portrait.pdf';
                    attachment1.data = this._commonService.GetDocument(attachment1.name);
                    attachments.push(attachment1);
                    data['SurveyMasterTitle'] = survey_master != null ? survey_master.title : "";
                    data['AwarenessSessionLink'] = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EXhgzzMExhxImgVfmZqlFjABEX4EZbk-MJvRY-N4CEj-jw?e=07TCr4';
                    var url = env.baseappurl + WorkflowScreenURL.SubmitSurvey.replace('{#Id#}', item.id);
                    var email_template = item.is_post_survey ? EmailTemplatesSurvey.PostSurveyReminder : EmailTemplatesSurvey.SurveyReminder;
                    await this._commonService.AddNotification(data, email_template, [item.assigned_to], EntityTypes.SurveyInstance, url, null, [], attachments);

                    var survey_instance = await this._survey_instanceRepo.getById(item.id);
                    survey_instance.last_notification_sent = new Date();
                    await this._survey_instanceRepo.update(survey_instance, 'System');
                }
                catch (ex) {
                    this._logger.error(ex, ex);
                }
            }
        }
    }

    private addDaysToDate(originalDate: Date, daysToAdd: number): Date {
        if (originalDate == null) {
            originalDate = new Date();
        }
        const newDate = new Date(originalDate);
        newDate.setDate(originalDate.getDate() + daysToAdd);
        return newDate;
    }
}