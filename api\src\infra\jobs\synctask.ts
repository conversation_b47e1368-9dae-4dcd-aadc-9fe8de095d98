import { SurveyService } from "../../services/survey.service";
import Database from "../../infra/database";
import { SurveyServiceExec } from "../../services/exec360/survey_exec.service";

export class SyncTask {
    _database: Database;
    _surveyService: SurveyService;
    _surveyServiceExec: SurveyServiceExec;

    constructor(database: Database, surveyService: SurveyService, surveyServiceExec: SurveyServiceExec) {
        this._database = database;
        this._surveyService = surveyService;
        this._surveyServiceExec = surveyServiceExec;
    }

    public async start() {
        await this._database.connect();
        await this.surveyJob();
    }

    private async surveyJob() {
        await this._surveyService.SurveyJob();
        await this._surveyServiceExec.ExecSurveyJob();
    }
}
