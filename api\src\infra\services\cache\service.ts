import _ from "lodash";
import { CacheManager } from "../../cachemanager";
import { LoggerService } from "../loggerservice";
import { CacheStartupService } from "./populate";

export class CacheService {
    _logger: LoggerService;
    _cacheManager: CacheManager;
    _cacheStartup: CacheStartupService;

    constructor(logger: LoggerService, cacheManager: CacheManager, cacheStartup: CacheStartupService) {
        this._logger = logger;
        this._cacheManager = cacheManager;
        this._cacheStartup = cacheStartup;
    }

    public async Startup() {
        this.ClearAllCache();
        this._cacheStartup.PopulateCache();
    }

    public async ClearAllCache() {
        this._cacheManager.clearAll();
    }



}

