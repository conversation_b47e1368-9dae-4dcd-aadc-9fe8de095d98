import * as Sequelize from 'sequelize';
import { DataTypes } from 'sequelize';
import { env } from '../../infra/config';
import { AppModel } from '../base/AppModel';


export type survey_instance_detailPk = "id";
export type survey_instance_detailId = survey_instance_detail[survey_instance_detailPk];

export class survey_instance_detail extends AppModel {
  id!: number;
  survey_instance_id?: number;
  survey_question_id?: number;
  question_type?: string;
  answer_data?: object;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;


  static initModel(sequelize: Sequelize.Sequelize): typeof survey_instance_detail {
    this.HasSoftDelete = true;
    survey_instance_detail.init({
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    survey_instance_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    survey_question_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    question_type: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    answer_data: {
      type: DataTypes.JSON,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'survey_instance_detail',
    schema: env.database.schema,
    timestamps: false,
    indexes: [
      {
        name: "survey_instance_detail_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return survey_instance_detail;
  }
}
