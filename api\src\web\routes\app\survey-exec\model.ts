export class User<PERSON><PERSON>vey {
    username: string;
    cohort_id: number;
}

export class InitiateExecSurvey {
    id!: number;
    users: UserParticipants;
    division: string;
    region: string;
    due_date: Date;
    survey_master_id: number;
    applicable_principles: any;
    reminder_frequency_days: number;
    business_unit: string;
    country: string;
    user_preferred_lang: string;
}

export class UserParticipants {
    self: any;
    manager: any = [];
    direct: any = [];
    peer: any = [];
    other: any = [];
}

export class filters {
    search_text: string;
    page_number: number;
    page_size: number;
    entity_name: string;
    user_status: string;
    division: string;
    region: string;
    survey_instance_ids: number[];
    survey_master_id: number;
    lang_code: string;
    user_name: string;
    business_unit: string;
}

export class SurveyInstanceExec {
    id: number;
    due_date: string;
    user_status: string;
    assigned_for: string;
    assigned_to: string;
    assigned_for_data: any;
    assigned_to_data: any;
    reminder_frequency_days: number;
    master_id: number;
    applicable_principles: string;
    division: string;
    region: string;
    business_unit: string;
    country: string;
}

export class SurveyInstanceExecListing extends SurveyInstanceExec {
    total_reponses: number;
    show_report: boolean;
    total_submitted: number;
    total_invitees: number;
    user_preferred_lang: string
}

export class QuestionRatingScale {
    prompt_question: string;
    description: string;
    enablers: SurveyItem[];
}

export class SurveyCategory {
    principle: string;
    effective: QuestionRatingScale;
    ineffective: QuestionRatingScale;
}

export class SurveyItem {
    title: string;
    questions: SurveyQuestion[];
}

export class SurveyQuestion {
    id: number;
    title: string;
    detail: string;
    question_type: string;
    order_number: number;
    rating_scale: object;
}

export class SurveyInstanceDetail {
    survey_question_id: number = null;
    answer_data?: any[] = [];
}

export class Attachment {
    name: string;
    data: Buffer;
}

export class NotificationData {
    id: number;
    participant_name: string;
    awareness_session_link: string;
    rater_name: string;
    due_date: string;
    is_external: boolean = false;
    rater_email: string;
    guide_book_link: string;
    survey_type: string;
}