import * as axios from 'axios'
import { env } from '../../config';

const tenantId = env.tennantId;
const applicationId = env.applicationId;
const notificationserviceurl = env.notificationserviceurl;

const notificationApiGet = async (path: string) => {
    const options: axios.AxiosRequestConfig = {
        method: 'GET',
        headers: { 'tennantId': tenantId, 'applicationid': applicationId, 'Accept': "text/html, application/json" }
    };

    var res = await axios.default.get(`${notificationserviceurl}${path}`, options);
    return res.data;
}

const notificationApiPost = async (path: string, data: any) => {
    const options: axios.AxiosRequestConfig = {
        method: 'POST',
        headers: { 'tennantId': tenantId, 'applicationid': applicationId, 'Accept': "text/html, application/json" },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
    };

    var res = await axios.default.post(`${notificationserviceurl}${path}`, data, options);
    return res.data;
}

export {
    notificationApiGet,
    notificationApiPost
};
