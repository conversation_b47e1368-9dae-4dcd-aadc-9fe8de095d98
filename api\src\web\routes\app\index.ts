import express, { Application, Request, Response } from 'express';
import authentication from '../../../infra/authentication';
import * as commonRouter from './common';
import * as profileRouter from './profile';
import * as surveyRouter from './survey';
import * as reportRouter from './report';
import * as surveyExecRouter from './survey-exec';
import * as reportExecRouter from './report-exec';

export function register(app: Application) {
    const router = express.Router();

    router.get('/', function (req, res) {
        res.send("App App API is up and running.");
    });

    authentication(router);
    router.use('/profile', profileRouter.register(router));
    router.use('/common', commonRouter.register(router));
    router.use('/survey', surveyRouter.register(router));
    router.use('/report', reportRouter.register(router));

    router.use('/survey-exec', surveyExecRouter.register(router));
    router.use('/report-exec', reportExecRouter.register(router));

    app.use('/api', router);
}