import { survey_instance_detail } from "../../../domain/entities/survey_instance_detail";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { isurvey_instance_detailRepo } from "../../../domain/interfaces/repo/isurvey_instance_detail.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class survey_instance_detailRepo extends BaseRepository<survey_instance_detail> implements isurvey_instance_detailRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_instance_detail);
    }
}