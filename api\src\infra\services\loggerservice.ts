import { createLogger, format, Logger, transports } from 'winston';
import  'winston-daily-rotate-file';
import { Config } from '../config/model';

export class LoggerService {
    _logger: Logger;
    constructor(config: Config) {
      this._logger = createLogger({
        transports:
            [
                new transports.DailyRotateFile({
                    filename: 'logs/application-%DATE%.log',
                    format:format.combine(
                        format.timestamp({format: 'MMM-DD-YYYY HH:mm:ss'}),
                        format.align(),
                        format.printf(info => `${info.level}: ${[info.timestamp]}: ${info.message}`),
                    ),
                    level: config.log.level,
                    datePattern: 'YYYY-MM-DD-HH',
                    zippedArchive: true,
                    maxSize: '20m',
                    maxFiles: '30d'           
                  }),
                new transports.Console({ level: 'info' })      
            ]
        });
  }
  public async info(message: any): Promise<void>;
  public async info(nmessage: any, obj: any):  Promise<void>;
  
  public async info(message: any, obj?: any) {
    if (obj) {
      this._logger.log('info', message, {
        obj
      });
    }
      else {
        this._logger.log('info', message);
      }
  }

  public async debug(message: any): Promise<void>;
  public async debug(nmessage: any, obj: any):  Promise<void>;
  
  public async debug(message: any, obj?: any) {
    if (obj) {
      this._logger.log('debug', message, {
        obj
      });
    }
      else {
        this._logger.log('debug', message);
      }
  }

  public async error(message: any): Promise<void>;
  public async error(nmessage: any, obj: any):  Promise<void>;
  
  public async error(message: any, obj?: any) {
    if (obj) {
      this._logger.log('error', message, {
        obj
      });
    }
      else {
        this._logger.log('error', message);
      }
  }
}



