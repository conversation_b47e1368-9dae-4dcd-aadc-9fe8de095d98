import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../infra/config';
import { AppModel } from '../base/AppModel';


export type survey_instance_cohortPk = "id";
export type survey_instance_cohortId = survey_instance_cohort[survey_instance_cohortPk];

export class survey_instance_cohort extends AppModel {
  id!: number;
  root_entity_code?: string;
  root_entity_id?: number;
  title?: string;
  detail?: string;
  due_date?: Date;
  post_program_survey_start_date?: Date;
  post_program_survey_end_date?: Date;
  user_status?: string;
  workflow_status?: string;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;
  master_id?: number;

  static initModel(sequelize: Sequelize.Sequelize): typeof survey_instance_cohort {
    survey_instance_cohort.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    root_entity_code: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    root_entity_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: true
    },   
    detail: {
      type: DataTypes.STRING(4000),
      allowNull: true
    },
    due_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    post_program_survey_start_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    post_program_survey_end_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    user_status: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    workflow_status: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    master_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'survey_instance_cohort',
    schema: env.database.schema,
    timestamps: false,
    indexes: [
      {
        name: "survey_instance_cohort_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return survey_instance_cohort;
  }
}
