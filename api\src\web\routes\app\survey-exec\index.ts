import express, { Router } from "express";
import { container } from "../../../../container";
import { ResponseController } from "../../../../shared/utils/ResponseController";
import {
  PermissionType,
  SurveyType,
  SurveyTypeExec,
  WorkflowStatus,
  WorkflowStatusExec,
  WorkflowStatus_Cohort
} from "../../../../shared/enum/general";
import { HasPermission } from "../../../validators/app.validators.exec";
import { WorkContext } from "../../../../shared/context/workcontext";
import { CommonService } from "../../../../services/common.service";
import _ from "lodash";
import { SurveyServiceExec } from "../../../../services/exec360/survey_exec.service";
import {
  InitiateExecSurvey,
  SurveyInstanceDetail,
  UserParticipants,
  UserSurvey,
  filters
} from "./model";
import { truncate } from "fs/promises";
import { env } from "../../../../infra/config";

export function register(router: Router) {
  var surveyServiceExec: SurveyServiceExec =
    container.resolve("surveyServiceExec");
  var commonService: CommonService = container.resolve("commonService");

  const innerRouter = express.Router();

  innerRouter.post("/getAllBusinessEntity", async function (req, res, next) {
    try {
      var workcontext = req.user as WorkContext;
      var resp = await commonService.getallbusinessentity(
        workcontext.currentUser.userName,
        req.body.permission
      );
      return ResponseController.ok(res, resp);
    } catch (error) {
      next(error);
    }
  });

  innerRouter.get("/getSurveyMasters", async function (req, res, next) {
    try {
      var survey_master = await surveyServiceExec.getSurveyMasters();
      return ResponseController.ok(res, survey_master);
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.post(
    "/getUserInprogressSurvey",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        const request: UserSurvey = req.body;
        var resp = await surveyServiceExec.getUserInprogressSurvey(
          request.username
        );
        return ResponseController.ok(res, { valid: resp });
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/getUserParticipants",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        const request: UserSurvey = req.body;
        var resp = await surveyServiceExec.getUserParticipants(
          request.username
        );
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/getAllSurveyInstances",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        const request: filters = req.body;
        var workContext = (req.user as WorkContext).currentUser;
        var result = await surveyServiceExec.getAllSurveyInstances(
          request,
          workContext
        );
        var resp = {
          results: result.data,
          total_count: result.total_count
        };
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/initiateSurvey",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        const request: InitiateExecSurvey = req.body;
        var workContext = (req.user as WorkContext).currentUser;
        var resp = await surveyServiceExec.initiateSurvey(request, workContext);
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/getSurveyInstanceDetails/",
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        const request: any = req.body;
        var survey_instances = await surveyServiceExec.getSurveyInstanceDetails(
          Number(request.instance_id),
          workContext.userName
        );
        var self_instance = survey_instances
          ? survey_instances.find((x) => x.survey_type === SurveyTypeExec.Self)
          : null;
        if (
          survey_instances != null &&
          self_instance != null &&
          (await commonService.hasSurveyAccess(
            PermissionType.SurveyAccessExec,
            workContext.userName,
            self_instance
          ))
        ) {
          return ResponseController.ok(res, survey_instances);
        } else {
          return ResponseController.ok(res, []);
        }
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post("/createSurveyInstance", async function (req, res, next) {
    try {
      var workContext = (req.user as WorkContext).currentUser;
      const request: any = req.body;
      var self_instance = await surveyServiceExec.getSurveyInstanceById(
        Number(request.self_instance_id)
      );
      if (
        commonService.isValidEmail(request.assigned_to) &&
        self_instance != null &&
        (await commonService.hasSurveyAccess(
          PermissionType.SurveyAccessExec,
          workContext.userName,
          self_instance
        ))
      ) {
        var result = await surveyServiceExec.createSurvey(
          request,
          self_instance,
          workContext
        );
        return ResponseController.ok(res, result);
      } else {
        return ResponseController.ok(res, false);
      }
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.get(
    "/deleteSurveyInstance/:instanceid",
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(
          Number(req.params.instanceid)
        );
        if (
          survey_instance &&
          ((await commonService.hasSurveyAccess(
            PermissionType.SurveyAccessExec,
            workContext.userName,
            survey_instance
          )) ||
            survey_instance.assigned_for?.toLowerCase() ===
              workContext.userName?.toLowerCase())
        ) {
          await surveyServiceExec.deleteSurveyInstance(
            survey_instance.id,
            workContext
          );
          return ResponseController.ok(res, true);
        } else {
          return ResponseController.unauthorized(res);
        }
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post("/sendSurvey", async function (req, res, next) {
    try {
      var workContext = (req.user as WorkContext).currentUser;
      const request: any[] = req.body;
      var survey_instance_self =
        request.length > 0
          ? await surveyServiceExec.getSurveyInstanceById(
              Number(request[0].master_id)
            )
          : null;

      var survey_instances = await surveyServiceExec.getSurveyInstanceByIds(
        request.map((x) => x.id)
      );
      var any_submitted_survey =
        survey_instances.filter(
          (x) => x.workflow_status !== WorkflowStatusExec.Draft
        )?.length > 0;

      if (
        !any_submitted_survey &&
        survey_instance_self != null &&
        ((await commonService.hasSurveyAccess(
          PermissionType.SurveyAccessExec,
          workContext.userName,
          survey_instance_self
        )) ||
          survey_instance_self.assigned_for?.toLowerCase() ===
            workContext.userName?.toLowerCase())
      ) {
        var result = await surveyServiceExec.sendSurvey(
          request,
          survey_instance_self,
          workContext
        );
        return ResponseController.ok(res, result);
      } else {
        return ResponseController.unauthorized(res);
      }
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.get("/getAssignedSurvey", async function (req, res, next) {
    try {
      var workContext = (req.user as WorkContext).currentUser;
      var resp = await surveyServiceExec.getAssignedSurvey(workContext);
      return ResponseController.ok(res, resp);
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.post("/reopenSurvey", async function (req, res, next) {
    try {
      var workContext = (req.user as WorkContext).currentUser;
      const request: any = req.body;
      var survey_instance = await surveyServiceExec.getSurveyInstanceById(
        Number(request.survey_instance_id)
      );
      if (
        survey_instance != null &&
        (survey_instance.workflow_status === WorkflowStatusExec.Completed ||
          survey_instance.workflow_status ===
            WorkflowStatusExec.Pending_Validation ||
          survey_instance.workflow_status ===
            WorkflowStatusExec.CompletedReportReady ||
          survey_instance.workflow_status === WorkflowStatusExec.Expired) &&
        (await commonService.hasSurveyAccess(
          PermissionType.SurveyAccessExec,
          workContext.userName,
          null
        ))
      ) {
        var resp = await surveyServiceExec.reopenSurvey(
          survey_instance,
          request,
          workContext
        );
        return ResponseController.ok(res, resp);
      } else {
        return ResponseController.unauthorized(res);
      }
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.get(
    "/getAllInvitees/:instance_id",
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(
          Number(req.params.instance_id)
        );
        if (
          await commonService.hasSurveyAccess(
            PermissionType.SurveyAccessExec,
            workContext.userName,
            survey_instance
          )
        ) {
          var invitees = await surveyServiceExec.getAllInvitees(
            Number(req.params.instance_id)
          );
          return ResponseController.ok(res, invitees);
        } else {
          return ResponseController.unauthorized(res);
        }
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/downloadInviteesBySurveyId",
    async function (req, res, next) {
      try {
        var workContext = req.user as WorkContext;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(
          req.body.survey_id
        );
        if (
          survey_instance &&
          (await commonService.hasSurveyAccess(
            PermissionType.SurveyAccessExec,
            workContext.currentUser.userName,
            survey_instance
          ))
        ) {
          var data = await surveyServiceExec.downloadInviteesBySurveyId(
            survey_instance.id
          );
          res.status(200);
          res.setHeader("Content-Type", "text/xlsx");
          res.setHeader(
            "Content-Disposition",
            "attachment; filename=Survey-Invitees.xlsx"
          );
          res.write(data);
          res.end();
        } else {
          return ResponseController.unauthorized(res);
        }
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post("/getSurveyQuestions", async function (req, res, next) {
    try {
      var workContext = req.user as WorkContext;
      var survey_instance = await surveyServiceExec.getSurveyInstanceById(
        Number(req.body.survey_instance_id)
      );
      if (
        survey_instance &&
        (await commonService.hasSurveyAccess(
          PermissionType.SurveyAccessExec,
          workContext.currentUser.userName,
          survey_instance
        ))
      ) {
        var resp = await surveyServiceExec.getSurveyGroupedQuestions(
          survey_instance,
          req.body.lang_code
        );
        return ResponseController.ok(res, resp);
      } else {
        return ResponseController.unauthorized(res);
      }
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.post("/getTemplates", async function (req, res, next) {
    try {
      var workContext = req.user as WorkContext;
      var templates = await surveyServiceExec.getTemplates(req.body.lang_code);
      var survey_instance = await surveyServiceExec.getSurveyInstanceById(
        Number(req.body.survey_instance_id)
      );
      if (
        survey_instance &&
        (await commonService.hasSurveyAccess(
          PermissionType.SurveyAccessExec,
          workContext.currentUser.userName,
          survey_instance
        ))
      ) {
        return ResponseController.ok(res, templates);
      } else {
        return ResponseController.unauthorized(res);
      }
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.post("/resendNotification", async function (req, res, next) {
    try {
      var workContext = (req.user as WorkContext).currentUser;
      const request: any = req.body;
      var survey_instance = await surveyServiceExec.getSurveyInstanceById(
        Number(request.instance_id)
      );
      if (
        survey_instance &&
        survey_instance.workflow_status === WorkflowStatusExec.InProgress &&
        ((await commonService.hasSurveyAccess(
          PermissionType.SurveyAccessExec,
          workContext.userName,
          null
        )) ||
          survey_instance.assigned_for?.toLowerCase() ===
            workContext.userName?.toLowerCase())
      ) {
        await surveyServiceExec.resendNotification(
          survey_instance,
          workContext
        );
        return ResponseController.ok(res, true);
      } else {
        return ResponseController.unauthorized(res);
      }
    } catch (error) {
      console.log(error);
      next(error);
    }
  });

  innerRouter.post(
    "/editSurvey",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        const request: InitiateExecSurvey = req.body;
        var workContext = (req.user as WorkContext).currentUser;
        var resp = await surveyServiceExec.editSurvey(request, workContext);
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/editParticipantInfo",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        const request: any = req.body;
        var workContext = (req.user as WorkContext).currentUser;
        var resp = await surveyServiceExec.editParticipantInfo(
          request,
          workContext
        );
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/getSetupData",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        var resp = await surveyServiceExec.getSetupData(req.body.setup_type);
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/bulkReopenSurvey",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        var resp = await surveyServiceExec.bulkReopenSurvey(
          req.body.survey_instance_ids,
          req.body.due_date,
          workContext
        );
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/bulkChangeDueDate",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        var resp = await surveyServiceExec.bulkChangeDueDate(
          req.body.survey_instance_ids,
          req.body.due_date,
          workContext
        );
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/validateReport",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        var resp = await surveyServiceExec.validateReport(
          req.body.survey_instance_id,
          workContext
        );
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/completedWithoutNotGenerated",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        var resp = await surveyServiceExec.completedWithoutNotGenerated(
          req.body.survey_instance_id,
          workContext
        );
        return ResponseController.ok(res, resp);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.get("/getAllCountries", async function (req, res, next) {
    try {
      var resp = await commonService.getAllCountries();
      return ResponseController.ok(res, resp);
    } catch (error) {
      next(error);
    }
  });

  // innerRouter.post('/getSurveyQuestionsForGroupReport', HasPermission(PermissionType.SurveyInitiateExec), async function (req, res, next) {
  //     try {
  //         var resp = await surveyServiceExec.getSurveyGroupedQuestionsForGroupReport(req.body.survey_instance_ids, req.body.survey_master_id, req.body.lang_code);
  //         return ResponseController.ok(res, resp);

  //     } catch (error) {
  //         console.log(error);
  //         next(error);
  //     }
  // });

  // innerRouter.post('/getTemplatesForGroupReport', HasPermission(PermissionType.SurveyInitiateExec), async function (req, res, next) {
  //     try {
  //         var templates = await surveyServiceExec.getTemplates(req.body.lang_code);
  //         return ResponseController.ok(res, templates);
  //     } catch (error) {
  //         console.log(error);
  //         next(error);
  //     }
  // });

  // innerRouter.get('/getSurveyInstanceForSubmit/:instanceid', async function (req, res, next) {
  //     try {
  //         var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.params.instanceid));
  //         var workContext = (req.user as WorkContext).currentUser;

  //         if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress
  //             && survey_instance.assigned_to?.toLowerCase() === workContext.userName.toLowerCase()) {
  //             return ResponseController.ok(res, survey_instance);
  //         } else {
  //             return ResponseController.unauthorized(res);
  //         }

  //     } catch (error) {
  //         console.log(error);
  //         next(error);
  //     }
  // });

  // innerRouter.get('/getSurveyInstanceForSubmit/:uniqueid', async function (req, res, next) {
  //     try {
  //         var survey_instance = await surveyServiceExec.getSurveyInstanceByUniqueId(req.params.uniqueid);
  //         var workContext = (req.user as WorkContext).currentUser;

  //         if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress
  //             && survey_instance.assigned_to?.toLowerCase() === workContext.userName.toLowerCase()) {
  //             return ResponseController.ok(res, survey_instance);
  //         } else {
  //             return ResponseController.unauthorized(res);
  //         }

  //     } catch (error) {
  //         console.log(error);
  //         next(error);
  //     }
  // });

  // innerRouter.post('/getSurveyQuestionsByRating', async function (req, res, next) {
  //     try {
  //         var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.body.survey_instance_id));
  //         if (survey_instance != null) {
  //             var resp = await surveyServiceExec.getSurveyGroupedQuestions(survey_instance, req.body.lang_code);
  //             return ResponseController.ok(res, resp);
  //         } else {
  //             return ResponseController.unauthorized(res);
  //         }
  //     } catch (error) {
  //         console.log(error);
  //         next(error);
  //     }
  // });

  // innerRouter.post('/getTemplates', async function (req, res, next) {
  //     try {
  //         var templates = await surveyServiceExec.getTemplates(req.body.lang_code);
  //         var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.body.survey_instance_id));
  //         var workContext = (req.user as WorkContext).currentUser;

  //         if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress
  //             && survey_instance.assigned_to?.toLowerCase() === workContext.userName.toLowerCase()) {
  //             return ResponseController.ok(res, templates);
  //         } else {
  //             return ResponseController.unauthorized(res);
  //         }

  //     } catch (error) {
  //         console.log(error);
  //         next(error);
  //     }
  // });

  // innerRouter.post('/submitSurvey', async function (req, res, next) {
  //     try {
  //         var workContext = (req.user as WorkContext).currentUser;
  //         var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.body.survey_instance_id));
  //         if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress
  //             && survey_instance.assigned_to?.toLowerCase() === workContext.userName.toLowerCase()) {
  //             const request: SurveyInstanceDetail[] = req.body.survey_insance_details;
  //             var resp = await surveyServiceExec.submitSurvey(request, survey_instance, req.body.lang_code, workContext);
  //             return ResponseController.ok(res, true);
  //         } else {
  //             return ResponseController.unauthorized(res);
  //         }
  //     } catch (error) {
  //         console.log(error);
  //         next(error);
  //     }
  // });

  return innerRouter;
}
