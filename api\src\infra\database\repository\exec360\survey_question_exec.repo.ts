import { survey_question } from "../../../../domain/entities/exec360/survey_question";
import { BaseRepository } from "../../../../domain/interfaces/repo/baserepository";
import { isurvey_question_execRepo } from "../../../../domain/interfaces/repo/exec360/isurvey_question_exec.repo";
import { Config } from "../../../config/model";
import { LoggerService } from "../../../services/loggerservice";

export class survey_question_execRepo extends BaseRepository<survey_question> implements isurvey_question_execRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_question);
    }
}