// src/index.ts
import { initModels  } from "./init-model";
import sequelize from 'sequelize';
import { Config } from "../config/model";
import { LoggerService } from "../services/loggerservice";

export default class Database {
    _config: Config;
    _logger: LoggerService;
    constructor( config: Config, logger: LoggerService ) {
        this._config = config;
        this._logger = logger;
    } 
    
    public async connect() {
        var dbConfig = new sequelize.Sequelize(
            this._config.database.name,this._config.database.username, this._config.database.password,
            {
                port: this._config.database.port,
                host:this._config.database.server,
                dialect: "postgres", 
                dialectOptions: {
                    ssl: {
                        "require": true
                    }
                },
                ssl : true,
                pool: {
                    max: 5,
                    min: 0,
                    acquire: 30000,
                    idle: 10000
                  }
            }
        );

        await dbConfig.authenticate().then(e=>
            {
                this._logger.info('Database Connection has been established successfully.');
                initModels(dbConfig);

            }).catch((err) => {
                this._logger.error('Unable to connect to the database:', err);
            });
        }

    }
  

