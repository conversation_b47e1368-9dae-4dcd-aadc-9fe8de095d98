import { env } from "../../infra/config";
import { usernameQuery } from "../../infra/database/query/general";
import { adminApiPost } from "../../infra/services/adminapi/adminApi";
import { WorkContext } from "../context/workcontext";
import { AdminUrl } from "../enum/adminurl";
//import { AdminUrl } from "../enum/adminurl";

export class PermissionCheck {

    _workcontext: WorkContext;
    constructor(workContext: WorkContext) {
        this._workcontext = workContext;
    }

    public async HasFormAccessByRole(roleName: string, userName: string) {
        var roles = await adminApiPost(AdminUrl.GetUserRoles, usernameQuery(userName)) as any;
        if (roles != null && roles.find((e: { roleName: string; }) => e.roleName.toLowerCase() == roleName.toLowerCase())) {
            return true;
        }
        else {
            return false;
        }
    }

    public async HasFormAccessByPermission(permissionName: string, userName: string) {
        var permissions = await adminApiPost(AdminUrl.GetUserPermissions, usernameQuery(userName)) as any;
        if (permissions != null && permissions.find((e: { permissionName: string; }) => e.permissionName.toLowerCase() == permissionName.toLowerCase())) {
            return true;
        }
        else {
            return false;
        }
    }

    public async HasFormAccessByRoleWithLocation(roleName: string, userName: string, business_entity_id: number) {
        var roles = await adminApiPost(AdminUrl.GetUserRoles, usernameQuery(userName)) as any;
        if (roles != null && roles.find((e: any) => e.roleName.toLowerCase() === roleName.toLowerCase() && e.locations.includes(business_entity_id?.toString()))) {
            return true;
        }
        else {
            return false;
        }
    }

    public async HasFormAccessByPermissionWithLocation(permissionName: string, userName: string, business_entity_id: number) {
        var permissions = await adminApiPost(AdminUrl.GetUserPermissions, usernameQuery(userName)) as any;
        if (permissions != null && permissions.find((e: any) => e.permissionName.toLowerCase() == permissionName.toLowerCase() && e.locations.includes(business_entity_id?.toString()))) {
            return true;
        }
        else {
            return false;
        }
    }

    public async HasAccessByPermissionWithLocationCode(permissionName: string, userName: string, business_entity_code: string) {
        var permissions = await adminApiPost(AdminUrl.GetUserPermissionsWithLocCode, usernameQuery(userName)) as any;
        var _permission = permissions?.find((e: any) => e.permissionName.toLowerCase() == permissionName.toLowerCase());
        if (_permission) {
            if (_permission.locations.find(w => w.code === 'Group') ||
                _permission.locations.find(w => w.code === business_entity_code?.toString())) {
                return true;
            }
        }
        return false;
    }

    public async HasAccessByPermissionWithLocationCodes(permissionName: string, userName: string, business_entity_codes: string[]) {
        var permissions = await adminApiPost(AdminUrl.GetUserPermissionsWithLocCode, usernameQuery(userName)) as any;
        var _permission = permissions?.find((e: any) => e.permissionName.toLowerCase() == permissionName.toLowerCase());
        if (_permission) {
            if (_permission.locations.find(w => w.code === 'Group')) {
                return true;
            }

            var location_codes = _permission.locations.map(w => w.code);
            if(this.areAllElementsInArray(business_entity_codes, location_codes)) {
                return true;
            }
        }
        return false;
    }

    areAllElementsInArray(arr1, arr2) {
        return arr1.every(element => arr2.includes(element));
    }
}
