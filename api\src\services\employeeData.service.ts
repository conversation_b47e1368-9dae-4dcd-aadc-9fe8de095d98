import { LoggerService } from "../infra/services/loggerservice";
import { employeeDataApiPost } from "../infra/services/employeeData/employeeDataApi";

export class EmployeeDataService {
    _logger: LoggerService;

    constructor(logger: LoggerService) {
        this._logger = logger;
    }

    public async getEmployeedata(userid: string) {
        var list = [];
        if (!!userid) {
            list.push(userid);
        }       
        var empdata_request = { "id_list": list };
        return await employeeDataApiPost("employees/findFirst", empdata_request);
    }

    public async findEmpListByType(type: string, search_text: string) {
        var empdata_request = { "list_name": type, "search_text": search_text };
        return await employeeDataApiPost("employees/finddatalist", empdata_request);
    }

    public async getEmpListByType(type: string, filter_data: any) {
        var empdata_request = { "list_name": type };
        if (filter_data) {
            empdata_request = { ...empdata_request, ...{ "filter_data": filter_data } }
        }
        return await employeeData<PERSON>piPost("employees/getdatalist", empdata_request);
    }

    public async getEmployeesbyEntity(data: any) {
        var empdata_request = {
            "search_text": data.search_text,
            "business_unit": data.business_unit,
            "region": data.region,
            "page_size":10
        };
        return await employeeDataApiPost("employees/find", empdata_request);
    }

    public async getEmployee(data: any) {
        var empdata_request = {
            "search_text": data.search_text,
            "page_size":10
        };
        return await employeeDataApiPost("employees/find", empdata_request);
    }

    public async getEmployeesDirects(person_number: any) {
        var empdata_request = {
            //"search_type":  "line_manager_person_number",
            "id_list": [person_number.toString()],
        };
        return await employeeDataApiPost("employees/findDirect", empdata_request);
    }

    // public async getEmployeesPeers(person_number: any) {
    //     var empdata_request = {
    //         "search_type":  "line_manager_person_number",
    //         "id_list": [person_id.toString()],
    //     };
    //     return await employeeDataApiPost("employees/findDirect", empdata_request);
    // }

    public async getEmployeesLineManager(username: string) {
        var list = [];
        if (!!username) {
            list.push(username);
        }       
        var empdata_request = { "id_list": list };
        return await employeeDataApiPost("employees/findFirst", empdata_request);
    }
}