import * as msal from '@azure/msal-node';
import { env } from '../../config';
import * as axios from 'axios'

const tenantId = env.azure.ad.tennantid;
const clientId = env.azure.ad.clientid;
const clientSecret = env.azure.ad.clientsecret;


const clientConfig = {
  auth: {
    clientId,
    clientSecret,
    authority: `https://login.microsoftonline.com/${tenantId}`
  }
};

const authClient = new msal.ConfidentialClientApplication(clientConfig);

const queryGraphApi = async (path: string) => {
  const tokens = await authClient.acquireTokenByClientCredential({
    scopes: ['https://graph.microsoft.com/.default']
  });

  const options: axios.AxiosRequestConfig = {
    method: 'GET',
    headers: { 'Authorization': `Bearer ${tokens.accessToken}`, 'Accept': "text/html, application/json", "ConsistencyLevel": "eventual" }
  };


  var res = await axios.default.get(`https://graph.microsoft.com/v1.0${path}`, options);

  return await res.data.value;
}

export {
  queryGraphApi
};
