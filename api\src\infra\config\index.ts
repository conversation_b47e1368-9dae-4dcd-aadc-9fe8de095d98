import * as dotenv from 'dotenv';
import fs from 'fs';
import path from "path";
import { Config } from './model';

if (process.env.NODE_ENV !== 'production') {
    if (process.env.NODE_ENV === 'development') {
        dotenv.config();
    }
    else {

        let filename = '/config.json';
        
        if (process.env.NODE_ENV && process.env.NODE_ENV !== '') {
            filename = '/config.' + process.env.NODE_ENV + '.json';
        }

        filename = path.join(__dirname, filename);
        
        let rawData = fs.readFileSync(filename, 'utf8');
        process.env.API_CONFIG = rawData;

    }
    
}
//env.config();
export const env = JSON.parse(process.env.API_CONFIG) as Config;


