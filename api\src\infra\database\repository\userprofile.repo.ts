import { userprofile } from "../../../domain/entities/userprofile";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { iuserprofileRepo } from "../../../domain/interfaces/repo/iuserprofile.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class userprofileRepo extends BaseRepository<userprofile> implements iuserprofileRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, userprofile);
    }
}