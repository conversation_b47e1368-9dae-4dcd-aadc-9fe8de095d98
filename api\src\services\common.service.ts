import { LoggerService } from "../infra/services/loggerservice";
import { CacheManager } from "../infra/cachemanager";
import { Config } from "../infra/config/model";
import { UserSearchService } from "../infra/services/graphapi/usersearch.service";
import { adminApiGet, adminApiPost } from "../infra/services/adminapi/adminApi";
import _ from "lodash";
import {
  activeNonDeletedQuery,
  navQuery,
  nonDeletedQuery,
  textSearchQuery,
  usernameQuery
} from "../infra/database/query/general";
import { AdminServiceUrl, AdminUrl } from "../shared/enum/adminurl";
import { adminRequestApiPost } from "../infra/services/adminapi/adminRequestApi";
import {
  DTOAttachment,
  DTOAttachmentService,
  DTONotification,
  DTORequestHistory,
  DTOTask,
  DTOUserRolePermission
} from "../web/routes/app/common/model";
import { NavType } from "../shared/enum/general";
import path from "path";
import { MessageUtil } from "../shared/utils/MessageUtil";
import { notificationApiPost } from "../infra/services/adminapi/notificationApi";
import { WorkContextUser } from "../shared/context/workcontext";
import { PermissionCheck } from "../shared/utils/permissioncheck";
import fs from "fs";
import {
  QuestionRatingScale,
  SurveyCategory,
  SurveyItem
} from "../web/routes/app/survey-exec/model";
import { env } from "../infra/config";

export class CommonService {
  _logger: LoggerService;
  _cacheManager: CacheManager;
  _tennantId: string;
  _appId: string;
  _userSearchService: UserSearchService;
  _permissionCheck: PermissionCheck;

  private templatepath = path.join(__dirname, "..", "templates");

  constructor(
    config: Config,
    logger: LoggerService,
    userSearchService: UserSearchService,
    cacheManager: CacheManager,
    permissionCheck: PermissionCheck
  ) {
    this._logger = logger;
    this._cacheManager = cacheManager;
    this._tennantId = config.tennantId;
    this._appId = config.applicationId;
    this._userSearchService = userSearchService;
    this._permissionCheck = permissionCheck;
  }

  public async getAllUser(userName: string) {
    var data = await this._userSearchService.SearchUser(userName);

    var returnData = data.map(function (e: any) {
      return {
        email: e.mail,
        user_name: e.userType == "Guest" ? e.mail : e.userPrincipalName,
        display_name: e.displayName,
        title: e.jobTitle,
        department: e.department
      };
    });

    return returnData;
  }

  public async hasSurveyAccess(
    permission_name: string,
    user_name: string,
    survey_instance: any,
    applicationId: string = env.applicationId
  ) {
    var hasPermission = await this._permissionCheck.HasFormAccessByPermission(
      permission_name,
      user_name
    );
    if (hasPermission) {
      return true;
    }

    if (
      survey_instance != null &&
      survey_instance.assigned_to?.toLowerCase() === user_name?.toLowerCase()
    ) {
      return true;
    }

    return false;
  }

  public async HasAccessByPermissionWithLocationCode(
    permission_name: string,
    user_name: string,
    enitty_code: any
  ) {
    var hasPermission =
      await this._permissionCheck.HasAccessByPermissionWithLocationCode(
        permission_name,
        user_name,
        enitty_code
      );
    if (hasPermission) {
      return true;
    }
    return false;
  }

  public async HasAccessByPermissionWithLocationCodes(
    permission_name: string,
    user_name: string,
    business_entity_codes: any
  ) {
    var hasPermission =
      await this._permissionCheck.HasAccessByPermissionWithLocationCodes(
        permission_name,
        user_name,
        business_entity_codes
      );
    if (hasPermission) {
      return true;
    }
    return false;
  }

  //#region Menus
  public async getMenus(username: string) {
    var menus = (await adminApiPost(
      AdminUrl.GetNavigation,
      navQuery(NavType.Side)
    )) as any;
    var permissions = (await adminApiPost(
      AdminUrl.GetUserPermissions,
      usernameQuery(username)
    )) as any;

    menus = _.filter(menus, function (menu) {
      return (
        !menu.required_permission ||
        permissions.find(
          (x: any) =>
            x.permissionName?.toLowerCase() ==
            menu.required_permission?.toLowerCase()
        )
      );
    });
    return menus;
  }

  //#endregion

  //#region Request History

  public async getHistory(entityid: number, entitytype: string) {
    var request = new DTORequestHistory();
    request.entity_type = entitytype;
    request.entity_id = entityid;

    return await adminRequestApiPost(
      AdminServiceUrl.GetRequestHistory,
      request
    );
  }

  public async AddHistory(data: DTORequestHistory) {
    await adminRequestApiPost(AdminServiceUrl.AddRequestHistory, data);
  }

  //#endregion

  //#region Attachments

  public async AddAttachments(
    data: DTOAttachment,
    entityId: number,
    entitytype: string,
    createdby: string,
    meta_data_1?: string,
    meta_data_2?: string,
    meta_data_3?: string
  ) {
    var attachment = new DTOAttachmentService();
    attachment.entity_id = entityId;
    attachment.entity_section = data.section;
    attachment.entity_type = entitytype;
    data.file_base64 = data.file_base64.split("base64,")[1];
    attachment.file_data = Buffer.from(data.file_base64, "base64");
    attachment.attachment_name = data.attachment_name;
    attachment.attachment_rel_path = data.type + "/" + entityId;
    attachment.attachment_content_type = data.attachment_content_type;
    attachment.attachment_content_size = attachment.file_data?.length;
    attachment.description = data.description;
    attachment.meta_data_1 = meta_data_1;
    attachment.meta_data_2 = meta_data_2;
    attachment.meta_data_3 = meta_data_3;
    attachment.additional_info = data.additional_info;
    attachment.created_by = createdby;

    return await adminRequestApiPost(AdminServiceUrl.AddAttachment, attachment);
  }

  public async UpdateAttachment(
    data: DTOAttachment,
    meta_data_1?: string,
    meta_data_2?: string,
    meta_data_3?: string
  ) {
    var attachment = new DTOAttachmentService();
    attachment.id = data.id;
    attachment.description = data.description;
    attachment.meta_data_1 = meta_data_1;
    attachment.meta_data_2 = meta_data_2;
    attachment.meta_data_3 = meta_data_3;
    attachment.additional_info = data.additional_info;
    await adminRequestApiPost(AdminServiceUrl.UpdateAttachment, attachment);
  }

  public async GetAttachments(
    entityId: number,
    entityType: string,
    section?: string
  ) {
    var attachment = new DTOAttachmentService();
    attachment.entity_type = entityType;
    attachment.entity_id = entityId;
    attachment.entity_section = section;

    return await adminRequestApiPost(
      AdminServiceUrl.GetAttachments,
      attachment
    );
  }

  public async downloadImageByFileId(id: string) {
    var data = await adminRequestApiPost(
      AdminServiceUrl.GetAttachmentContentbyFileId,
      { file_id: id }
    );
    return data;
  }

  public async downloadFileById(id: string) {
    var data = await adminRequestApiPost(
      AdminServiceUrl.GetAttachmentContentbyFileId,
      { file_id: id }
    );
    return data;
  }

  public async deleteAttachmentbyFileId(id: string) {
    var data = await adminRequestApiPost(
      AdminServiceUrl.DeleteAttachmentbyFileId,
      { file_id: id }
    );
    return data;
  }

  public async getattchmentbyfileid(id: string) {
    var data = await adminRequestApiPost(AdminServiceUrl.Getattchmentbyfileid, {
      file_id: id
    });
    return data;
  }

  //#endregion

  //#region Task

  public async AddTask(data: DTOTask) {
    var id = await adminRequestApiPost(AdminServiceUrl.AddTask, data);
    return id;
  }

  public async CancelTask(
    id: number,
    createdby: string,
    comments: string,
    additional_info?: string
  ) {
    var task = new DTOTask();
    task.id = id;
    task.created_by = createdby;
    task.comments = comments;
    task.additional_info = additional_info;
    await adminRequestApiPost(AdminServiceUrl.CancelTask, task);
  }

  public async CompleteTask(
    id: number,
    outcome: string,
    comments: string,
    createdby: string,
    additional_info?: string
  ) {
    var task = new DTOTask();
    task.id = id;
    task.outcome = outcome;
    task.created_by = createdby;
    task.comments = comments;
    task.additional_info = additional_info;
    return await adminRequestApiPost(AdminServiceUrl.CompleteTask, task);
  }

  public async CancelAllTask(
    entitytype: string,
    entityid: number,
    comments: string,
    createdby: string
  ) {
    var task = new DTOTask();
    task.entity_id = entityid;
    task.entity_type = entitytype;
    task.comments = comments;
    task.created_by = createdby;
    return await adminRequestApiPost(AdminServiceUrl.CancelAllTask, task);
  }

  public async GetTaskById(id: number) {
    return await adminRequestApiPost(AdminServiceUrl.GetTaskById, { id: id });
  }

  public async GetUsertask(username: string) {
    return await adminRequestApiPost(AdminServiceUrl.GetUsertask, {
      user_name: username
    });
  }

  public async completeAllTask(data: DTOTask) {
    return await adminRequestApiPost(AdminServiceUrl.completeAll, data);
  }

  //#endregion

  public async getbusinessentitybyid(id: number) {
    return await adminApiGet(AdminUrl.GetBusinessEntityById + "/" + id);
  }

  public async getbusinessentitylevels() {
    return await adminApiGet(AdminUrl.Getbusinessentitylevels);
  }

  public async getallbusinessentity(user_name: string, permission: string) {
    var query = { user_name: user_name, permission: permission };
    return await adminApiPost(AdminUrl.GetBusinessEntityByPermission, query);
  }

  public async getallapplicationbusinessentity() {
    return await adminApiPost(AdminUrl.Getallapplicationbusinessentity, null);
  }

  public async getbusinessentityallchild(
    user_name: string,
    permission: string
  ) {
    var query = { user_name: user_name, permission: permission };
    return await adminApiPost(AdminUrl.GetBusinessEntityAllChild, query);
  }

  public async getparentbytype(business_entity_id: number, type: string) {
    var query = { business_entity_id: business_entity_id, type: type };
    return await adminApiPost(AdminUrl.GetParentByType, query);
  }

  public async getbusinessentityfullname(business_entity_id: number) {
    var query = { business_entity_id: business_entity_id };
    return await adminApiPost(AdminUrl.GetBusinessEntityFullName, query);
  }

  public async getallchildfromparent(parent_id: number) {
    var query = { parent_id: parent_id };
    return await adminApiPost(AdminUrl.GetAllChildFromParent, query);
  }

  public async GetRoleUsers(rolename: string) {
    return await adminApiPost(AdminUrl.GetRoleUsers, {
      role_name: rolename,
      business_entity_id: null
    });
  }

  public async getCurrentUserRolePermissions(username: string) {
    var resp = new DTOUserRolePermission();

    resp.permissions = await adminApiPost(
      AdminUrl.GetUserPermissions,
      usernameQuery(username)
    );
    resp.roles = await adminApiPost(
      AdminUrl.GetUserRoles,
      usernameQuery(username)
    );

    return resp;
  }

  public async getAllCountries() {
    return await adminApiGet(AdminUrl.getAllCountries);
  }

  public async getRequestNumber(
    prefix: string,
    metadata1: string,
    metadata2: string = ""
  ) {
    if (metadata2) {
      return await adminRequestApiPost(AdminServiceUrl.GetRequestNumber, {
        prefix: prefix,
        meta_data_1: metadata1,
        meta_data_2: metadata2
      });
    } else {
      return await adminRequestApiPost(AdminServiceUrl.GetRequestNumber, {
        prefix: prefix,
        meta_data_1: metadata1
      });
    }
  }

  public async AddNotification(
    data: any,
    templateName: string,
    user_names: string[],
    entitytype: string,
    url: string,
    currentUser: WorkContextUser,
    bcc_list?: string[],
    attachment?: any
  ) {
    var useremails: any = [];
    var bccuseremails: any = [];
    var assignedToFullName = "";
    var messageresp = await MessageUtil.GenerateMessageFromTemplate(
      templateName,
      data,
      url
    );

    if (user_names?.length > 0 && !data.is_external) {
      await Promise.all(
        user_names?.map(async (item: any) => {
          var email = await this._userSearchService.SearchUserByEmail(item);
          if (email && email[0] && email[0].mail) {
            useremails.push(email[0]?.mail);
          }
        })
      );
    } else {
      useremails.push(data.rater_email);
    }

    if (bcc_list?.length > 0) {
      await Promise.all(
        bcc_list?.map(async (item: any) => {
          var email = await this._userSearchService.SearchUserByEmail(item);
          if (email && email[0] && email[0].mail) {
            bccuseremails.push(email[0]?.mail);
          }
        })
      );
    }

    var notification = new DTONotification();
    notification.entity_type = entitytype;
    notification.entity_id = data.id;
    notification.receiver = useremails
      ? useremails.length > 1
        ? useremails.join(";")
        : useremails[0]
      : "";
    notification.cc = "";
    notification.bcc = bccuseremails
      ? bccuseremails.length > 1
        ? bccuseremails.join(";")
        : bccuseremails[0]
      : "";
    notification.subject = messageresp.subject;
    notification.body = messageresp.body;
    notification.additional_data = null;
    notification.is_approval_email = false;
    notification.approval_task_id = 0;
    notification.attachments = attachment;

    await notificationApiPost(AdminUrl.SendNotificationQueue, notification);
  }

  public async getLocationCodeByPermission(
    permission_name: string,
    user_name: string
  ) {
    var permission = (await adminApiPost(
      AdminUrl.GetUserPermissionsWithLocCode,
      usernameQuery(user_name)
    )) as any;
    var location_codes: any[] = [];
    var permission_list = permission.find(
      (x: any) => x.permissionName === permission_name
    );
    if (permission_list) {
      permission_list.locations
        .map((x: { code: any }) => x.code)
        ?.forEach((b: any) => {
          location_codes.push(b);
        });
    }
    return location_codes;
  }

  public async CreateSvg(current_user_name: string) {
    var userInitial = this.getUserInitial(current_user_name);
    var asciiCode =
      userInitial.charCodeAt(0) +
      (isNaN(userInitial.charCodeAt(1)) ? 0 : userInitial.charCodeAt(1));

    var backgroundColors = [
      "#005e50",
      "#373277",
      "#603d30",
      "#567c73",
      "#69797e",
      "#ca5010",
      "#0078d4",
      "#881798",
      "#750b1c",
      "#498205"
    ];

    var fontColors = [
      "#fff",
      "#fff",
      "#fff",
      "#fff",
      "#fff",
      "#fff",
      "#fff",
      "#fff",
      "#fff",
      "#fff"
    ];

    var index = Math.trunc(asciiCode % backgroundColors.length);

    var svg =
      '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality;" viewBox="0 0 30 30">' +
      '<g><circle style="fill:' +
      backgroundColors[index] +
      ';stroke:#FFF;stroke-width:0.0;" cx="15" cy="15" r="15"></circle>' +
      '<text x="50%" y="50%" font-family="Pilat Demi, Poppins, Helvetica" text-anchor="middle" stroke-width="1px" style="fill: ' +
      fontColors[index] +
      ';" dy=".3em">' +
      userInitial +
      "</text></g></svg>";

      // var file_base64 = btoa(svg);
    var file_base64 = btoa(unescape(encodeURIComponent(svg)));

    return {
      content_data: file_base64,
      content_type: "image/svg+xml",
      attachment_name: "profile_pic"
    };
  }

  public createPrincipleQuestions(
    groupedByPrinciple: any,
    effectiveScale: any,
    ineffectiveScale: any
  ) {
    return Object.keys(groupedByPrinciple).map((principle) => {
      const category = new SurveyCategory();
      category.principle = principle;
      category.effective = this.createQuestionRatingScale(
        groupedByPrinciple[principle],
        effectiveScale
      );
      category.ineffective = this.createQuestionRatingScale(
        groupedByPrinciple[principle],
        ineffectiveScale
      );
      return category;
    });
  }

  private createQuestionRatingScale(questions: any[], scale: any) {
    const ratingScale = new QuestionRatingScale();
    ratingScale.prompt_question = scale.prompt_question;
    // ratingScale.rating_scale = scale.rating_scale;
    ratingScale.description = scale.description;
    ratingScale.enablers = this.groupQuestionsByEnabler(
      questions,
      scale.id,
      scale.rating_scale
    );
    return ratingScale;
  }

  private groupQuestionsByEnabler(
    questions: any[],
    scaleId: number,
    rating_scale: any
  ) {
    const filteredQuestions = questions.filter(
      (x) => x.rating_scale_id === scaleId
    );
    const groupedByEnablers = _.groupBy(
      filteredQuestions,
      (question) => question.enabler
    );

    return Object.keys(groupedByEnablers).map((enabler) => {
      const surveyItem = new SurveyItem();
      surveyItem.title = enabler;
      surveyItem.questions = _.orderBy(
        groupedByEnablers[enabler],
        (q) => q.order_number,
        "asc"
      );
      surveyItem.questions.map((e) => (e.rating_scale = rating_scale));
      return surveyItem;
    });
  }

  private getUserInitial(created_by: string): string {
    created_by = created_by ?? "";
    var names = created_by.split(" ");
    if (names.length === 1) {
      return names[0].substring(0, 1).toUpperCase();
    } else {
      return (
        names[0].substring(0, 1).toUpperCase() +
        names[1]?.substring(0, 1).toUpperCase()
      );
    }
  }

  public GetDocument(FileName: string) {
    //var document = fs.readFileSync('./src/templates/' + FileName);
    var document = fs.readFileSync(this.templatepath + "/" + FileName);
    return document;
  }

  public isValidEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }

  public dateWithoutTimezone = (date: Date) => {
    const tzoffset = date.getTimezoneOffset() * 60000; //offset in milliseconds
    const withoutTimezone = new Date(date.valueOf() - tzoffset)
      .toISOString()
      .slice(0, -1);
    return withoutTimezone;
  };

  public addDaysToDate(originalDate: Date, daysToAdd: number): Date {
    if (originalDate == null) {
      originalDate = new Date();
    }
    const newDate = new Date(originalDate);
    newDate.setDate(originalDate.getDate() + daysToAdd);
    return newDate;
  }
}
