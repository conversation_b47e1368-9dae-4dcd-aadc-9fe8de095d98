import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../../infra/config';
import { AppModel } from '../../base/AppModel';

export type templatesPk = "id";
export type survey_masterId = templates[templatesPk];

export class templates extends AppModel {
  id!: number;
  system_name!: string;
  lang_code?: string;
  template_data?: string;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof templates {
    templates.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    system_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    lang_code: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    template_data: {
      type: DataTypes.STRING,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'templates',
    schema: env.database.schema_exec,
    timestamps: false,
    indexes: [
      {
        name: "templates_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return templates;
  }
}
