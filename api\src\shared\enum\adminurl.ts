export enum AdminUrl {
    GetNavigation = 'nav/getNavigation',
    GetUserPermissions = 'user/getuserpermissions',
    GetUserRoles = 'user/getuserroles',
    GetNotificationTemplate = 'notification/getNotificationTemplate',
    SendNotificationQueue = 'notification/sendQueue',
    CancelInvite = 'notification/cancelInvite',
    GetRoleUsers = 'role/getroleusers',
    GetBusinessEntityByPermission = 'businessentity/getallbusinessentity',
    Getallapplicationbusinessentity = 'businessentity/getallapplicationbusinessentity',
    GetBusinessEntityById = 'businessentity/getbyId',
    Getbusinessentitylevels = 'businessentity/getbusinessentitylevels',
    GetBusinessEntityAllChild = 'businessentity/getbusinessentityallchild',
    GetParentByType = 'businessentity/getparentbytype',
    GetBusinessEntityFullName = 'businessentity/getbusinessentityfullname',
    GetAllChildFromParent = 'businessentity/getallchildfromparent',
    GetUserPermissionsWithLocCode = 'user/getuserpermissionswithloccode',
    GetAllApplicationBusinessentity = 'businessentity/getallapplicationbusinessentity',
    GenerateFromHtml = 'pdf/generatefromhtml',
    getAllCountries = 'setup/getAllCountries',
}

export enum AdminServiceUrl {
    GetRequestHistory = 'history/getrequesthistory',
    GetRequestNumber = 'request/generateNextSequence',
    AddRequestHistory = 'history/addrequesthistory',
    AddAttachment = 'attachment/add',
    UpdateAttachment = 'attachment/update',
    GetAttachments = 'attachment/getattachments',
    GetAttachmentContentbyFileId = 'attachment/getcontentbyfileid',
    DeleteAttachmentbyFileId = 'attachment/deleteattachmentbyfileid',
    Getattchmentbyfileid = 'attachment/getattchmentbyfileid',
    AddTask = 'task/add',
    CancelTask = 'task/cancel',
    CompleteTask = 'task/complete',
    CancelAllTask = 'task/cancelAll',
    CancelOverdue = 'task/cancelOverdue',
    GetTaskById = 'task/getById',
    GetUsertask = 'task/userTask',
    completeAll = 'task/completeAll'
}
