
export class SurveyReportQuestionData {
    survey_type: string;
    answers: any;
}

export class SurveyReportCommentData {
    survey_type: string;
    comments: any;
}

export class SurveyDataType {
    Self: any;
    Manager: any;
    Direct: any;
    Peer: any;
    Other: any;
    Total: any;
}

export class ExportReportData {
    question: string;
    pre_program: ExportReportDataType;
    post_program: ExportReportDataType;    
}

export class ExportReportDataType {
    doing_well: ExportReportDataType;
    improment_needed: ExportReportDataType;
    self: ExportReportDataTypeAnswer;
    manager: ExportReportDataTypeAnswer;
    direct: ExportReportDataTypeAnswer;
    peer: ExportReportDataTypeAnswer;
}



export class ExportReportDataPrincple {
    collaborate_to_win: number;
    deliver_growth: number;
    adapt_and_evolve: number;
    prioritise_customers: number;
    build_for_better_future: number;
}

export class ExportReportDataTypeAnswer extends ExportReportDataPrincple {
    start_doing_it: number;
    do_more_consistently: number;
    already_doing_well: number;
    unable_to_evaluate: number;
}

