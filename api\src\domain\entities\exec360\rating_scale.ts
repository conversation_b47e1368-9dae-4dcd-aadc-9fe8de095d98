import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../../infra/config';
import { AppModel } from '../../base/AppModel';

export type rating_scalePk = "id";
export type rating_scaleId = rating_scale[rating_scalePk];

export class rating_scale extends AppModel {
  id!: number;
  system_name?: string;
  prompt_question?: string;
  lang_code?: string;
  description?: string;
  rating_scale?: object;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof rating_scale {
    rating_scale.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    system_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    prompt_question: {
      type: DataTypes.STRING,
      allowNull: true
    },
    lang_code: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true
    },
    rating_scale: {
      type: DataTypes.JSON,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'rating_scale',
    schema: env.database.schema_exec,
    timestamps: false,
    indexes: [
      {
        name: "rating_scale_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return rating_scale;
  }
}
