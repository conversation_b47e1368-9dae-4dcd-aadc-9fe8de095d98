import { survey_instance } from "../../../domain/entities/survey_instance";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { isurvey_instanceRepo } from "../../../domain/interfaces/repo/isurvey_instance.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class survey_instanceRepo extends BaseRepository<survey_instance> implements isurvey_instanceRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_instance);
    }
}