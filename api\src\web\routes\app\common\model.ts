import { WorkContextUserPermission, WorkContextUserRoles } from "../../../../shared/context/workcontext";

export class DTORequestHistory {
  entity_type: string;
  entity_id: number;
  action_performed: string;
  comments: string;
  additional_info: string;
  created_by: string;
  action_date: Date;
}

export class DTONotification {
  entity_type: string;
  entity_id: number;
  receiver: string;
  cc: string;
  bcc: string;
  subject: string;
  body: string;
  additional_data: object;
  is_approval_email: boolean;
  approval_task_id: number;
  attachments: any;
  is_teams_meeting?: boolean;
  is_calendar_invite?: boolean;
  start_date?: Date;
  end_date?: Date;
  invite_id?: string;
}

export class DTOAttachment {
  id: number;
  file_base64: any;
  attachment_name: string;
  type: string;
  fileimage: string;
  file: any;
  file_id: string;
  isdeleted: boolean;
  url: string;
  size: string;
  description: string;
  additional_info: string;
  section: string;
  attachment_content_type: string;
}

export class DTOAttachmentService {
  id: number;
  file_data: any;
  created_by: string;
  entity_type: string;
  entity_id: number;
  entity_section: string;
  attachment_rel_path: string;
  attachment_name: string;
  attachment_content_type: string;
  attachment_content_size: string;
  description: string;
  meta_data_1: string;
  meta_data_2: string;
  meta_data_3: string;
  additional_info: string;
}

export class DTOTask {
  id: number;
  entity_id: number;
  entity_type: string;
  title: string;
  details: string;
  assigned_to: string;
  is_group_assignemnt: boolean;
  base_url: string;
  rel_url: string;
  additional_info: string;
  created_by: string;
  comments: string;
  outcome: string;
  business_entity_id: number;
  due_date: string;
}

export class MessageTemplate {
  subject: string;
  body: string;
}

export class DTOUserRolePermission {
  roles: WorkContextUserRoles[];
  permissions: WorkContextUserPermission[];
}


export class AdditionalData {
  isNotification: boolean = false;
  actionby: string;
  actionType: string;
  comments: string;
  ApplicationSystemName: string;
  TaskUser: string;
  TaskUsersType: string;
  IsSubmitter: boolean = true;
  createdby: string;
 
}


