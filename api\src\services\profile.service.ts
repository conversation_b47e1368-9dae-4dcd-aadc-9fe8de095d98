import { LoggerService } from "../infra/services/loggerservice";
import { CacheManager } from "../infra/cachemanager";
import { Config } from "../infra/config/model";
import { UserSearchService } from "../infra/services/graphapi/usersearch.service";
import _ from "lodash";
import { CommonService } from "./common.service";
import { userprofileRepo } from "../infra/database/repository/userprofile.repo";
import { WorkContextUser } from "../shared/context/workcontext";
import { userprofile } from "../domain/entities/userprofile";
import { activeNonDeletedQuery, orQuery, textSearchQueryLike, userIdQuery } from "../infra/database/query/general";
import { EmployeeDataService } from "./employeeData.service";

export class ProfileService {
    _logger: LoggerService;
    _cacheManager: CacheManager;
    _userSearchService: UserSearchService;
    _userprofileRepo: userprofileRepo;
    _commonService: CommonService;
    _employeeDataService: EmployeeDataService;

    constructor(logger: LoggerService, userSearchService: UserSearchService, cacheManager: CacheManager, commonService: CommonService,
        userprofileRepo: userprofileRepo, employeeDataService: EmployeeDataService) {
        this._logger = logger;
        this._cacheManager = cacheManager;
        this._userSearchService = userSearchService;
        this._commonService = commonService;
        this._userprofileRepo = userprofileRepo;
        this._employeeDataService = employeeDataService;
    }

    public async getOrAddUserProfile(workcontext: WorkContextUser) {
        var data = await this._userprofileRepo.findFirst(userIdQuery(workcontext.userName, true, true));
        if (!data) {
            // var user_profile = await this._userSearchService.SearchUser(workcontext.userName);
            // if (user_profile && user_profile[0]) {
            var employeeData = await this._employeeDataService.getEmployeedata(workcontext.userName);
            if (employeeData) {
                var request = userprofile.build();
                request.uid = workcontext.userName;
                request.full_name = employeeData.full_name;
                request.job_title = employeeData.job_position;
                request.department = employeeData.department;
                request.email = employeeData.work_email;
                request.employee_id = employeeData.person_number;
                data = await this._userprofileRepo.create(request, workcontext.userName);
            }
           // }
        }

        return data;

    }

    public async getAllUser(userName: string) {
        var query = { ...activeNonDeletedQuery };
        if (!!userName) {
            query = { ...query, ...orQuery([textSearchQueryLike("full_name", userName), textSearchQueryLike("email", userName), textSearchQueryLike("uid", userName)]) }
        };
        var data = await this._userprofileRepo.find(query, [['full_name', 'ASC']], 10, 0);
        let returnData: any[] = [];
        if (data?.length > 0) {
            returnData = data.map(
                function (e: any) {
                    return {
                        email: e.email, user_name: e.uid,
                        display_name: e.full_name, title: e.title, department: e.department
                    }
                }
            );
        }
        return returnData;
    }
}