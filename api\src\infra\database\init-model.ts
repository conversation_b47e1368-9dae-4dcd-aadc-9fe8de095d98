import type { Sequelize } from "sequelize";
import { option_list } from "../../domain/entities/option_list";
import { survey_instance } from "../../domain/entities/survey_instance";
import { survey_instance_detail } from "../../domain/entities/survey_instance_detail";
import { survey_question } from "../../domain/entities/survey_question";
import { survey_section } from "../../domain/entities/survey_section";
import { userprofile } from "../../domain/entities/userprofile";
import { survey_master } from "../../domain/entities/survey_master";
import { survey_instance_cohort } from "../../domain/entities/survey_instance_cohort";
import { survey_instance_detail as survey_instance_detail_exec } from "../../domain/entities/exec360/survey_instance_detail";
import { survey_instance as survey_instance_exec } from "../../domain/entities/exec360/survey_instance";
import { survey_master as survey_master_exec } from "../../domain/entities/exec360/survey_master";
import { survey_question as survey_question_exec } from "../../domain/entities/exec360/survey_question";
import { rating_scale as rating_scale_exec } from "../../domain/entities/exec360/rating_scale";
import { templates as templates_exec } from "../../domain/entities/exec360/templates";
import { setup_data as setup_data_exec } from "../../domain/entities/exec360/setup_data";
import { serialize } from "v8";

export {
  option_list,
  survey_instance,
  survey_instance_detail,
  survey_question,
  survey_section,
  userprofile,
  survey_master,
  survey_instance_detail_exec,
  survey_instance_exec,
  survey_master_exec,
  survey_question_exec,
  rating_scale_exec,
  templates_exec,
  setup_data_exec
};

export function initModels(sequelize: Sequelize) {
  option_list.initModel(sequelize);
  survey_instance.initModel(sequelize);
  survey_instance_detail.initModel(sequelize);
  survey_question.initModel(sequelize);
  survey_section.initModel(sequelize);
  userprofile.initModel(sequelize);
  survey_master.initModel(sequelize);
  survey_instance_cohort.initModel(sequelize);

  survey_instance_detail_exec.initModel(sequelize);
  survey_instance_exec.initModel(sequelize);
  survey_master_exec.initModel(sequelize);
  survey_question_exec.initModel(sequelize);
  rating_scale_exec.initModel(sequelize);
  templates_exec.initModel(sequelize);
  setup_data_exec.initModel(sequelize);

  return {
    option_list: option_list,
    survey_instance: survey_instance,
    survey_instance_detail: survey_instance_detail,
    survey_question: survey_question,
    survey_section: survey_section,
    userprofile: userprofile,
    survey_master: survey_master,
    survey_instance_cohort: survey_instance_cohort,

    survey_instance_detail_exec: survey_instance_detail_exec,
    survey_instance_exec: survey_instance_exec,
    survey_master_exec: survey_master_exec,
    survey_question_exec: survey_question_exec,
    rating_scale_exec: rating_scale_exec,
    templates_exec: templates_exec,
    setup_data_exec: setup_data_exec
  };
}
