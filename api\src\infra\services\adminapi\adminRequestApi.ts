import * as axios from 'axios'
import { env } from '../../config';

const tenantId = env.tennantId;
const applicationId = env.applicationId;
const adminrequestapiurl = env.adminrequestapiurl;

const adminRequestApiGet = async (path: string) => {
  const options: axios.AxiosRequestConfig = {
    method: 'GET',
    headers: { 'tennantId': tenantId, 'applicationid': applicationId, 'Accept': "text/html, application/json" }
  };

  var res = await axios.default.get(`${adminrequestapiurl}${path}`, options);

  return res.data;
}

const adminRequestApiPost = async (path: string, data: any) => {
  const options: axios.AxiosRequestConfig = {
    method: 'POST',
    headers: { 'tennantId': tenantId, 'applicationid': applicationId, 'Accept': "text/html, application/json" },
    maxContentLength: Infinity,
    maxBodyLength: Infinity
  };

  var resp = await axios.default.post(`${adminrequestapiurl}${path}`, data, options);
  return resp.data;
}

export {
  adminRequestApiGet,
  adminRequestApiPost
};
