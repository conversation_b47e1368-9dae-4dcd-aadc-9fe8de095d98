{"environment": "dev", "tennantId": "56b27696-13a4-4b01-82ac-0001aeace8b6", "applicationId": "2b15b878-d172-4946-8531-3279cbeab37a", "adminapiurl": "http://honodeadminapi-dev.hoapps.svc.cluster.local/api/", "adminrequestapiurl": "http://honoderequestapi-dev.hoapps.svc.cluster.local/api/", "notificationserviceurl": "http://honodenotification-dev.hoapps.svc.cluster.local/api/", "baseappurl": "https://appshodev.dpworld.com/360", "pdfserviceurl": "http://pdfserviceapidev.hoapps.svc.cluster.local/api/", "employeedataapiurl": "http://hoemployeedata-dev.hoapps.svc.cluster.local/api/", "log": {"level": "info"}, "app": {"url": "http://localhost", "port": 3604, "allowedOrigins": ["http://localhost:3604"]}, "database": {"server": "psql-hoapps-nonprod.postgres.database.azure.com", "name": "Lead360", "schema": "assessment_dev", "schema_exec": "exec360_dev", "username": "cadevuser", "password": "LG(h]8SV5X", "port": 5432}, "azure": {"ad": {"clientid": "1adb1c92-7181-447b-ae47-d5d5bbab1b65", "clientsecret": "*************************************", "audience": "api://1adb1c92-7181-447b-ae47-d5d5bbab1b65", "scope": ["General"], "tennantid": "2bd16c9b-7e21-4274-9c06-7919f7647bbb", "authority": "sts.windows.net", "discovery": ".well-known/openid-configuration", "version": "v2.0"}}, "cache": {"type": "node-cache", "server": "localhost", "port": 6379, "password": "", "expirationtime": 300}, "exec360reportgeneratecount": 5}