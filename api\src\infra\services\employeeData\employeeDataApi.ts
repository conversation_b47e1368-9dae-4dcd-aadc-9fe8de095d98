import * as axios from 'axios'
import { env } from '../../config';

const tenantId = env.tennantId;
const employeedataapiurl = env.employeedataapiurl;

const employeeDataApiGet = async (path: string) => {
  const options: axios.AxiosRequestConfig = {
    method: 'GET',
    headers: { 'tennantId': tenantId, 'Accept': "text/html, application/json" }
  };

  var res = await axios.default.get(`${employeedataapiurl}${path}`, options);
  return res.data;
}

const employeeDataApiPost = async (path: string, data: any) => {
  const options: axios.AxiosRequestConfig = {
    method: 'POST',
    headers: { 'tennantId': tenantId, 'Accept': "text/html, application/json" }
  };

  var res = await axios.default.post(`${employeedataapiurl}${path}`, data, options);
  return res.data;
}

export {
  employeeDataApiGet,
  employeeDataApiPost
};
