import express, { Router } from "express";
import { container } from "../../../../container";
import { ReportService } from "../../../../services/report.service";
import { ResponseController } from "../../../../shared/utils/ResponseController";
import { WorkContext } from "../../../../shared/context/workcontext";
import { SurveyService } from "../../../../services/survey.service";
import { CommonService } from "../../../../services/common.service";
import { PermissionType, WorkflowStatus, WorkflowStatus_Cohort } from "../../../../shared/enum/general";


export function register(router: Router) {
    var reportService: ReportService = container.resolve('reportService');
    var surveyService: SurveyService = container.resolve('surveyService');
    var commonService: CommonService = container.resolve('commonService');

    const innerRouter = express.Router();

    innerRouter.get('/getSurveyDataByQuestionId/:instance_id/:question_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const question_id: any = Number(req.params.question_id);
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instance_id));
            if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.Completed || survey_instance.workflow_status === WorkflowStatus.Archived || survey_instance.workflow_status === WorkflowStatus.Expired || survey_instance.workflow_status === WorkflowStatus.ArchivedExpired)
                && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                var user_submitted_surveys = await reportService.getUserCompletedSurvey(survey_instance.id);
                if (user_submitted_surveys?.length >= 5) {
                    var result = await reportService.getSurveyDataByQuestionId(question_id, survey_instance.id, survey_instance.assigned_for);
                    return ResponseController.ok(res, result);
                }
            }
            return ResponseController.unauthorized(res);

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getSurveyDetailDataByQuestionId/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(request.instance_id));
            if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.Completed || survey_instance.workflow_status === WorkflowStatus.Archived || survey_instance.workflow_status === WorkflowStatus.Expired || survey_instance.workflow_status === WorkflowStatus.ArchivedExpired)
                && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                var user_submitted_surveys = await reportService.getUserCompletedSurvey(survey_instance.id);
                if (user_submitted_surveys?.length >= 5) {
                    var result = await reportService.getSurveyDetailDataByQuestionId(request, survey_instance.id, survey_instance.assigned_for);
                    return ResponseController.ok(res, result);
                }
            }
            return ResponseController.unauthorized(res);

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveyCommentsByQuestionId/:instance_id/:question_id', async function (req, res, next) {
        var workContext = (req.user as WorkContext).currentUser;
        const question_id: any = Number(req.params.question_id);
        var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instance_id));
        if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.Completed || survey_instance.workflow_status === WorkflowStatus.Archived || survey_instance.workflow_status === WorkflowStatus.Expired || survey_instance.workflow_status === WorkflowStatus.ArchivedExpired)
            && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
            var user_submitted_surveys = await reportService.getUserCompletedSurvey(survey_instance.id);
            if (user_submitted_surveys?.length >= 5) {
                var result = await reportService.getSurveyCommentsByQuestionId(question_id, survey_instance.id, survey_instance.assigned_for);
                return ResponseController.ok(res, result);
            }
        }
        return ResponseController.unauthorized(res);
    });

    innerRouter.post('/getSurveySummary/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(request.instance_id));
            if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.Completed || survey_instance.workflow_status === WorkflowStatus.Archived || survey_instance.workflow_status === WorkflowStatus.Expired || survey_instance.workflow_status === WorkflowStatus.ArchivedExpired)
                && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                var user_submitted_surveys = await reportService.getUserCompletedSurvey(survey_instance.id);
                if (user_submitted_surveys?.length >= 5) {
                    var result = await reportService.getUserSurveySummary(survey_instance.assigned_for, survey_instance.id);
                    return ResponseController.ok(res, result);
                }
            }
            return ResponseController.unauthorized(res);

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/printUserSurveyReport/:instance_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var request = req.body;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instance_id));
            if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.Completed || survey_instance.workflow_status === WorkflowStatus.Archived || survey_instance.workflow_status === WorkflowStatus.Expired || survey_instance.workflow_status === WorkflowStatus.ArchivedExpired)
                && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                var user_submitted_surveys = await reportService.getUserCompletedSurvey(survey_instance.id);
                if (user_submitted_surveys?.length >= 5) {
                    var result = await reportService.printUserSurveyReport(request, survey_instance);
                    res.status(200);
                    res.setHeader('Content-Type', 'text/xlsx');
                    res.setHeader(
                        'Content-Disposition',
                        'attachment; filename=Limits.xlsx'
                    );
                    res.write(result);
                    res.end();
                }
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    // innerRouter.get('/getUserCompletedSurvey/:instance_id', async function (req, res, next) {
    //     try {
    //         var workContext = (req.user as WorkContext).currentUser;
    //         var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instance_id));
    //         if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.Completed || survey_instance.workflow_status === WorkflowStatus.Archived) && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
    //             var user_submitted_surveys = await reportService.getUserCompletedSurvey(survey_instance.id);
    //             return ResponseController.ok(res, user_submitted_surveys);
    //         } else {
    //             return ResponseController.unauthorized(res);
    //         }
    //     } catch (error) {
    //         console.log(error);
    //         next(error);
    //     }
    // });

    innerRouter.get('/getUserCompletedSurveyCount/:instance_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instance_id));
            if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.Completed
                || survey_instance.workflow_status === WorkflowStatus.Archived || survey_instance.workflow_status === WorkflowStatus.Expired || survey_instance.workflow_status === WorkflowStatus.ArchivedExpired)
                && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                var user_submitted_surveys = await reportService.getUserCompletedSurveyCount(survey_instance.id);
                return ResponseController.ok(res, user_submitted_surveys);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getCompletedCohortSurveyCount/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var request: any = req.body;
            var cohort_survey_instance = await surveyService.getSurveyInstanceCohortById(Number(request.cohort_id));
            if (cohort_survey_instance != null && (cohort_survey_instance.workflow_status !== WorkflowStatus_Cohort.Open)
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohort_survey_instance.root_entity_code])) {
                var user_submitted_surveys = await reportService.getCompletedCohortSurvey(request);
                return ResponseController.ok(res, user_submitted_surveys);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getCohortSurveyDataByQuestionId/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohort_survey_instance = await surveyService.getSurveyInstanceCohortById(Number(request.cohort_id));
            if (cohort_survey_instance != null && (cohort_survey_instance.workflow_status !== WorkflowStatus_Cohort.Open)
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohort_survey_instance.root_entity_code])) {
                // var cohort_submitted_surveys = await reportService.getCompletedCohortSurvey(cohort_survey_instance.id);
                // if (cohort_submitted_surveys?.length >= 5) {
                var result = await reportService.getCohortSurveyDataByQuestionId(request.question_id, cohort_survey_instance.id, request.is_post_survey);
                return ResponseController.ok(res, result);
                // }
            }
            return ResponseController.unauthorized(res);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getAggregateCohortDataByQuestionId/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohorts = await surveyService.getSurveyInstanceCohortByIds(request.cohort_ids);
            if (cohorts != null && (cohorts.every(x => x.workflow_status !== WorkflowStatus_Cohort.Open))
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohorts.map(q => q.root_entity_code)])) {
                var result = await reportService.getAggregateCohortDataByQuestionId(request.question_id, cohorts.map(q => q.id), request.is_post_survey);
                return ResponseController.ok(res, result);
            }
            return ResponseController.unauthorized(res);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getCohortSurveyDetailDataByQuestionId/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohort_survey_instance = await surveyService.getSurveyInstanceCohortById(Number(request.cohort_id));
            if (cohort_survey_instance != null && (cohort_survey_instance.workflow_status !== WorkflowStatus_Cohort.Open)
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohort_survey_instance.root_entity_code])) {
                // var cohort_submitted_surveys = await reportService.getCompletedCohortSurvey(cohort_survey_instance.id);
                // if (cohort_submitted_surveys?.length >= 5) {
                var result = await reportService.getCohortSurveyDetailDataByQuestionId(request.question_id, cohort_survey_instance.id, request.is_post_survey);
                return ResponseController.ok(res, result);
                // }
            }
            return ResponseController.unauthorized(res);

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getAggregateCohortDetailDataByQuestionId/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohorts = await surveyService.getSurveyInstanceCohortByIds(request.cohort_ids);
            if (cohorts != null && (cohorts.every(x => x.workflow_status !== WorkflowStatus_Cohort.Open))
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohorts.map(q => q.root_entity_code)])) {
                var result = await reportService.getAggregateCohortDetailDataByQuestionId(request.question_id, cohorts.map(q => q.id), request.is_post_survey);
                return ResponseController.ok(res, result);
            }
            return ResponseController.unauthorized(res);

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getCohortSurveyCommentsByQuestionId/:cohort_id/:question_id', async function (req, res, next) {
        var workContext = (req.user as WorkContext).currentUser;
        const question_id: any = Number(req.params.question_id);
        var cohort_survey_instance = await surveyService.getSurveyInstanceCohortById(Number(req.params.cohort_id));
        if (cohort_survey_instance != null && (cohort_survey_instance.workflow_status !== WorkflowStatus_Cohort.Open)
            && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohort_survey_instance.root_entity_code])) {
            // var cohort_submitted_surveys = await reportService.getCompletedCohortSurvey(cohort_survey_instance.id);
            // if (cohort_submitted_surveys?.length >= 5) {
            var result = await reportService.getCohortSurveyCommentsByQuestionId(question_id, cohort_survey_instance.id);
            return ResponseController.ok(res, result);
            // }
        }
        return ResponseController.unauthorized(res);
    });

    innerRouter.post('/getCohortSurveySummary/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohort_survey_instance = await surveyService.getSurveyInstanceCohortById(Number(request.cohort_id));
            if (cohort_survey_instance != null && (cohort_survey_instance.workflow_status !== WorkflowStatus_Cohort.Open)
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohort_survey_instance.root_entity_code])) {
                // var cohort_submitted_surveys = await reportService.getCompletedCohortSurvey(cohort_survey_instance.id);
                // if (cohort_submitted_surveys?.length >= 5) {
                var result = await reportService.getCohortSurveySummary(cohort_survey_instance.id, request.is_post_survey);
                return ResponseController.ok(res, result);
                // }
            }
            return ResponseController.unauthorized(res);

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getAggregateCohortSummary/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohorts = await surveyService.getSurveyInstanceCohortByIds(request.cohort_ids);
            if (cohorts != null && (cohorts.every(x => x.workflow_status !== WorkflowStatus_Cohort.Open))
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohorts.map(q => q.root_entity_code)])) {
                var result = await reportService.getAggregateCohortSummary(cohorts.map(q => q.id), request.is_post_survey);
                return ResponseController.ok(res, result);
                // }
            }
            return ResponseController.unauthorized(res);

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/printCohortSurveyReport/:cohort_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var request = req.body;
            var cohort_survey_instance = await surveyService.getSurveyInstanceCohortById(Number(req.params.cohort_id));
            if (cohort_survey_instance != null && (cohort_survey_instance.workflow_status !== WorkflowStatus_Cohort.Open)
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohort_survey_instance.root_entity_code])) {
                // var cohort_submitted_surveys = await reportService.getCompletedCohortSurvey(cohort_survey_instance.id);
                // if (cohort_submitted_surveys?.length >= 5) {
                var result = await reportService.printCohortSurveyReport(request, cohort_survey_instance);
                res.status(200);
                res.setHeader('Content-Type', 'text/xlsx');
                res.setHeader(
                    'Content-Disposition',
                    'attachment; filename=Limits.xlsx'
                );
                res.write(result);
                res.end();
                // }
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/printAggregateCohortReport/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohorts = await surveyService.getSurveyInstanceCohortByIds(request.cohort_ids);
            if (cohorts != null && (cohorts.every(x => x.workflow_status !== WorkflowStatus_Cohort.Open))
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohorts.map(q => q.root_entity_code)])) {
                var result = await reportService.printAggregateCohortReport(request, cohorts);
                res.status(200);
                res.setHeader('Content-Type', 'text/xlsx');
                res.setHeader(
                    'Content-Disposition',
                    'attachment; filename=cohort.pdf'
                );
                res.write(result);
                res.end();
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/exportAggregateCohortReport/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohorts = await surveyService.getSurveyInstanceCohortByIds(request.cohort_ids);
            if (cohorts != null && (cohorts.every(x => x.workflow_status !== WorkflowStatus_Cohort.Open))
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohorts.map(q => q.root_entity_code)])) {
                var result = await reportService.exportAggregateCohortReport(request);
                res.status(200);
                res.setHeader('Content-Type', 'text/xlsx');
                res.setHeader(
                    'Content-Disposition',
                    'attachment; filename=cohort.pdf'
                );
                res.write(result);
                res.end();
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/exportcohortParticipantReport/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohort = await surveyService.getSurveyInstanceCohortById(request.cohort_id);
            if (cohort != null && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohort.root_entity_code])) {
                var result = await reportService.exportcohortParticipantReport(cohort);
                res.status(200);
                res.setHeader('Content-Type', 'text/xlsx');
                res.setHeader(
                    'Content-Disposition',
                    'attachment; filename=cohort.pdf'
                );
                res.write(result);
                res.end();
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    return innerRouter;
}
