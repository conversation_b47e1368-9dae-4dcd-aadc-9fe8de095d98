import express, { Router } from "express";
import { container } from "../../../../container";
import { ResponseController } from "../../../../shared/utils/ResponseController";
import { WorkContext } from "../../../../shared/context/workcontext";
import { CommonService } from "../../../../services/common.service";
import {
  PermissionType,
  WorkflowStatus,
  WorkflowStatus_Cohort,
  WorkflowStatusExec
} from "../../../../shared/enum/general";
import { ReportServiceExec } from "../../../../services/exec360/report_exec.service";
import { SurveyServiceExec } from "../../../../services/exec360/survey_exec.service";
import { env } from "../../../../infra/config";
import { HasPermission } from "../../../validators/app.validators.exec";
import { filters } from "../survey-exec/model";

export function register(router: Router) {
  var reportServiceExec: ReportServiceExec =
    container.resolve("reportServiceExec");
  var surveyServiceExec: SurveyServiceExec =
    container.resolve("surveyServiceExec");
  var commonService: CommonService = container.resolve("commonService");

  const innerRouter = express.Router();

  innerRouter.get(
    "/getUserSurveyInstances/:survey_instance_id",
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        const request: any = req.params;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(
          Number(request.survey_instance_id)
        );
        if (
          await commonService.hasSurveyAccess(
            PermissionType.SurveyAccessExec,
            workContext.userName,
            survey_instance
          )
        ) {
          var result = await reportServiceExec.getUserSurveyInstances(
            survey_instance.id
          );
          return ResponseController.ok(res, result);
        }
        return ResponseController.unauthorized(res);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.get(
    "/getSurveyDetailBySurveyId/:survey_instance_id",
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        const request: any = req.params;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(
          Number(request.survey_instance_id)
        );
        if (
          await commonService.hasSurveyAccess(
            PermissionType.SurveyAccessExec,
            workContext.userName,
            survey_instance
          )
        ) {
          var result = await reportServiceExec.getSurveyDetailBySurveyId(
            survey_instance.id
          );
          return ResponseController.ok(res, result);
        }
        return ResponseController.unauthorized(res);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/printUserSurveyReport/:instance_id",
    async function (req, res, next) {
      try {
        var workContext = (req.user as WorkContext).currentUser;
        var request = req.body;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(
          Number(req.params.instance_id)
        );
        if (
          survey_instance != null &&
          (survey_instance.workflow_status === WorkflowStatusExec.Completed ||
            survey_instance.workflow_status ===
              WorkflowStatusExec.CompletedReportReady) &&
          (await commonService.hasSurveyAccess(
            PermissionType.SurveyAccessExec,
            workContext.userName,
            survey_instance
          ))
        ) {
          var user_submitted_surveys =
            await reportServiceExec.getUserCompletedSurvey(survey_instance.id);
          if (
            user_submitted_surveys?.length >= env.exec360reportgeneratecount
          ) {
            var result = await reportServiceExec.printUserSurveyReport(
              request,
              survey_instance
            );
            res.status(200);
            res.setHeader("Content-Type", "text/xlsx");
            res.setHeader(
              "Content-Disposition",
              "attachment; filename=SurveyReport.xlsx"
            );
            res.write(result);
            res.end();
          } else {
            return ResponseController.unauthorized(res);
          }
        } else {
          //  var result = await reportServiceExec.printUserSurveyReport(request, survey_instance);
          //     res.status(200);
          //     res.setHeader('Content-Type', 'application/pdf');
          //     res.setHeader(
          //         'Content-Disposition',
          //         'attachment; filename=AssessmentReport.pdf'
          //     );
          //     res.write(result);
          //     res.end();
          return ResponseController.unauthorized(res);
        }
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/printGroupUserSurveyReport",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        var request = req.body;
        var result = await reportServiceExec.printGroupUserSurveyReport(
          request
        );
        res.status(200);
        res.setHeader("Content-Type", "text/xlsx");
        res.setHeader(
          "Content-Disposition",
          "attachment; filename=SurveyReport.xlsx"
        );
        res.write(result);
        res.end();
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  innerRouter.post(
    "/getGroupUserReportData",
    HasPermission(PermissionType.SurveyInitiateExec),
    async function (req, res, next) {
      try {
        const request: filters = req.body;
        var result = await reportServiceExec.getGroupUserReportData(request);
        return ResponseController.ok(res, result);
      } catch (error) {
        console.log(error);
        next(error);
      }
    }
  );

  return innerRouter;
}
