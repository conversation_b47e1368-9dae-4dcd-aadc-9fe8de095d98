import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../../infra/config';
import { AppModel } from '../../base/AppModel';


export type survey_masterPk = "id";
export type survey_masterId = survey_master[survey_masterPk];

export class survey_master extends AppModel {
  id!: number;
  title!: string;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof survey_master {
    survey_master.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'survey_master',
    schema: env.database.schema_exec,
    timestamps: false,
    indexes: [
      {
        name: "survey_master_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return survey_master;
  }
}
