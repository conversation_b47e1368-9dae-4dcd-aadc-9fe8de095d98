import * as axios from 'axios'
import { env } from '../../config';

const tenantId = env.tennantId;
const applicationId = env.applicationId;
const pdfserviceurl = env.pdfserviceurl;

const pdfApiGet = async (path: string) => {
    const options: axios.AxiosRequestConfig = {
        method: 'GET',
        headers: { 'tennantId': tenantId, 'applicationid': applicationId, 'Accept': "text/html, application/json" }
    };

    var res = await axios.default.get(`${pdfserviceurl}${path}`, options);
    return res.data;
}

const pdfApiPost = async (path: string, data: any) => {
    const options: axios.AxiosRequestConfig = {
        method: 'POST',
        timeout: 3000000,
        headers: { 'tennantId': tenantId, 'applicationid': applicationId, 'Accept': "application/pdf" },
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
        responseType: 'arraybuffer',
    };` `

    var res = await axios.default.post(`${pdfserviceurl}${path}`, data, options);
    return res.data;
}

export {
    pdfApiGet,
    pdfApiPost
};
