import puppeteer, { PDFOptions } from 'puppeteer';
import { Promise as PromiseBluebird } from 'bluebird';
import hb from 'handlebars';
import * as Handlebars from 'handlebars';
import * as fs from 'fs';
import path from 'path';
import { formatDateString } from './../helpers/date-helper';
import { currencyFormatter } from './../helpers/currency-formatter.helper';
import { pdfApiPost } from '../../infra/services/adminapi/pdfApi';
import { AdminUrl } from '../enum/adminurl';

type CallBackType = (pdf: any) => void;

interface OptionsProps extends PDFOptions {
  args?: string[];
}

interface FileWithUrl {
  url: string;
  content?: never;
}

interface FileWithContent {
  url?: never;
  content: string;
}

type FileType = FileWithUrl | FileWithContent;
export class PDFUtil {
  static async generatePdf(file: FileType, options: OptionsProps, header_template?: any, footer_template?: any, callback?: CallBackType) {
    let args = [
      '--no-sandbox',
      '--disable-setuid-sandbox',
    ];

    if (options?.args) {
      args = options.args;
      delete options.args;
    }

    options.displayHeaderFooter = true;
    if (header_template) {
      options.headerTemplate = header_template;
    }
    if (footer_template) {
      options.footerTemplate = footer_template;
    }
    options.margin = { top: '225px', bottom: '20px', left: '15px', right: '15px' };
    options.printBackground = true;
    options.format = 'a4';

    const browser = await puppeteer.launch({
      // headless: true,
      executablePath: process.env.CHROME_BIN || null,
      args
    });

    const page = await browser.newPage();

    if (file.content) {
      const template = hb.compile(file.content, { strict: true });
      const result = template(file.content);
      const html = result;

      await page.setContent(html);
    } else {
      await page.goto(file.url as string, {
        waitUntil: 'networkidle0',
      });
    }

    if (file.content) { }

    return PromiseBluebird.props(page.pdf(options))
      .then(async function (data) {
        await browser.close();

        return Buffer.from(Object.values(data));
      }).asCallback(callback);
  }

  static async generatehbsToPdf(data: any, templateName: string, landscape = false): Promise<Buffer> {
    //const templatePath = path.join(__dirname, '..', '..', `templates/${templateName}.hbs`);
    var hbsTemplatePath = path.join(__dirname, '..', '..', 'templates');
    this.setCustomHandlebarsHelpers();
    const template = Handlebars.compile(fs.readFileSync(hbsTemplatePath + `/${templateName}.hbs`, 'utf8'));
    const html = template(data);

    var request = {
      htmlbody: html,
      options: {
        format: 'a4', margin: { top: '15px', bottom: '15px', left: '15px', right: '15px' }, landscape: landscape
      }
    }
    const pdf = await pdfApiPost(AdminUrl.GenerateFromHtml, request);
    return pdf;

    // const browser = await puppeteer.launch({
    //   headless: true,
    //   args: ['--font-render-hinting=none', '--no-sandbox', '--disabled-setupid-sandbox']
    // });
    // // let options = { headless: true, args: ["--disable-gpu", "--disable-dev-shm-usage", "--disable-setuid-sandbox", "--no-sandbox"] };

    // const page = await browser.newPage();
    // await page.setContent(html, { waitUntil: 'networkidle0' });

    // var options: any = {
    //   margin: { top: '15px', bottom: '15px', left: '15px', right: '15px' },
    //   printBackground: true,
    //   format: 'a4',
    //   landscape: landscape,
    // }

    // await page.evaluateHandle('document.fonts.ready');

    // const pdf = await page.pdf(options);
    // await browser.close();
    // return pdf;
  }

  /**
   * Set custom handlebars helpers
   */
  static setCustomHandlebarsHelpers() {
    Handlebars.registerHelper('eq', (value1, value2) => {
      return value1 === value2;
    });

    Handlebars.registerHelper('neq', (value1, value2) => {
      return value1 !== value2;
    });

    Handlebars.registerHelper('or', (value1, value2) => {
      return value1 || value2;
    });


    Handlebars.registerHelper('and', (value1, value2) => {
      return value1 && value2;
    });

    Handlebars.registerHelper('contains', (str, substr) => {
      if (str.indexOf(substr) !== -1) {
        return true;
      } else {
        return false;
      }
    });

    Handlebars.registerHelper('formattedCurrency', (amount, currency) => {
      return currencyFormatter(amount, currency);
    });

    Handlebars.registerHelper('formattedDate', (date) => {
      return formatDateString(date);
    });
  }
}
