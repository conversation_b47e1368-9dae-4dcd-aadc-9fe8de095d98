import { any } from 'bluebird';
import e from 'express';
import express, { Application } from 'express';
import { container } from '../../container';
import { CommonService } from '../../services/common.service';

import { WorkContext } from '../../shared/context/workcontext';
import { SurveyService } from '../../services/survey.service';
import contentDisposition from 'content-disposition'
import { SurveyServiceExec } from '../../services/exec360/survey_exec.service';
import { WorkflowStatus } from '../../shared/enum/general';
import { ResponseController } from '../../shared/utils/ResponseController';
import { SurveyInstanceDetail } from './app/survey/model';

export function register(app: Application) {

    var surveyServiceExec: SurveyServiceExec = container.resolve('surveyServiceExec');
    
    const router = express.Router();
    // define the home page route
    router.get('/', function (req, res) {
        res.send("App is up and running.");
    });

    router.get('/app/common/downloadImageByFileId/:fileid', async function (req, res) {
        var _commonService: CommonService = container.resolve('commonService');
        var resp = await _commonService.downloadImageByFileId(req.params.fileid);
        res.status(200);
        res.setHeader('Content-Type', resp.attachment_content_type);
        res.setHeader(
            'Content-Disposition',
            'attachment; filename=' + resp.attachment_name
        );
        var buffer: Buffer = Buffer.from(resp.contents.data);
        res.write(buffer);
        res.end();
    });

    router.get('/app/common/getProfileImage/:instanceid', async function (req, res) {
        var _commonService: CommonService = container.resolve('commonService');
        var _surveyService: SurveyService = container.resolve('surveyService');
        var buffer: Buffer;
        var survey_instance = await _surveyService.getSurveyInstanceById(Number(req.params.instanceid))
        var respSvg = await _commonService.CreateSvg(survey_instance.assigned_to_data?.full_name);
        var attachment_name = respSvg.attachment_name;
        var content_type = respSvg.content_type;
        buffer = Buffer.from(respSvg.content_data, "base64");

        if (buffer) {
            res.status(200);
            res.setHeader('Content-Type', content_type);
            res.setHeader(
                'Content-Disposition', contentDisposition(attachment_name)
            );

            res.write(buffer);
            res.end();
        }
    });

    router.get('/app/common/getAssigneeProfileImage/:instanceid', async function (req, res) {
        var _commonService: CommonService = container.resolve('commonService');
        var _surveyService: SurveyService = container.resolve('surveyService');
        var buffer: Buffer;
        var survey_instance = await _surveyService.getSurveyInstanceById(Number(req.params.instanceid))
        var respSvg = await _commonService.CreateSvg(survey_instance?.assigned_for_data?.full_name);
        var attachment_name = respSvg.attachment_name;
        var content_type = respSvg.content_type;
        buffer = Buffer.from(respSvg.content_data, "base64");

        if (buffer) {
            res.status(200);
            res.setHeader('Content-Type', content_type);
            res.setHeader(
                'Content-Disposition', contentDisposition(attachment_name)
            );

            res.write(buffer);
            res.end();
        }
    });

    router.post('/app/common/getProfileImage/', async function (req, res) {
        var _commonService: CommonService = container.resolve('commonService');
        var buffer: Buffer;
        var respSvg = await _commonService.CreateSvg(req.body.full_name);
        var attachment_name = respSvg.attachment_name;
        var content_type = respSvg.content_type;
        buffer = Buffer.from(respSvg.content_data, "base64");

        if (buffer) {
            res.status(200);
            res.setHeader('Content-Type', content_type);
            res.setHeader(
                'Content-Disposition', contentDisposition(attachment_name)
            );

            res.write(buffer);
            res.end();
        }
    });

    router.get('/app/common/getProfileImageExec/:instanceid', async function (req, res) {
        var _commonService: CommonService = container.resolve('commonService');
        
        var buffer: Buffer;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.params.instanceid))
        var respSvg = await _commonService.CreateSvg(survey_instance?.assigned_to_data?.full_name);
        var attachment_name = respSvg.attachment_name;
        var content_type = respSvg.content_type;
        buffer = Buffer.from(respSvg.content_data, "base64");

        if (buffer) {
            res.status(200);
            res.setHeader('Content-Type', content_type);
            res.setHeader(
                'Content-Disposition', contentDisposition(attachment_name)
            );

            res.write(buffer);
            res.end();
        }
    });

    router.get('/app/common/getAssigneeProfileImageExec/:instanceid', async function (req, res) {
        var _commonService: CommonService = container.resolve('commonService');
        
        var buffer: Buffer;
        var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.params.instanceid))
        var respSvg = await _commonService.CreateSvg(survey_instance.assigned_for_data?.full_name);
        var attachment_name = respSvg.attachment_name;
        var content_type = respSvg.content_type;
        buffer = Buffer.from(respSvg.content_data, "base64");

        if (buffer) {
            res.status(200);
            res.setHeader('Content-Type', content_type);
            res.setHeader(
                'Content-Disposition', contentDisposition(attachment_name)
            );

            res.write(buffer);
            res.end();
        }
    });

    router.get('/app/survey-exec/getSurveyInstanceForOpenSubmit/:uniqueid', async function (req, res, next) {
        try {
            var survey_instance = await surveyServiceExec.getSurveyInstanceByUniqueId(req.params.uniqueid);
            if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress) {
                return ResponseController.ok(res, survey_instance);
            } else {
                return ResponseController.unauthorized(res);
            }

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    router.post('/app/survey-exec/getSurveyQuestionsForOpenSubmit', async function (req, res, next) {
        try {
            var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.body.survey_instance_id));
            if (survey_instance != null && survey_instance.workflow_status === WorkflowStatus.InProgress) {
                var resp = await surveyServiceExec.getSurveyGroupedQuestions(survey_instance, req.body.lang_code);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    router.post('/app/survey-exec/getTemplatesForOpenSubmit', async function (req, res, next) {
        try {
            var templates = await surveyServiceExec.getTemplates(req.body.lang_code);
            var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.body.survey_instance_id));

            if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress) {
                return ResponseController.ok(res, templates);
            } else {
                return ResponseController.unauthorized(res);
            }

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    router.post('/app/survey-exec/submitSurveyForOpenSubmit', async function (req, res, next) {
        try {
            var survey_instance = await surveyServiceExec.getSurveyInstanceById(Number(req.body.survey_instance_id));
            if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress) {
                const request: SurveyInstanceDetail[] = req.body.survey_insance_details;
                var resp = await surveyServiceExec.submitSurvey(request, survey_instance, req.body.lang_code, survey_instance.assigned_to);
                return ResponseController.ok(res, true);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    app.use(router);
}


