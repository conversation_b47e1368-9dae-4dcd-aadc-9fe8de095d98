import { Request, Response, NextFunction } from "express";
import { usernameQuery } from "../../infra/database/query/general";
import { adminApiPost } from "../../infra/services/adminapi/adminApi";
import { WorkContext } from "../../shared/context/workcontext";
import { env } from "../../infra/config";

export function HasRole(role: string) {
  return async function (req: Request, res: Response, next: NextFunction) {
    let workContext = req.user as WorkContext;
    var roles = await adminApiPost("user/getuserroles", usernameQuery(workContext.currentUser.userName)) as any;

    if (roles != null && roles.find((e: { roleName: string; }) => e.roleName.toLowerCase() == role.toLowerCase())) {
      next();
    }
    else {
      res.send(401);
    }
  }
}

export function HasPermission(permission: string) {
  return async function (req: Request, res: Response, next: NextFunction) {
    let workContext = req.user as WorkContext;
    var permissions = await adminApiPost("user/getuserpermissions", usernameQuery(workContext.currentUser.userName)) as any;

    if (permission != null && permissions.find((e: { permissionName: string; }) => e.permissionName.toLowerCase() == permission.toLowerCase())) {
      next();
    }
    else {
      res.sendStatus(401);
    }
  }
}
