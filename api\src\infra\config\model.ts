export interface LogConfig {
  level: string;
}
export interface ApiConfig {
  url: string;
  port: number;
  allowedOrigins: string[];
}

export interface DatabaseConfig {
  server: string;
  name: string;
  schema: string;
  username: string;
  password: string;
  port: number;
  schema_exec: string;
}

export interface AzureADConfig {
  clientid: string;
  clientsecret: string;
  scope: string[];
  audience: string;
  tennantid: string;
  authority: string;
  discovery: string;
  version: string;
}

export interface AzureConfig {
  ad: AzureADConfig;
}

export interface CacheConfig {
  type: string;
  server: string;
  port: number;
  expirationtime: number;
  password: string;
}

export interface Config {
  log: LogConfig;
  environment: string;
  tennantId: string;
  applicationId: string;
  adminapiurl: string;
  app: ApiConfig;
  database: DatabaseConfig;
  azure: AzureConfig;
  cache: CacheConfig;
  adminrequestapiurl: string;
  notificationserviceurl: string;
  baseappurl: string;
  employeedataapiurl: string;
  exec360reportgeneratecount: number;
  pdfserviceurl: string;
}
