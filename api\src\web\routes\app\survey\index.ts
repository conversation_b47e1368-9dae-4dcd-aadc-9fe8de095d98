import express, { Router } from "express";
import { container } from "../../../../container";
import { ResponseController } from "../../../../shared/utils/ResponseController";
import { PermissionType, SurveyType, WorkflowStatus, WorkflowStatus_Cohort } from "../../../../shared/enum/general";
import { HasPermission } from "../../../validators/app.validators";
import { WorkContext } from "../../../../shared/context/workcontext";
import { SurveyService } from "../../../../services/survey.service";
import { InitiateSurvey, SurveyInstanceCohort, SurveyInstanceCohortList, SurveyInstanceDetail, SurveyInstanceList, UserSurvey, filters } from "./model";
import { CommonService } from "../../../../services/common.service";
import _ from "lodash";

export function register(router: Router) {
    var surveyService: SurveyService = container.resolve('surveyService');
    var commonService: CommonService = container.resolve('commonService');

    const innerRouter = express.Router();

    innerRouter.post('/createSurveyinstanceCohort', async function (req, res, next) {
        try {
            const request: SurveyInstanceCohort = req.body;
            var workContext = (req.user as WorkContext).currentUser;

            if (await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [request.root_entity_code])) {
                var resp = await surveyService.createSurveyinstanceCohort(request, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/editSurveyinstanceCohort', async function (req, res, next) {
        try {
            const request: SurveyInstanceCohort = req.body;
            var workContext = (req.user as WorkContext).currentUser;
            var surveyInstanceCohort = await surveyService.getSurveyInstanceCohortById(request.id);
            if (surveyInstanceCohort != null && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [surveyInstanceCohort.root_entity_code])) {
                var resp = await surveyService.editSurveyinstanceCohort(request, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getAllSurveyInstanceCohorts', HasPermission(PermissionType.SurveyInitiate), async function (req, res, next) {
        try {
            const request: filters = req.body;
            var workContext = (req.user as WorkContext).currentUser;
            var result = await surveyService.getAllSurveyInstanceCohorts(request, workContext);
            var resp = new SurveyInstanceCohortList();
            resp.results = result.data;
            resp.total_count = result.total_count;
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveyInstanceCohortById/:id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var cohort = await surveyService.getSurveyInstanceCohortById(Number(req.params.id));
            if (cohort != null &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [cohort.root_entity_code])) {
                return ResponseController.ok(res, cohort);
            } else {
                return ResponseController.unauthorized(res);
            }

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getSurveyInstanceCohortByIds', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohorts = await surveyService.getSurveyInstanceCohortByIds(request.cohort_ids);
            if (cohorts != null &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [cohorts.map(q => q.root_entity_code)])) {
                return ResponseController.ok(res, cohorts);
            } else {
                return ResponseController.unauthorized(res);
            }

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getAllSurveyInstance', HasPermission(PermissionType.SurveyAccess), async function (req, res, next) {
        try {
            const request: filters = req.body;
            var workContext = (req.user as WorkContext).currentUser;
            var cohort = await surveyService.getSurveyInstanceCohortById(request.cohort_id);
            if (cohort != null &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [cohort.root_entity_code])) {
                var result = await surveyService.getAllSurveyInstance(request, workContext);
                var resp = new SurveyInstanceList();
                resp.results = result.data;
                resp.total_count = result.total_count;
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveyInstanceById/:instance_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instance_id));
            if (survey_instance != null && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                return ResponseController.ok(res, survey_instance);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveyInstanceDetailById/:instance_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance = await surveyService.getSurveyInstanceDetailById(Number(req.params.instance_id));
            if (survey_instance != null && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                return ResponseController.ok(res, survey_instance);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getSurveyInstanceDetails/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var survey_instance = await surveyService.getSurveyInstanceDetails(Number(request.instance_id), workContext.userName);
            if (survey_instance != null && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                return ResponseController.ok(res, survey_instance);
            } else {
                return ResponseController.ok(res, true);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveyMasters', async function (req, res, next) {
        try {
            var survey_master = await surveyService.getSurveyMasters();
            return ResponseController.ok(res, survey_master);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/initiateSurvey', async function (req, res, next) {
        try {
            const request: InitiateSurvey = req.body;
            var workContext = (req.user as WorkContext).currentUser;
            var cohort = await surveyService.getSurveyInstanceCohortById(request.cohort_id);
            if (cohort != null && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [cohort.root_entity_code])) {
                var resp = await surveyService.initiateSurvey(request, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveyInstanceForSubmit/:instanceid', async function (req, res, next) {
        try {
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instanceid));
            var workContext = (req.user as WorkContext).currentUser;

            if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress
                && survey_instance.assigned_to?.toLowerCase() === workContext.userName.toLowerCase()) {
                return ResponseController.ok(res, survey_instance);
            } else {
                return ResponseController.unauthorized(res);
            }

        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getUserInprogressSurvey', HasPermission(PermissionType.SurveyInitiate), async function (req, res, next) {
        try {
            const request: UserSurvey = req.body;
            var resp = await surveyService.getUserInprogressSurvey(request.username, request.cohort_id);
            return ResponseController.ok(res, { 'valid': resp });
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSelfInprogressSurvey', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getSelfInprogressSurvey(workContext);
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSelfManagerSurvey/:instanceid', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getSelfManagerSurvey(Number(req.params.instanceid), workContext);
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSelfDirectSurvey/:instanceid', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getSelfDirectSurvey(Number(req.params.instanceid), workContext);
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSelfPeerSurvey/:instanceid', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getSelfPeerSurvey(Number(req.params.instanceid), workContext);
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getAssignedSurvey', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getAssignedSurvey(workContext);
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSelfCompletedSurveyCount', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getSelfCompletedSurveyCount(workContext);
            return ResponseController.ok(res, { result: resp });
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveySectionQuestions/:instanceid', async function (req, res, next) {
        try {
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instanceid));
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(Number(survey_instance?.cohort_id));
            if (survey_instance != null && survey_instance_cohort != null) {
                var resp = await surveyService.getSurveySectionQuestionsByType(survey_instance, survey_instance_cohort.master_id, false);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSurveySectionQuestionsForSubmit/:instanceid', async function (req, res, next) {
        try {
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instanceid));
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(Number(survey_instance?.cohort_id));
            if (survey_instance != null && survey_instance_cohort != null) {
                var resp = await surveyService.getSurveySectionQuestionsByType(survey_instance, survey_instance_cohort.master_id, true);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getCohortSurveySectionQuestions/', async function (req, res, next) {
        try {
            const request: any = req.body;
            var cohort_survey_instance = await surveyService.getSurveyInstanceCohortById(Number(request.cohort_id));
            if (cohort_survey_instance != null) {
                var resp = await surveyService.getSurveySectionQuestions(cohort_survey_instance.master_id);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getAggregateCohortSectionQuestions/', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var cohorts = await surveyService.getSurveyInstanceCohortByIds(request.cohort_ids);
            if (cohorts != null && (cohorts.every(x => x.workflow_status !== WorkflowStatus_Cohort.Open))
                && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyAccess, workContext.userName, [cohorts.map(q => q.root_entity_code)])) {
                var resp = await surveyService.getSurveySectionQuestions(cohorts[0].master_id);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/submitSurvey/:instanceid', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instanceid));
            if (survey_instance && survey_instance.workflow_status === WorkflowStatus.InProgress
                && survey_instance.assigned_to?.toLowerCase() === workContext.userName.toLowerCase()) {
                const request: SurveyInstanceDetail[] = req.body;
                var resp = await surveyService.submitSurvey(request, survey_instance, workContext);
                return ResponseController.ok(res, true);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/sendSurvey', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any[] = req.body;
            var any_submitted_survey = request.filter(x => x.workflow_status !== 'draft')?.length > 0
            if (!any_submitted_survey) {
                var result = await surveyService.sendSurvey(request, workContext);
                return ResponseController.ok(res, result);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/sendManagerSurvey', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var survey_instance = await surveyService.getSurveyInstanceById(request.id);
            if (survey_instance && survey_instance.workflow_status === WorkflowStatus.Draft && survey_instance.assigned_for?.toLowerCase() === workContext.userName?.toLowerCase()) {
                var result = await surveyService.sendManagerSurvey(request, survey_instance, workContext);
                return ResponseController.ok(res, result);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/deleteSurveyInstance/:instanceid', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instanceid));
            if (survey_instance && survey_instance.assigned_for?.toLowerCase() === workContext.userName?.toLowerCase()) {
                await surveyService.deleteSurveyInstance(survey_instance.id, workContext);
                return ResponseController.ok(res, true);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/createSurveyInstance', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            if (commonService.isValidEmail(request.assigned_to)) {
                var result = await surveyService.createSurvey(request, workContext);
                return ResponseController.ok(res, result);
            } else {
                return ResponseController.ok(res, false);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getAllInvitees/:instance_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance = await surveyService.getSurveyInstanceById(Number(req.params.instance_id));
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(Number(survey_instance?.cohort_id));
            if (await commonService.HasAccessByPermissionWithLocationCode(PermissionType.SurveyAccess, workContext.userName, survey_instance_cohort.root_entity_code)) {
                var invitees = await surveyService.getAllInvitees(Number(req.params.instance_id));
                return ResponseController.ok(res, invitees);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getUserArchivedReports', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var archived_reports = await surveyService.getUserArchivedReports(workContext);
            return ResponseController.ok(res, archived_reports);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/editSurvey', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var survey_instance = await surveyService.getSurveyInstanceById(request.id);
            if (survey_instance && survey_instance.workflow_status !== WorkflowStatus.Completed && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.userName, survey_instance)) {
                var result = await surveyService.editSurvey(request, survey_instance, workContext);
                return ResponseController.ok(res, result);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/updateAssignedTo', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            const request: any = req.body;
            var survey_instance = await surveyService.getSurveyInstanceById(request.id);
            if (commonService.isValidEmail(request.assigned_to) && survey_instance
                && survey_instance.workflow_status === WorkflowStatus.Draft && survey_instance.assigned_for.toLowerCase() === workContext.userName.toLowerCase()) {
                var result = await surveyService.updateAssignedTo(request, survey_instance, workContext);
                return ResponseController.ok(res, result);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/downloadInviteesBySurveyId', async function (req, res, next) {
        try {
            var workContext = req.user as WorkContext;
            var survey_instance = await surveyService.getSurveyInstanceById(req.body.survey_id);
            if (survey_instance && await commonService.hasSurveyAccess(PermissionType.SurveyAccess, workContext.currentUser.userName, survey_instance)) {
                var data = await surveyService.downloadInviteesBySurveyId(survey_instance.id);
                res.status(200);
                res.setHeader('Content-Type', 'text/xlsx');
                res.setHeader(
                    'Content-Disposition',
                    'attachment; filename=Survey-Invitees.xlsx'
                );
                res.write(data);
                res.end();
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getUserCohorts', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getUserCohorts(workContext);
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.get('/getSelfReportByCohortId/:cohort_id', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var resp = await surveyService.getSelfReportByCohortId(Number(req.params.cohort_id), workContext);
            return ResponseController.ok(res, resp);
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/deleteParticipant', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance = await surveyService.getSurveyInstanceById(req.body.survey_instance_id);
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(survey_instance?.cohort_id);
            if (survey_instance != null && (survey_instance.workflow_status === WorkflowStatus.InProgress || survey_instance.workflow_status === WorkflowStatus.Submitted) &&
                await commonService.HasAccessByPermissionWithLocationCode(PermissionType.SurveyAccess, workContext.userName, [survey_instance_cohort?.root_entity_code])) {
                var resp = await surveyService.deleteParticipant(survey_instance.id, survey_instance.is_post_survey, workContext);
                return ResponseController.ok(res, { result: resp });
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/deleteCohort', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(Number(req.body.id));
            if (survey_instance_cohort != null && survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.Open &&
                await commonService.HasAccessByPermissionWithLocationCode(PermissionType.SurveyAccess, workContext.userName, [survey_instance_cohort.root_entity_code])) {
                var resp = await surveyService.deleteCohort(survey_instance_cohort.id, workContext);
                return ResponseController.ok(res, { result: resp });
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/getCohotsForMove', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(Number(req.body.cohort_id));
            if ((survey_instance_cohort.workflow_status == WorkflowStatus_Cohort.Open || survey_instance_cohort.workflow_status == WorkflowStatus_Cohort.SurveyCompleted) &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [survey_instance_cohort.root_entity_code])) {
                var resp = await surveyService.getCohotsForMove(survey_instance_cohort, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/moveParticipantsByCohort', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance_cohort_old = await surveyService.getSurveyInstanceCohortById(Number(req.body.old_cohort_id));
            var survey_instance_cohort_new = await surveyService.getSurveyInstanceCohortById(Number(req.body.new_cohort_id));
            if ((survey_instance_cohort_old.workflow_status == WorkflowStatus_Cohort.Open || survey_instance_cohort_old.workflow_status == WorkflowStatus_Cohort.SurveyCompleted) &&
                (survey_instance_cohort_new.workflow_status == WorkflowStatus_Cohort.Open || survey_instance_cohort_new.workflow_status == WorkflowStatus_Cohort.SurveyCompleted) &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [survey_instance_cohort_old.root_entity_code]) &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [survey_instance_cohort_new.root_entity_code])) {
                var resp = await surveyService.moveParticipant(survey_instance_cohort_new, survey_instance_cohort_old, 0, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/moveParticipant', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var surevy_instance_id = Number(req.body.survey_instance_id);
            var survey_instance_cohort_old = await surveyService.getSurveyInstanceCohortById(Number(req.body.old_cohort_id));
            var survey_instance_cohort_new = await surveyService.getSurveyInstanceCohortById(Number(req.body.new_cohort_id));
            if (surevy_instance_id > 0 && (survey_instance_cohort_old.workflow_status == WorkflowStatus_Cohort.Open || survey_instance_cohort_old.workflow_status == WorkflowStatus_Cohort.SurveyCompleted) &&
                (survey_instance_cohort_new.workflow_status == WorkflowStatus_Cohort.Open || survey_instance_cohort_new.workflow_status == WorkflowStatus_Cohort.SurveyCompleted) &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [survey_instance_cohort_old.root_entity_code]) &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [survey_instance_cohort_new.root_entity_code])) {
                var resp = await surveyService.moveParticipant(survey_instance_cohort_new, survey_instance_cohort_old, surevy_instance_id, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/reopenSurveyByCohort', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(Number(req.body.cohort_id));
            if ((survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.SurveyCompleted || survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.Closed) &&
                await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [survey_instance_cohort.root_entity_code])) {
                let is_post_survey = false;
                if (survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.Closed) {
                    is_post_survey = true;
                }
                var resp = await surveyService.reopenSurvey(survey_instance_cohort, req.body.due_date, is_post_survey, 0, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    innerRouter.post('/reopenSurvey', async function (req, res, next) {
        try {
            var workContext = (req.user as WorkContext).currentUser;
            var survey_instance_id = Number(req.body.survey_instance_id);
            var survey_instance_cohort = await surveyService.getSurveyInstanceCohortById(Number(req.body.cohort_id));
            if (survey_instance_id > 0 && await commonService.HasAccessByPermissionWithLocationCodes(PermissionType.SurveyInitiate, workContext.userName, [survey_instance_cohort.root_entity_code])) {
                let is_post_survey = false;
                if (survey_instance_cohort.workflow_status === WorkflowStatus_Cohort.Closed) {
                    is_post_survey = true;
                }
                var resp = await surveyService.reopenSurvey(survey_instance_cohort, req.body.due_date, is_post_survey, survey_instance_id, workContext);
                return ResponseController.ok(res, resp);
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            console.log(error);
            next(error);
        }
    });

    // innerRouter.get('/sample/', async function (req, res, next) {
    //     try {
    //         var cohort = await surveyService.SurveyJob();

    //     } catch (error) {
    //         console.log(error);
    //         next(error);
    //     }
    // });

    return innerRouter;
}
