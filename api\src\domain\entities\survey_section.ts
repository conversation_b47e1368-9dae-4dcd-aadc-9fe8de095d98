import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../infra/config';
import { AppModel } from '../base/AppModel';


export type survey_sectionPk = "id";
export type survey_sectionId = survey_section[survey_sectionPk];

export class survey_section extends AppModel {
  id!: number;
  title?: string;
  detail?: string;
  order_number?: number;
  applicable_survey?: string;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof survey_section {
    this.HasSoftDelete = true;
    survey_section.init({
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true
      },
      title: {
        type: DataTypes.STRING,
        allowNull: true
      },
      detail: {
        type: DataTypes.STRING,
        allowNull: true
      },
      order_number: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      applicable_survey: {
        type: DataTypes.STRING,
        allowNull: true
      },
      active: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      deleted: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      created_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      created_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      modified_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      modified_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
    }, {
      sequelize,
      tableName: 'survey_section',
      schema: env.database.schema,
      timestamps: false,
      indexes: [
        {
          name: "survey_section_pk",
          unique: true,
          fields: [
            { name: "id" },
          ]
        },
      ]
    });
    return survey_section;
  }
}
