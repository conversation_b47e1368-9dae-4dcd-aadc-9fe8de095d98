{"scripts": {"start": "ts-node ./src/app.ts", "build": "tsc -p ."}, "dependencies": {"@azure/msal-node": "^1.3.1", "@types/multer": "^1.4.7", "awilix-express": "^4.0.0", "axios": "^0.21.4", "bcrypt": "^5.0.1", "bluebird": "^3.7.2", "body-parser": "^1.19.0", "compression": "^1.7.4", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "exceljs": "^4.3.0", "express": "^4.17.1", "express-validator": "^6.12.1", "guid-typescript": "^1.0.9", "handlebars": "^4.7.6", "helmet": "^4.6.0", "install": "^0.13.0", "joi": "^17.4.2", "jsonwebtoken": "^8.5.1", "multer": "^1.4.3", "node-cache": "^5.1.2", "passport": "^0.4.1", "passport-azure-ad": "^4.3.1-beta.0", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "puppeteer": "^12.0.1", "puppeteer-core": "^12.0.0", "sequelize": "^6.6.5", "sequelize-typescript": "^2.1.0", "winston": "^3.3.3", "winston-daily-rotate-file": "^4.5.5"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/bluebird": "^3.5.33", "@types/body-parser": "^1.19.1", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.2", "@types/cors": "^2.8.12", "@types/es6-promise": "^3.3.0", "@types/express": "^4.17.13", "@types/express-validator": "^3.0.0", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^8.5.5", "@types/node": "^16.9.1", "@types/node-cache": "^4.2.5", "@types/passport": "^1.0.7", "@types/passport-azure-ad": "^4.3.1", "@types/sequelize": "^4.28.10", "@types/winston": "^2.4.4", "ts-node": "^10.2.1", "typescript": "^4.4.3"}}