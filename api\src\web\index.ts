import express, { Request, Response, NextFunction, Application } from 'express';
import { ApiConfig, Config } from '../infra/config/model';
import { LoggerService } from '../infra/services/loggerservice';
import cors from 'cors';
import * as Routes from './routes'
import * as AppRoutes from './routes/app'

import compression from 'compression';
import helmet from 'helmet';

const app: express.Application = express();

export class Server {

  _logger: LoggerService;
  _config: Config;

  constructor(config: Config//, router
    , logger: LoggerService) {

    this._logger = logger;
    this._config = config;

    app.disable('x-powered-by');
    //this._express.use(router);
  }

  configureApplication(application: Application, apiConfig: ApiConfig) {
    application.use(express.json({limit: '50mb'}));
    application.use(express.urlencoded({
        limit: '50mb',
        extended: true
    }));

    if (this._config.environment != "dev") {
      const options: cors.CorsOptions = {
        origin: apiConfig.allowedOrigins
      };

      application.use(cors(options));
    } else {
      application.use(cors());
    }

    application.use(compression())
    application.use(helmet())
  }

  start() {
    this.configureApplication(app, this._config.app);
    
    Routes.register(app);
    AppRoutes.register(app);

    app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      this._logger.error(error);
      res.status(500).send(error.message);
    });

    app.listen(this._config.app.port, () => {
      this._logger.info(`[p ${process.pid}] Listening at port ${this._config.app.port}`);
    }).on("error", (err) => {
      this._logger.error(err);
      process.exit(1);
      return;
    });



  }
}

