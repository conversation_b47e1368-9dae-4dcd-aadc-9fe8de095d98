import { LoggerService } from "../loggerservice";
import { queryGraphApi } from "./queryconfig";

export class UserSearchService {
    _logger: LoggerService;

    constructor(logger: LoggerService) {
        this._logger = logger;
    }

    public async SearchUser(text: string) {
        var url = `/users?$select=displayName,userPrincipalName,mail,surname,jobTitle,givenName,userType,department&$count=true&$search="userPrincipalName:${text}" OR "mail:${text}" OR "displayName:${text}"&$filter=onPremisesExtensionAttributes%2FextensionAttribute9%20eq%20'DP%20World%20Guest'%20OR%20userType%20eq%20'Member'`;
        var users = await queryGraphApi(url)
        return users;
    }

    public async SearchUserByEmail(text: string) {
        var url = `/users?$select=displayName,userPrincipalName,mail,userType&$filter=userPrincipalName eq '${text}' OR mail eq '${text}'&$top=1`;
        var users = await queryGraphApi(url)
        return users;
    }

    public async SearchUserByUserNameAndEmail(user_name: string, email: string) {
        var url = `/users?$select=displayName,userPrincipalName,mail,surname,jobTitle,givenName,userType,department&$count=true&$filter=userPrincipalName eq '${user_name}' OR mail eq '${user_name}' OR userPrincipalName eq '${email}' OR mail eq '${email}'&$top=1`;
        var users = await queryGraphApi(url);

        users?.forEach(x => {
            x.userPrincipalName = ((x.userType == "Guest") ? x.mail : x.userPrincipalName)
        });
        
        return (users != null && users.length > 0) ? users[0] : null;
    }
    
}