import { env } from './config'
import express, { Request, Response } from 'express';
import passport from 'passport';
import { BearerStrategy, IBearerStrategyOptionWithRequest } from 'passport-azure-ad';
import { WorkContext } from '../shared/context/workcontext';
import { container } from '../container';


const options: IBearerStrategyOptionWithRequest = {
    identityMetadata: `https://${env.azure.ad.authority}/${env.azure.ad.tennantid}/${env.azure.ad.version}/${env.azure.ad.discovery}`,
    issuer: `https://${env.azure.ad.authority}/${env.azure.ad.tennantid}/${env.azure.ad.version}`,
    audience: env.azure.ad.audience,
    validateIssuer: false,
    clientID: env.azure.ad.clientid,
    passReqToCallback: true,
    loggingLevel: "info",
    scope: env.azure.ad.scope,
    loggingNoPII: true,
    clockSkew: 320
};

const bearerStrategy = new BearerStrategy(options, async (req, token, done) => {
    let workContext: WorkContext = container.resolve('workContext');

    var userName = (token.upn == undefined || token.upn == null || token.upn == "") ? token.unique_name : token.upn;
    userName = userName?.toLowerCase();

    workContext.currentUser.fullName = token.name;
    workContext.currentUser.email = "";
    workContext.currentUser.firstName = token.given_name;
    workContext.currentUser.userName = userName;

    done(null, workContext, token);
}
);


export default (router: express.Router) => {

    router.use(passport.initialize());

    passport.use(bearerStrategy);

    // API endpoint exposed
    router.use(passport.authenticate('oauth-bearer', { session: false }), (req: Request, res: Response, next: any) => {
        let user = req.user as WorkContext;
        if (user?.currentUser?.userName != undefined && user?.currentUser?.userName != null && user?.currentUser?.userName != "") {
            return next();
        }
        else {
            res.send(401);
        }
    });


};
