import express, { Router } from "express";
import { container } from "../../../../container";
import { PermissionCheck } from "../../../../shared/utils/permissioncheck";
import { ResponseController } from "../../../../shared/utils/ResponseController";
import multer from 'multer';
import { PermissionType } from "../../../../shared/enum/general";
import { HasPermission } from "../../../validators/app.validators";
import { filters, SetupListModel } from "./model";
import { WorkContext } from "../../../../shared/context/workcontext";
import { ProfileService } from "../../../../services/profile.service";


const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

export function register(router: Router) {
    var profileService: ProfileService = container.resolve('profileService');
    
    const innerRouter = express.Router();
    
    // innerRouter.post('/getAllUser', HasPermission(PermissionType.Manage), async function (req, res, next) {
    //     try{
    //         const request: filters = req.body;
    //         var resp = await profileService.getAllUser(req.body.username);
    //         return ResponseController.ok(res,  resp);
    //     } catch (error) {
    //         console.log(error);
    //         next(error);
    //     }
    // });
   
    return innerRouter;
}
