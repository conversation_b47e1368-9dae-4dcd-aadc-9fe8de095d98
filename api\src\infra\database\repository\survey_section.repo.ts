import { survey_section } from "../../../domain/entities/survey_section";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { isurvey_sectionRepo } from "../../../domain/interfaces/repo/isurvey_section.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class survey_sectionRepo extends BaseRepository<survey_section> implements isurvey_sectionRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_section);
    }
}