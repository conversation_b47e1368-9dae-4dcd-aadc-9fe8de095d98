import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../infra/config';
import { AppModel } from '../base/AppModel';


export type option_listPk = "id";
export type option_listId = option_list[option_listPk];

export class option_list extends AppModel {
  id!: number;
  title!: string;
  has_weightage?: boolean;
  detail?: string;
  option_data?: object;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof option_list {
    option_list.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    has_weightage: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    detail: {
      type: DataTypes.STRING,
      allowNull: false
    },
    option_data: {
      type: DataTypes.JSON,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'option_list',
    schema: env.database.schema,
    timestamps: false,
    indexes: [
      {
        name: "option_list_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return option_list;
  }
}
