import { survey_question } from "../../../domain/entities/survey_question";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { isurvey_questionRepo } from "../../../domain/interfaces/repo/isurvey_question.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class survey_questionRepo extends BaseRepository<survey_question> implements isurvey_questionRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_question);
    }
}