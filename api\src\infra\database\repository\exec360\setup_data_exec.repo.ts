import { setup_data } from "../../../../domain/entities/exec360/setup_data";
import { BaseRepository } from "../../../../domain/interfaces/repo/baserepository";
import { isetup_data_execRepo } from "../../../../domain/interfaces/repo/exec360/isetup_data_exec.repo";
import { Config } from "../../../config/model";
import { LoggerService } from "../../../services/loggerservice";

export class setup_data_execRepo extends BaseRepository<setup_data> implements isetup_data_execRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, setup_data);
    }
}