import _ from "lodash";
import { CacheManager } from "../../cachemanager";
import { LoggerService } from "../loggerservice";

export class CacheStartupService {
    _logger: LoggerService;
    _cacheManager: CacheManager;

    constructor(logger: LoggerService, cacheManager: CacheManager) {
        this._logger = logger;
        this._cacheManager = cacheManager;
    }

    public async PopulateCache() {
        this.PopulateApplicationCache();
    }

    public async PopulateApplicationCache() {
    }
}

