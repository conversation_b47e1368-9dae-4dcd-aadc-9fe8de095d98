
export class filters {
    id: string;
    search_text: string;
    active?: boolean;
    page_number: number;
    page_size: number;
    entity_name: string;
    survey_master_id: number;
    cohort_id: number;
}

export class InitiateSurvey {
    users: any[];
    completed_by: Date;
    survey_type: string;
    cohort_id: number;
}

export class UserSurvey {
    username: string;
    cohort_id: number;
}

export class SurveySection {
    title: string;
    detail: string;
    order_number: number;
    questions: SurveyQuestion[];
}

export class SurveyQuestion {
    id: number;
    title: string;
    detail: string;
    question_type: string;
    order_number: number;
    max_option_selection: number;
    option_data: object;
}

export class SurveyInstance {
    id: number;
    assigned_for: string;
    assigned_to: string;
    completion_date: Date;
    user_status: string;    
    due_date: Date;
    post_program_due_date: Date;
    title: string;
    cohort_id: number;
    assigned_for_data: any;
    assigned_to_data: any;
    cohort_title: string;
    survey_master_id?: number;
    pre_programme_detail: ProgrammDetails;
    post_programme_detail: ProgrammDetails;
    is_post_survey: boolean;
}

export class ProgrammDetails {
    id: number;
    total_reponses: number;
    show_report: boolean;
    user_status: string;
    workflow_status: string;
    total_invitees: number;
    total_submitted: number;
}

export class SurveyInstanceDetail {
    survey_instance_id: number = null;
    survey_question_id: number = null;
    question_type?: string = "";
    answer_data?: any[] = [];
    answer_data_list?: object = null;
}

export class SurveyInstanceList {
    results: SurveyInstance[];
    total_count: number;
}

export class Attachment {
    name: string;
    data: Buffer;
}

export class SurveyInstanceCohort {
    id!: number;
    root_entity_code?: string;
    root_entity_id?: number;
    title?: string;
    detail?: string;
    due_date?: any;
    post_program_survey_start_date?: any;
    post_program_survey_end_date?: any;
    user_status?: string;
    workflow_status?: string;
    master_id?: number;
    self_instance?: any;
    is_survey_inprogress?: boolean;
    total_reponses?: number;
}

export class SurveyInstanceCohortList {
    results: SurveyInstanceCohort[];
    total_count: number;
}

export class DTOSurveyInstance {
    id: number;
    assigned_to: string;
    assigned_for: string;
    survey_type: string;
    user_status: string;
    workflow_status: string;
    assigned_to_data: any;
    assigned_for_data: any;
    master_id: string;
    cohort_id: number;    
    cohort_title: string;
}