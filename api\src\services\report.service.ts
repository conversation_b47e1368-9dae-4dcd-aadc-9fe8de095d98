import { LoggerService } from "../infra/services/loggerservice";
import { CacheManager } from "../infra/cachemanager";
import { Config } from "../infra/config/model";
import _ from "lodash";
import { activeNonDeletedQuery, inQuery, navQuery, nonDeletedQuery, notInQuery, notequalQuery, orQuery, textSearchQuery, textSearchQueryLike, usernameQuery } from "../infra/database/query/general";
import { SurveyType, WorkflowStatus } from "../shared/enum/general";
import fs from 'fs';
import { survey_instanceRepo } from "../infra/database/repository/survey_instance.repo";
import { EmployeeDataService } from "./employeeData.service";
import { survey_sectionRepo } from "../infra/database/repository/survey_section.repo";
import { survey_questionRepo } from "../infra/database/repository/survey_question.repo";
import { option_listRepo } from "../infra/database/repository/option_list.repo";
import { CommonService } from "./common.service";
import { survey_instance_detailRepo } from "../infra/database/repository/survey_instance_detail.repo";
import { ExportReportData, ExportReportDataPrincple, ExportReportDataType, ExportReportDataTypeAnswer, SurveyDataType, SurveyReportCommentData, SurveyReportQuestionData } from "../web/routes/app/report/model";
import { PDFUtil } from "../shared/utils/HtmlToPDF";
import moment from 'moment';
import excel, { Fill } from 'exceljs';
import path from "path";

export class ReportService {

    private templatepath = path.join(__dirname, '..', 'templates');
    private imagespath = path.join(__dirname, '..', 'images');

    _logger: LoggerService;
    _cacheManager: CacheManager;
    _tennantId: string;
    _appId: string;
    _survey_instanceRepo: survey_instanceRepo;
    _survey_instance_detailRepo: survey_instance_detailRepo;
    _survey_sectionRepo: survey_sectionRepo;
    _survey_questionRepo: survey_questionRepo;
    _option_listRepo: option_listRepo;
    _employeeDataService: EmployeeDataService;
    _commonService: CommonService;

    constructor(config: Config, logger: LoggerService, cacheManager: CacheManager, survey_instanceRepo: survey_instanceRepo, survey_instance_detailRepo: survey_instance_detailRepo,
        employeeDataService: EmployeeDataService, survey_sectionRepo: survey_sectionRepo, survey_questionRepo: survey_questionRepo,
        option_listRepo: option_listRepo, commonService: CommonService) {
        this._logger = logger;
        this._cacheManager = cacheManager;
        this._tennantId = config.tennantId;
        this._appId = config.applicationId;
        this._survey_instanceRepo = survey_instanceRepo;
        this._employeeDataService = employeeDataService;
        this._survey_sectionRepo = survey_sectionRepo;
        this._survey_questionRepo = survey_questionRepo;
        this._option_listRepo = option_listRepo;
        this._commonService = commonService;
        this._survey_instance_detailRepo = survey_instance_detailRepo;
    }

    public async getSurveyDataByQuestionId(survey_question_id: any, instance_id: number, assigned_for: string) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', assigned_for),
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "master_id": instance_id }
        });

        var answers_list_datas = this.surveyDataByQuestionId(user_survey_instance, survey_question_id);
        return answers_list_datas;
    }

    public async getCohortSurveyDataByQuestionId(survey_question_id: any, cohort_id: number, is_post_survey: boolean) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": cohort_id },
            ... { "is_post_survey": is_post_survey }
        });

        var answers_list_datas = await this.surveyDataByQuestionId(user_survey_instance, survey_question_id);
        return answers_list_datas;
    }

    public async getAggregateCohortDataByQuestionId(survey_question_id: any, cohort_ids: number[], is_post_survey: boolean) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": inQuery(cohort_ids) },
            ... { "is_post_survey": is_post_survey }
        });

        var answers_list_datas = await this.surveyDataByQuestionId(user_survey_instance, survey_question_id);
        return answers_list_datas;
    }

    public async surveyDataByQuestionId(user_survey_instance: any, survey_question_id: any) {
        var answers_list_datas = [];
        if (user_survey_instance != null) {
            var instance_ids = user_survey_instance.map((q: { id: any; }) => q.id);
            var query = { ...activeNonDeletedQuery, ...{ "survey_instance_id": inQuery(instance_ids), ...{ "survey_question_id": survey_question_id } } };

            let survey_instance_details = await this._survey_instance_detailRepo.find(query);
            if (survey_instance_details != null) {
                var answers_lists = survey_instance_details.map(w => w.answer_data);

                answers_lists?.forEach(a => {
                    answers_list_datas = [...answers_list_datas, ...a];
                });
            }
        }

        return answers_list_datas;
    }

    public async getSurveyDetailDataByQuestionId(request: any, instance_id: number, assigned_for: string) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', assigned_for),
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "master_id": instance_id },
            ...{ "is_post_survey": request.is_post_survey }
        });

        var result = await this.SurveyDetailDataByQuestionId(user_survey_instance, request.question_id)
        return result;
    }

    public async getCohortSurveyDetailDataByQuestionId(survey_question_id: any, cohort_id: number, is_post_survey: any) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": cohort_id },
            ... { "is_post_survey": is_post_survey }
        });

        var result = await this.SurveyDetailDataByQuestionId(user_survey_instance, survey_question_id)
        return result;
    }

    public async getAggregateCohortDetailDataByQuestionId(survey_question_id: any, cohort_ids: number[], is_post_survey: any) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": inQuery(cohort_ids) },
            ... { "is_post_survey": is_post_survey }
        });

        var result = await this.SurveyDetailDataByQuestionId(user_survey_instance, survey_question_id)
        return result;
    }

    private async SurveyDetailDataByQuestionId(user_survey_instance: any, survey_question_id: any) {
        var instance_ids = user_survey_instance.map((q: { id: any; }) => q.id);
        var query = { ...activeNonDeletedQuery, ...{ "survey_instance_id": inQuery(instance_ids), ...{ "survey_question_id": survey_question_id } } };

        let survey_instance_details = await this._survey_instance_detailRepo.find(query);

        let result = new Array<SurveyReportQuestionData>();

        let self_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Self);
        if (self_surveys?.length > 0) {
            var self_instance_ids = self_surveys.map(w => w.id);
            let self_survey_data = new SurveyReportQuestionData();
            self_survey_data.survey_type = SurveyType.Self;
            var self_answers_list = [];
            var self_answers = survey_instance_details.filter(q => self_instance_ids.includes(q.survey_instance_id))?.map(e => e.answer_data);
            self_answers?.forEach(s => {
                self_answers_list = [...self_answers_list, ...s];
            });
            self_survey_data.answers = self_answers_list;
            result.push(self_survey_data);
        }

        let manager_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Manager);
        if (manager_surveys?.length > 0) {
            var manager_instance_ids = manager_surveys.map(w => w.id);
            let manager_survey_data = new SurveyReportQuestionData();
            manager_survey_data.survey_type = SurveyType.Manager;
            var manager_answers_list = [];
            var manager_answers = survey_instance_details.filter(q => manager_instance_ids.includes(q.survey_instance_id))?.map(e => e.answer_data);
            manager_answers?.forEach(s => {
                manager_answers_list = [...manager_answers_list, ...s];
            });
            manager_survey_data.answers = manager_answers_list;
            result.push(manager_survey_data);
        }

        var isother_report = false;

        let direct_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Direct);
        let peers_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Peer);

        if (!direct_surveys || direct_surveys?.length < 3 || !peers_surveys || peers_surveys?.length < 3) {
            isother_report = true;
        }

        if (isother_report) {
            var otherInstanceIds = direct_surveys?.map(w => w.id);
            otherInstanceIds = [...otherInstanceIds, ...peers_surveys?.map(w => w.id)];
            let other_survey_data = new SurveyReportQuestionData();
            other_survey_data.survey_type = SurveyType.Other;
            var other_answers_list = [];
            var other_answers = survey_instance_details.filter(q => otherInstanceIds.includes(q.survey_instance_id))?.map(e => e.answer_data);
            other_answers?.forEach(s => {
                other_answers_list = [...other_answers_list, ...s];
            });
            other_survey_data.answers = other_answers_list;
            result.push(other_survey_data);

        } else {
            var direct_instance_ids = direct_surveys.map(w => w.id);
            let direct_survey_data = new SurveyReportQuestionData();
            direct_survey_data.survey_type = SurveyType.Direct;
            var direct_answers_list = [];
            var direct_answers = survey_instance_details.filter(q => direct_instance_ids.includes(q.survey_instance_id))?.map(e => e.answer_data);
            direct_answers?.forEach(s => {
                direct_answers_list = [...direct_answers_list, ...s];
            });
            direct_survey_data.answers = direct_answers_list;
            result.push(direct_survey_data);

            var peer_instance_ids = peers_surveys.map(w => w.id);
            let peer_survey_data = new SurveyReportQuestionData();
            peer_survey_data.survey_type = SurveyType.Peer;
            var peer_answers_list = [];
            var direct_answers = survey_instance_details.filter(q => peer_instance_ids.includes(q.survey_instance_id))?.map(e => e.answer_data);
            direct_answers?.forEach(s => {
                peer_answers_list = [...peer_answers_list, ...s];
            });
            peer_survey_data.answers = peer_answers_list;
            result.push(peer_survey_data);
        }

        return result;
    }

    public async getUserCompletedSurvey(self_instance_id: number) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...{ 'master_id': self_instance_id },
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }])
        });

        return user_survey;
    }

    public async getUserSurveyInstances(self_instance_id: number) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...{ 'master_id': self_instance_id },
            ...orQuery([{ 'workflow_status': WorkflowStatus.Submitted }, { 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }, { 'workflow_status': WorkflowStatus.InProgress }, { 'workflow_status': WorkflowStatus.Cancelled }, { 'workflow_status': WorkflowStatus.Expired }])
        });

        return user_survey;
    }

    public async getCompletedCohortSurvey(request: any) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...{ 'cohort_id': request.cohort_id },
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
        });

        let pre_instance = user_survey.filter((x: any) => !x.is_post_survey || x.is_post_survey === false);
        let post_instance = user_survey.filter((x: any) => x.is_post_survey || x.is_post_survey === true);

        var result = {
            "pre_completed_survey_count": pre_instance ? pre_instance.count : 0,
            "post_completed_survey_count": post_instance ? post_instance.count : 0
        };

        return result;
    }

    public async getUserCompletedSurveyCount(self_instance_id: number) {
        let user_survey = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...{ 'master_id': self_instance_id },
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }])
        });
        return user_survey ? user_survey.length : 0;
    }

    public async getSurveyCommentsByQuestionId(survey_question_id: any, instance_id: number, assigned_for: string) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', assigned_for),
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "master_id": instance_id }
        });

        var result = await this.surveyCommentsByQuestion(user_survey_instance, survey_question_id);
        return result;
    }

    public async getCohortSurveyCommentsByQuestionId(survey_question_id: any, cohort_id: number) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": cohort_id }
        });

        var result = await this.surveyCommentsByQuestion(user_survey_instance, survey_question_id);
        return result;
    }

    public async surveyCommentsByQuestion(user_survey_instance: any, survey_question_id: any) {
        var instance_ids = user_survey_instance.map((q: { id: any; }) => q.id);
        var query = { ...activeNonDeletedQuery, ...{ "survey_instance_id": inQuery(instance_ids), ...{ "survey_question_id": survey_question_id } } };

        let survey_instance_details = await this._survey_instance_detailRepo.find(query);

        let result = new Array<SurveyReportCommentData>();

        let self_survey = user_survey_instance.find(w => w.survey_type === SurveyType.Self);
        if (self_survey) {
            let self_survey_data = new SurveyReportCommentData();
            self_survey_data.survey_type = SurveyType.Self;
            var self_answers = survey_instance_details.filter(q => q.survey_instance_id === self_survey.id)?.map(e => e.answer_data);
            var self_comments = [];
            self_answers?.forEach(s => {
                s?.forEach(v => {
                    self_comments.push(v.title);
                });
            });
            self_survey_data.comments = self_comments; //self_comments?.length === 1 ? self_comments[0] : self_comments?.join(", ");
            result.push(self_survey_data);
        }

        let manager_survey = user_survey_instance.find(w => w.survey_type === SurveyType.Manager);
        if (manager_survey) {
            let manager_survey_data = new SurveyReportCommentData();
            manager_survey_data.survey_type = SurveyType.Manager;
            var manager_answers = survey_instance_details.filter(q => q.survey_instance_id === manager_survey.id)?.map(e => e.answer_data);
            var manager_comments = [];
            manager_answers?.forEach(s => {
                s?.forEach(v => {
                    manager_comments.push(v.title);
                });
            });
            manager_survey_data.comments = manager_comments;//manager_comments?.length === 1 ? manager_comments[0] : manager_comments?.join(", ");
            result.push(manager_survey_data);
        }

        var isother_report = false;

        let direct_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Direct);
        let peers_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Peer);

        if (!direct_surveys || direct_surveys?.length < 3 || !peers_surveys || peers_surveys?.length < 3) {
            isother_report = true;
        }

        if (isother_report) {
            let other_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Direct || w.survey_type === SurveyType.Peer);
            var otherInstanceIds = other_surveys?.map(w => w.id);

            let other_survey_data = new SurveyReportCommentData();
            other_survey_data.survey_type = SurveyType.Other;
            var other_answers = survey_instance_details.filter(q => otherInstanceIds.includes(q.survey_instance_id))?.map(e => e.answer_data);
            var other_comments = [];
            other_answers?.forEach(s => {
                s?.forEach(v => {
                    other_comments.push(v.title);
                });
            });
            other_survey_data.comments = other_comments;// other_comments?.length === 1 ? other_comments[0] : other_comments?.join(", ");
            result.push(other_survey_data);
        } else {
            let peer_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Peer);
            if (peer_surveys && peer_surveys.length > 0) {
                var peerInstanceIds = peer_surveys?.map(w => w.id);
                let peer_survey_data = new SurveyReportCommentData();
                peer_survey_data.survey_type = SurveyType.Peer;
                var peer_answers = survey_instance_details.filter(q => peerInstanceIds.includes(q.survey_instance_id))?.map(e => e.answer_data);
                var peer_comments = [];
                peer_answers?.forEach(s => {
                    s?.forEach(v => {
                        peer_comments.push(v.title);
                    });
                });
                peer_survey_data.comments = peer_comments;// peer_comments?.length === 1 ? peer_comments[0] : peer_comments?.join(", ");
                result.push(peer_survey_data);
            }

            let direct_surveys = user_survey_instance.filter(w => w.survey_type === SurveyType.Direct);
            if (direct_surveys && direct_surveys.length > 0) {
                var directInstanceIds = direct_surveys?.map(w => w.id);
                let direct_survey_data = new SurveyReportCommentData();
                direct_survey_data.survey_type = SurveyType.Direct;
                var direct_answers = survey_instance_details.filter(q => directInstanceIds.includes(q.survey_instance_id))?.map(e => e.answer_data);
                var direct_comments = [];
                direct_answers?.forEach(s => {
                    s?.forEach(v => {
                        direct_comments.push(v.title);
                    });
                });
                direct_survey_data.comments = direct_comments;// direct_comments?.length === 1 ? direct_comments[0] : direct_comments?.join(", ");
                result.push(direct_survey_data);
            }
        }

        return result;
    }

    public async getUserSurveySummary(assigned_for: string, instance_id: number) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...textSearchQuery('assigned_for', assigned_for),
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "master_id": instance_id }
        });

        var survey_summary = await this.getSurveySummary(user_survey_instance);
        return survey_summary;
    }

    public async getCohortSurveySummary(cohort_id: number, is_post_survey: any) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": cohort_id },
            ... { "is_post_survey": is_post_survey }
        });

        var survey_summary = await this.getSurveySummary(user_survey_instance);
        return survey_summary;
    }

    public async getAggregateCohortSummary(cohort_ids: number[], is_post_survey: any) {
        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": inQuery(cohort_ids) },
            ... { "is_post_survey": is_post_survey }
        });

        var survey_summary = await this.getSurveySummary(user_survey_instance);
        return survey_summary;
    }

    private async getSurveySummary(user_survey_instance: any) {
        var survey_summary = new SurveyDataType();

        survey_summary.Self = user_survey_instance.filter((w: any) => w.survey_type === SurveyType.Self)?.length;
        survey_summary.Manager = user_survey_instance.filter((w: any) => w.survey_type === SurveyType.Manager)?.length;
        var direct_surveys = user_survey_instance.filter((w: any) => w.survey_type === SurveyType.Direct)?.length;
        var peers_surveys = user_survey_instance.filter((w: any) => w.survey_type === SurveyType.Peer)?.length;
        if (direct_surveys < 3 || peers_surveys < 3) {
            survey_summary.Other = (direct_surveys ?? 0) + (peers_surveys ?? 0);
        }
        else {
            survey_summary.Direct = direct_surveys;
            survey_summary.Peer = peers_surveys;
        }
        survey_summary.Total = survey_summary.Self + survey_summary.Manager + (survey_summary.Direct ?? 0) + (survey_summary.Peer ?? 0) + (survey_summary.Other ?? 0);

        return survey_summary;
    }

    public async printUserSurveyReport(data: any, survey_instance: any) {
        var attachment_buffer: any;
        var template_name = survey_instance.survey_master_id === 1 ? 'surevy-report' : 'surevy-report-way2';
        var pdfdata = await this.generateUserHtmlPdf(data, survey_instance?.assigned_for_data?.full_name, template_name);
        let file: any = { content: pdfdata.html };
        let options = { headless: true, args: ["--disable-gpu", "--disable-dev-shm-usage", "--disable-setuid-sandbox", "--no-sandbox"] };

        await PDFUtil.generatePdf(file, options, pdfdata.header_template, pdfdata.footer_template).then(pdfBuffer => {
            attachment_buffer = pdfBuffer;
        });
        return await attachment_buffer;
    }

    public async printCohortSurveyReport(data: any, cohort_survey_instance: any) {
        var attachment_buffer: any;
        var template_name = 'surevy-report-cohort';
        var pdfdata = await this.generateCohortHtmlPdf(data, cohort_survey_instance.title, template_name);
        let file: any = { content: pdfdata.html };
        let options = { headless: true, args: ["--disable-gpu", "--disable-dev-shm-usage", "--disable-setuid-sandbox", "--no-sandbox"] };

        await PDFUtil.generatePdf(file, options, pdfdata.header_template, pdfdata.footer_template).then(pdfBuffer => {
            attachment_buffer = pdfBuffer;
        });
        return await attachment_buffer;
        // return await this.generateCohortPDF(data, cohort_survey_instance)
    }

    public async printAggregateCohortReport(data: any, cohorts: any) {
        var attachment_buffer: any;
        var template_name = 'surevy-report-cohort';
        var cohort_titles = cohorts.map(x => x.title)?.join(', ');
        var pdfdata = await this.generateCohortHtmlPdf(data, cohort_titles, template_name);
        let file: any = { content: pdfdata.html };
        let options = { headless: true, args: ["--disable-gpu", "--disable-dev-shm-usage", "--disable-setuid-sandbox", "--no-sandbox"] };

        await PDFUtil.generatePdf(file, options, pdfdata.header_template, pdfdata.footer_template).then(pdfBuffer => {
            attachment_buffer = pdfBuffer;
        });
        return await attachment_buffer;
        // return await this.generateCohortPDF(data, cohort_survey_instance)
    }

    // private async generatePDF(data: any, survey_instance: any) {
    //     var attachment_buffer: any;
    //     var template_name = survey_instance.survey_master_id === 1 ? 'surevy-report' : 'surevy-report-way2';
    //     var pdfdata = await this.generateHtmlPdf(data, survey_instance?.assigned_for_data?.full_name, template_name);
    //     let file: any = { content: pdfdata.html };
    //     let options = { headless: true, args: ["--disable-gpu", "--disable-dev-shm-usage", "--disable-setuid-sandbox", "--no-sandbox"] };

    //     await PDFUtil.generatePdf(file, options, pdfdata.header_template, pdfdata.footer_template).then(pdfBuffer => {
    //         attachment_buffer = pdfBuffer;
    //     });
    //     return attachment_buffer;
    // }

    // private async generateCohortPDF(data: any, cohort_survey_instance: any) {
    //     var attachment_buffer: any;
    //     var template_name = 'surevy-report-cohort';
    //     var pdfdata = await this.generateHtmlPdf(data, cohort_survey_instance.title, template_name);
    //     let file: any = { content: pdfdata.html };
    //     let options = { headless: true, args: ["--disable-gpu", "--disable-dev-shm-usage", "--disable-setuid-sandbox", "--no-sandbox"] };

    //     await PDFUtil.generatePdf(file, options, pdfdata.header_template, pdfdata.footer_template).then(pdfBuffer => {
    //         attachment_buffer = pdfBuffer;
    //     });
    //     return attachment_buffer;
    // }

    public async generateUserHtmlPdf(data: any, participantName: any, filename: string) {

        var header_img = this.GetImages("headerImg.png");

        var html = fs.readFileSync(this.templatepath + '/' + filename + '.html', 'utf8');
        html = html.replace('{#PilateDemi#}', this.GetFontBase64("Pilat\ Demi.ttf"));
        html = html.replace('{#PilateLight#}', this.GetFontBase64("Pilat\ Light.ttf"));
        html = html.replace('{#PilateHeavy#}', this.GetFontBase64("Pilat\ Wide\ Heavy.ttf"));
        html = html.replace('{#headerImg#}', header_img);
        html = html.replace('{#page1Footer#}', this.GetImages("page1Footer.jpg"));
        html = html.replace('{#page2Footer#}', this.GetImages("page2Footer.jpg"));
        html = html.replace('{#page3FooterImg#}', this.GetImages("page3Footer.png"));
        html = html.replace('{#footerImg#}', this.GetImages("footerImg.jpg"));

        html = html.replace('{#participantName#}', participantName);
        var current_date = moment.utc(new Date).format("MMM D, YYYY").toLocaleString();
        html = html.replace('{#reportGeneratedOn#}', current_date);

        if (data.chart_pre_behaviour) {
            var pre_behaviour = '<div style="width:1125px;padding-top: 25px; padding-bottom: 50px;"><img src="' + data.chart_pre_behaviour + '" width="100%" /></div>';
            html = html.replace('{#chartPreBehaviourDiv#}', pre_behaviour);
        } else {
            html = html.replace('{#chartPreBehaviourDiv#}', '');
        }

        if (data.chart_post_behaviour) {
            var post_behaviour = '<div style="width:1125px;padding-top: 25px; padding-bottom: 50px;"><img src="' + data.chart_post_behaviour + '" width="100%" /></div>';
            html = html.replace('{#chartPostBehaviourDiv#}', post_behaviour);
        } else {
            html = html.replace('{#chartPostBehaviourDiv#}', '');
        }

        let primary_focused_questions = '';
        let index = 0;
        data.primary_questions?.forEach((a: any) => {
            primary_focused_questions += '<div style="display: flex;flex-wrap: wrap;margin: -8px;"><div style="box-sizing: border-box;padding: 8px;width: 100%;"><div><h3 style="font-weight:bolder; font-size: 20px; "> ' + a.title + '</h3><p style="font-size:18px; text-align: justify; line-height: 26px;">' + a.detail + '</div></div>';
            if (a.pre_chartdata) {
                primary_focused_questions += '<div style="box-sizing: border-box;padding: 8px;width: 50%;">';
                if (a.post_chartdata) {
                    primary_focused_questions += '<h4 style="text-align:center">Pre Programme</h4>';
                }
                primary_focused_questions += '<img src="' + a.pre_chartdata + '" width="100%"/></div>';
            }
            if (a.post_chartdata) {
                primary_focused_questions += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><h4 style="text-align:center">Post Programme</h4><img src="' + a.post_chartdata + '" width="100%"/></div>';
            }
            primary_focused_questions += '</div>';
            index++;
        });
        html = html.replace('{#primaryFocusedQuestionsHtml#}', primary_focused_questions);

        let secoundary_focused_questions = '';
        data.secoundary_questions?.forEach((a: any) => {
            secoundary_focused_questions += '<div style="display: flex;flex-wrap: wrap;margin: -8px; margin-bottom:30px!important"><div style="box-sizing: border-box;padding: 8px;width: 100%;"><div><h3 style="font-weight:bolder; font-size: 20px; "> ' + a.title + '</h3><p style="font-size:18px; text-align: justify; line-height: 26px;">' + a.detail + '</div></div>';
            if (a.pre_chartdata) {
                secoundary_focused_questions += '<div style="box-sizing: border-box;padding: 8px;width: 50%;">';
                if (a.post_chartdata) {
                    secoundary_focused_questions += '<h4 style="text-align:center">Pre Programme</h4>';
                }
                secoundary_focused_questions += '<img src="' + a.pre_chartdata + '" width="100%"/></div>';
            }
            if (a.post_chartdata) {
                secoundary_focused_questions += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><h4 style="text-align:center">Post Programme</h4><img src="' + a.post_chartdata + '" width="100%"/></div>';
            }
            secoundary_focused_questions += '</div>';
            index++;
        });
        html = html.replace('{#secoundaryFocusedQuestionsHtml#}', secoundary_focused_questions);

        let feedback_tablehtml = '<table class="tableBorder" style=" border-spacing: 0px; width: 100%;"><tbody>';
        if (data.post_survey_summary) {
            feedback_tablehtml += '<tr style="height: 80px;"><td style="width:40%; padding-left: 10px; font-size:24px; background-color: #cccccc;"><h3>Respondents Category</h3></td><td style="width:30%; padding-left: 10px; font-size:24px;"><h3>Pre-Programme Responses</h3></td><td style="width:30%; padding-left: 10px; font-size:24px;"><h3>Post-Programme Responses</h3></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Self-Assessment</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.self + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.self ? data.post_survey_summary?.self : '') + '</p></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Manager</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.manager + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.manager ? data.post_survey_summary?.manager : '') + '</p></td></tr>';
            if ((data.pre_survey_summary?.direct && data.pre_survey_summary?.direct !== 'N/A') || (data.post_survey_summary?.direct && data.post_survey_summary?.direct !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Direct Reports</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.direct + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.direct ? data.post_survey_summary?.direct : '') + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.peer && data.pre_survey_summary?.peer !== 'N/A') || (data.post_survey_summary?.peer && data.post_survey_summary?.peer !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Peers</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.peer + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.peer ? data.post_survey_summary?.peer : '') + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.other && data.pre_survey_summary?.other !== 'N/A') || (data.post_survey_summary?.other && data.post_survey_summary?.other !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Other</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.other + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.other ? data.post_survey_summary?.other : '') + '</p></td></tr>';
            }
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;"><strong>Total Respondents</strong></p></td><td><p style="font-size:18px; font-weight: bold;margin-left: 10px;">' + data.pre_survey_summary?.total + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.total ? data.post_survey_summary?.total : '') + '</p></td></tr>'
        } else {
            feedback_tablehtml += '<tr style="height: 80px;"><td style="width:40%; padding-left: 10px; font-size:24px; background-color: #cccccc;"><h3>Respondents Category</h3></td><td style="width:30%; padding-left: 10px; font-size:24px;"><h3>Pre-Programme Responses</h3></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Self-Assessment</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.self + '</p></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Manager</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.manager + '</p></td></tr>';
            if ((data.pre_survey_summary?.direct && data.pre_survey_summary?.direct !== 'N/A') || (data.post_survey_summary?.direct && data.post_survey_summary?.direct !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Direct Reports</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.direct + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.peer && data.pre_survey_summary?.peer !== 'N/A') || (data.post_survey_summary?.peer && data.post_survey_summary?.peer !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Peers</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.peer + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.other && data.pre_survey_summary?.other !== 'N/A') || (data.post_survey_summary?.other && data.post_survey_summary?.other !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Other</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.other + '</p></td></tr>';
            }
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;"><strong>Total Respondents</strong></p></td><td><p style="font-size:18px; font-weight: bold;margin-left: 10px;">' + data.pre_survey_summary?.total + '</p></td></tr>'
        }

        feedback_tablehtml += '</tbody></table>';
        html = html.replace('{#feedbackTableHtml#}', feedback_tablehtml);

        let p1tablehtml = '<div style="display: flex;flex-wrap: wrap;margin: -8px;">';
        if (data.chart_principle_doingwell_self) {
            p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_self + '" width="100%"/></div>'
        }
        if (data.chart_principle_doingwell_manager) {
            p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_manager + '" width="100%"/></div>'
        }
        if (!data.chart_principle_doingwell_other) {
            if (data.chart_principle_doingwell_direct) {
                p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_direct + '" width="100%"/></div>'
            }
            if (data.chart_principle_doingwell_peer) {
                p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_peer + '" width="100%"/></div>'
            }
        } else {
            if (data.chart_principle_doingwell_other) {
                p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_other + '" width="100%"/></div>'
            }
        }
        p1tablehtml += '</div>';
        html = html.replace('{#principleDoingWellHtml#}', p1tablehtml);

        let p2tablehtml = '<div style="display: flex;flex-wrap: wrap;margin: -8px;">';
        if (data.chart_principle_improvment_self) {
            p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_self + '" width="100%"/></div>'
        }
        if (data.chart_principle_improvment_manager) {
            p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_manager + '" width="100%"/></div>'
        }
        if (!data.chart_principle_improvment_other) {
            if (data.chart_principle_improvment_direct) {
                p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_direct + '" width="100%"/></div>'
            }
            if (data.chart_principle_improvment_peer) {
                p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_peer + '" width="100%"/></div>'
            }
        } else {
            if (data.chart_principle_improvment_other) {
                p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_other + '" width="100%"/></div>'
            }
        }
        p2tablehtml += '</div>';

        html = html.replace('{#principleImprovementHtml#}', p2tablehtml);

        var behaviourCommentsHtml = '<table style="width:100%;"><tbody>';
        var self_behaviour_comment = '';
        data.behaviour_comments_self?.forEach((item, index) => {
            self_behaviour_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
        });
        behaviourCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Self</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + self_behaviour_comment + '</div></div></td></tr>';

        var manager_behaviour_comment = '';
        data.behaviour_comments_manager?.forEach((item, index) => {
            manager_behaviour_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
        });
        behaviourCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Manager</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + manager_behaviour_comment + '</div></div></td></tr>';

        if (!data.behaviour_comments_other) {
            var direct_behaviour_comment = '';
            data.behaviour_comments_direct?.forEach((item, index) => {
                direct_behaviour_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            behaviourCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Direct</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + direct_behaviour_comment + '</div></div></td></tr>';

            var peer_behaviour_comment = '';
            data.behaviour_comments_peer?.forEach((item, index) => {
                peer_behaviour_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            behaviourCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Peer</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + peer_behaviour_comment + '</div></div></td></tr>';
        } else {
            var other_behaviour_comment = '';
            data.behaviour_comments_other?.forEach((item, index) => {
                other_behaviour_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            behaviourCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Other</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + other_behaviour_comment + '</div></div></td></tr>';
        }
        behaviourCommentsHtml += '</tbody></table>';

        html = html.replace('{#behaviourCommentsHtml#}', behaviourCommentsHtml);

        var effectiveCommentsHtml = '<table style="width:100%;"><tbody>';

        var self_effective_comment = '';
        data.effective_comments_self?.forEach((item, index) => {
            self_effective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
        });
        effectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Self</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + self_effective_comment + '</div></div></td></tr>';

        var manager_effective_comment = '';
        data.effective_comments_manager?.forEach((item, index) => {
            manager_effective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
        });
        effectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Manager</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + manager_effective_comment + '</div></div></td></tr>';

        if (!data.effective_comments_other) {
            var direct_effective_comment = '';
            data.effective_comments_direct?.forEach((item, index) => {
                direct_effective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            effectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Direct</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + direct_effective_comment + '</div></div></td></tr>';

            var peer_effective_comment = '';
            data.effective_comments_peer?.forEach((item, index) => {
                peer_effective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            effectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Peer</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + peer_effective_comment + '</div></div></td></tr>';
        } else {
            var other_effective_comment = '';
            data.effective_comments_other?.forEach((item, index) => {
                other_effective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            effectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Other</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + other_effective_comment + '</div></div></td></tr>';
        }
        effectiveCommentsHtml += '</tbody></table>';

        html = html.replace('{#effectiveCommentsHtml#}', effectiveCommentsHtml);

        var inEffectiveCommentsHtml = '<table style="width:100%;"><tbody>';
        var self_ineffective_comment = '';
        data.ineffective_comments_self?.forEach((item, index) => {
            self_ineffective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
        });
        inEffectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Self</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + self_ineffective_comment + '</div></div></td></tr>';

        var manager_ineffective_comment = '';
        data.ineffective_comments_manager?.forEach((item, index) => {
            manager_ineffective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
        });
        inEffectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Manager</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + manager_ineffective_comment + '</div></div></td></tr>';

        if (!data.ineffective_comments_other) {
            var direct_ineffective_comment = '';
            data.ineffective_comments_direct?.forEach((item, index) => {
                direct_ineffective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            inEffectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Direct</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + direct_ineffective_comment + '</div></div></td></tr>';

            var peer_ineffective_comment = '';
            data.ineffective_comments_peer?.forEach((item, index) => {
                peer_ineffective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            inEffectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Peer</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + peer_ineffective_comment + '</div></div></td></tr>';
        } else {
            var other_ineffective_comment = '';
            data.ineffective_comments_other?.forEach((item, index) => {
                other_ineffective_comment += '<div style="border-bottom: 1px dotted #ababab;padding:5px;"><span style="margin-right:10px;">' + (index + 1) + '.</span><span>' + item + '</span></div>';
            });
            inEffectiveCommentsHtml += '<tr><td style="border: none;"><div style="width:100%;"><h3>Other</h3><div style="border-top: 1px solid #000000;min-height: 60px;font-size: 18px;">' + other_ineffective_comment + '</div></div></td></tr>';
        }
        inEffectiveCommentsHtml += '</tbody></table>';

        html = html.replace('{#inEffectiveCommentsHtml#}', inEffectiveCommentsHtml);


        var header_template = '<div style="width: 100%;  height: 300px;"><img src="' + header_img + '" width="100%" /></div>';

        return { html: html, header_template: header_template, footer_template: null };
    }

    public async generateCohortHtmlPdf(data: any, participantName: any, filename: string) {

        var header_img = this.GetImages("headerImg.png");

        //var html = fs.readFileSync('./src/templates/' + filename + '.html', 'utf8');
        var html = fs.readFileSync(this.templatepath + '/' + filename + '.html', 'utf8');
        html = html.replace('{#PilateDemi#}', this.GetFontBase64("Pilat\ Demi.ttf"));
        html = html.replace('{#PilateLight#}', this.GetFontBase64("Pilat\ Light.ttf"));
        html = html.replace('{#PilateHeavy#}', this.GetFontBase64("Pilat\ Wide\ Heavy.ttf"));
        html = html.replace('{#headerImg#}', header_img);
        html = html.replace('{#page1Footer#}', this.GetImages("page1Footer.png"));
        html = html.replace('{#page2Footer#}', this.GetImages("page2Footer.png"));
        html = html.replace('{#page3FooterImg#}', this.GetImages("page3Footer.png"));
        html = html.replace('{#footerImg#}', this.GetImages("footerImg.jpg"));

        html = html.replace('{#participantName#}', participantName);
        var current_date = moment.utc(new Date).format("MMM D, YYYY").toLocaleString();
        html = html.replace('{#reportGeneratedOn#}', current_date);

        if (data.chart_pre_behaviour) {
            var pre_behaviour = '<div style="width:1125px;padding-top: 25px; padding-bottom: 50px;"><img src="' + data.chart_pre_behaviour + '" width="100%" /></div>';
            html = html.replace('{#chartPreBehaviourDiv#}', pre_behaviour);
        } else {
            html = html.replace('{#chartPreBehaviourDiv#}', '');
        }

        if (data.chart_post_behaviour) {
            var post_behaviour = '<div style="width:1125px;padding-top: 25px; padding-bottom: 50px;"><img src="' + data.chart_post_behaviour + '" width="100%" /></div>';
            html = html.replace('{#chartPostBehaviourDiv#}', post_behaviour);
        } else {
            html = html.replace('{#chartPostBehaviourDiv#}', '');
        }

        let questions = '';
        let index = 0;
        data.questions?.forEach((a: any) => {
            questions += '<div style="display: flex;flex-wrap: wrap;margin: -8px; margin-bottom:15px!important"><div style="box-sizing: border-box;padding: 8px;width: 100%;"><div><h3 style="font-weight:bolder; font-size: 20px; "> ' + a.title + '</h3><p style="font-size:18px; text-align: justify; line-height: 26px;">' + a.detail + '</div></div>';
            if (a.pre_chartdata) {
                questions += '<div style="box-sizing: border-box;padding: 8px;width: 50%;">';
                if (a.post_chartdata) {
                    questions += '<h4 style="text-align:center">Pre Programme</h4>';
                }
                questions += '<img src="' + a.pre_chartdata + '" width="100%"/></div>';
            }
            if (a.post_chartdata) {
                questions += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><h4 style="text-align:center">Post Programme</h4><img src="' + a.post_chartdata + '" width="100%"/></div>';
            }
            questions += '</div>';
            index++;
        });
        html = html.replace('{#QuestionsHtml#}', questions);

        let feedback_tablehtml = '<table class="tableBorder" style=" border-spacing: 0px; width: 100%;"><tbody>';
        if (data.post_survey_summary) {
            feedback_tablehtml += '<tr style="height: 80px;"><td style="width:40%; padding-left: 10px; font-size:24px; background-color: #cccccc;"><h3>Respondents Category</h3></td><td style="width:30%; padding-left: 10px; font-size:24px;"><h3>Pre-Programme Responses</h3></td><td style="width:30%; padding-left: 10px; font-size:24px;"><h3>Post-Programme Responses</h3></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Self-Assessment</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.self + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.self ? data.post_survey_summary?.self : '') + '</p></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Manager</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.manager + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.manager ? data.post_survey_summary?.manager : '') + '</p></td></tr>';
            if ((data.pre_survey_summary?.direct && data.pre_survey_summary?.direct !== 'N/A') || (data.post_survey_summary?.direct && data.post_survey_summary?.direct !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Direct Reports</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.direct + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.direct ? data.post_survey_summary?.direct : '') + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.peer && data.pre_survey_summary?.peer !== 'N/A') || (data.post_survey_summary?.peer && data.post_survey_summary?.peer !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Peers</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.peer + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.peer ? data.post_survey_summary?.peer : '') + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.other && data.pre_survey_summary?.other !== 'N/A') || (data.post_survey_summary?.other && data.post_survey_summary?.other !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Other</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.other + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.other ? data.post_survey_summary?.other : '') + '</p></td></tr>';
            }
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;"><strong>Total Respondents</strong></p></td><td><p style="font-size:18px; font-weight: bold;margin-left: 10px;">' + data.pre_survey_summary?.total + '</p></td><td><p style="font-size:18px;margin-left: 10px;">' + (data.post_survey_summary?.total ? data.post_survey_summary?.total : '') + '</p></td></tr>'
        } else {
            feedback_tablehtml += '<tr style="height: 80px;"><td style="width:40%; padding-left: 10px; font-size:24px; background-color: #cccccc;"><h3>Respondents Category</h3></td><td style="width:30%; padding-left: 10px; font-size:24px;"><h3>Pre-Programme Responses</h3></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Self-Assessment</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.self + '</p></td></tr>';
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Manager</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.manager + '</p></td></tr>';
            if ((data.pre_survey_summary?.direct && data.pre_survey_summary?.direct !== 'N/A') || (data.post_survey_summary?.direct && data.post_survey_summary?.direct !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Direct Reports</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.direct + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.peer && data.pre_survey_summary?.peer !== 'N/A') || (data.post_survey_summary?.peer && data.post_survey_summary?.peer !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Peers</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.peer + '</p></td></tr>';
            }
            if ((data.pre_survey_summary?.other && data.pre_survey_summary?.other !== 'N/A') || (data.post_survey_summary?.other && data.post_survey_summary?.other !== 'N/A')) {
                feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;">Other</p></td><td><p style="font-size:18px;margin-left: 10px;">' + data.pre_survey_summary?.other + '</p></td></tr>';
            }
            feedback_tablehtml += '<tr style="height: 80px;"><td style="padding-left: 10px; padding-right: 10px;"><p style="font-size:18px;"><strong>Total Respondents</strong></p></td><td><p style="font-size:18px; font-weight: bold;margin-left: 10px;">' + data.pre_survey_summary?.total + '</p></td></tr>'
        }

        feedback_tablehtml += '</tbody></table>';
        html = html.replace('{#feedbackTableHtml#}', feedback_tablehtml);

        let p1tablehtml = '<div style="display: flex;flex-wrap: wrap;margin: -8px;">';
        if (data.chart_principle_doingwell_self) {
            p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_self + '" width="100%"/></div>'
        }
        if (data.chart_principle_doingwell_manager) {
            p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_manager + '" width="100%"/></div>'
        }
        if (!data.chart_principle_doingwell_other) {
            if (data.chart_principle_doingwell_direct) {
                p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_direct + '" width="100%"/></div>'
            }
            if (data.chart_principle_doingwell_peer) {
                p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_peer + '" width="100%"/></div>'
            }
        } else {
            if (data.chart_principle_doingwell_other) {
                p1tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_doingwell_other + '" width="100%"/></div>'
            }
        }
        p1tablehtml += '</div>';
        html = html.replace('{#principleDoingWellHtml#}', p1tablehtml);

        let p2tablehtml = '<div style="display: flex;flex-wrap: wrap;margin: -8px;">';
        if (data.chart_principle_improvment_self) {
            p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_self + '" width="100%"/></div>'
        }
        if (data.chart_principle_improvment_manager) {
            p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_manager + '" width="100%"/></div>'
        }
        if (!data.chart_principle_improvment_other) {
            if (data.chart_principle_improvment_direct) {
                p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_direct + '" width="100%"/></div>'
            }
            if (data.chart_principle_improvment_peer) {
                p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_peer + '" width="100%"/></div>'
            }
        } else {
            if (data.chart_principle_improvment_other) {
                p2tablehtml += '<div style="box-sizing: border-box;padding: 8px;width: 50%;"><img src="' + data.chart_principle_improvment_other + '" width="100%"/></div>'
            }
        }
        p2tablehtml += '</div>';

        html = html.replace('{#principleImprovementHtml#}', p2tablehtml);

        var header_template = '<div style="width: 100%;  height: 300px;"><img src="' + header_img + '" width="100%" /></div>';

        return { html: html, header_template: header_template, footer_template: null };
    }

    public async exportAggregateCohortReport(request: any) {
        // Create a new workbook and add a worksheet
        const workbook = new excel.Workbook();

        const worksheet = workbook.addWorksheet('Survey-Questions');

        // Define the main headers
        worksheet.getRow(1).values = [
            'Questions',
            'Pre Program', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
            'Post Program', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''
        ];

        worksheet.mergeCells('B1:Q1'); // Merge "Pre Program"
        worksheet.mergeCells('R1:AG1'); // Merge "Post Program"

        // Define the subheaders
        worksheet.getRow(2).values = [
            'Questions',
            'Self(%)', '', '', '', 'Manager(%)', '', '', '', 'Direct(%)', '', '', '', 'Peer(%)', '', '', '',
            'Self(%)', '', '', '', 'Manager(%)', '', '', '', 'Direct(%)', '', '', '', 'Peer(%)', '', '', ''
        ];

        // // Merge the cells for the subheaders
        worksheet.mergeCells('B2:E2'); // Merge "Self" under "Pre Program"
        worksheet.mergeCells('F2:I2'); // Merge "Manager" under "Pre Program"
        worksheet.mergeCells('J2:M2'); // Merge "Direct" under "Pre Program"
        worksheet.mergeCells('N2:Q2'); // Merge "Peer" under "Pre Program"

        worksheet.mergeCells('R2:U2');   // Merge "Self" under "Post Program"
        worksheet.mergeCells('V2:Y2');   // Merge "Manager" under "Post Program"
        worksheet.mergeCells('Z2:AC2');  // Merge "Direct" under "Post Program"
        worksheet.mergeCells('AD2:AG2'); // Merge "Peer" under "Post Program"

        // Define the sub-subheaders (3rd level)
        worksheet.getRow(3).values = [
            'Questions',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate',
            'Start Doing It', 'Do More Consistently', 'Already Doing Well', 'Unable to Evaluate'
        ];

        worksheet.mergeCells('A1:A3');

        worksheet.columns = [
            { key: 'questions', width: 70 },
            { key: 'pre_program_sdi_self', width: 20 },
            { key: 'pre_program_dmc_self', width: 20 },
            { key: 'pre_program_adw_self', width: 20 },
            { key: 'pre_program_ute_self', width: 20 },

            { key: 'pre_program_sdi_manager', width: 20 },
            { key: 'pre_program_dmc_manager', width: 20 },
            { key: 'pre_program_adw_manager', width: 20 },
            { key: 'pre_program_ute_manager', width: 20 },

            { key: 'pre_program_sdi_direct', width: 20 },
            { key: 'pre_program_dmc_direct', width: 20 },
            { key: 'pre_program_adw_direct', width: 20 },
            { key: 'pre_program_ute_direct', width: 20 },

            { key: 'pre_program_sdi_peer', width: 20 },
            { key: 'pre_program_dmc_peer', width: 20 },
            { key: 'pre_program_adw_peer', width: 20 },
            { key: 'pre_program_ute_peer', width: 20 },

            { key: 'post_program_sdi_self', width: 20 },
            { key: 'post_program_dmc_self', width: 20 },
            { key: 'post_program_adw_self', width: 20 },
            { key: 'post_program_ute_self', width: 20 },

            { key: 'post_program_sdi_manager', width: 20 },
            { key: 'post_program_dmc_manager', width: 20 },
            { key: 'post_program_adw_manager', width: 20 },
            { key: 'post_program_ute_manager', width: 20 },

            { key: 'post_program_sdi_direct', width: 20 },
            { key: 'post_program_dmc_direct', width: 20 },
            { key: 'post_program_adw_direct', width: 20 },
            { key: 'post_program_ute_direct', width: 20 },

            { key: 'post_program_sdi_peer', width: 20 },
            { key: 'post_program_dmc_peer', width: 20 },
            { key: 'post_program_adw_peer', width: 20 },
            { key: 'post_program_ute_peer', width: 20 },
        ];

        const headerFill: Fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFCCCCFF' }
        };

        const centerAlignment: any = { vertical: 'middle', horizontal: 'center' };

        const headerBorder: any = {
            top: { style: 'thin', color: { argb: '3e3c90' } },
            left: { style: 'thin', color: { argb: '3e3c90' } },
            bottom: { style: 'thin', color: { argb: '3e3c90' } },
            right: { style: 'thin', color: { argb: '3e3c90' } }
        };

        for (let i = 1; i <= 3; i++) {
            const row = worksheet.getRow(i);
            row.alignment = centerAlignment;
            row.font = { bold: true };
            row.fill = headerFill;
            row.eachCell({ includeEmpty: true }, (cell) => {
                cell.border = headerBorder;
            });
        };

        var questions = await this._survey_questionRepo.find({ ...activeNonDeletedQuery, ...{ "survey_master_id": request.survey_master_id } });

        let user_survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery, ...orQuery([{ 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.Archived }]),
            ...{ "cohort_id": inQuery(request.cohort_ids) }
        });

        var pre_survey_instance = user_survey_instance.filter(x => x.is_post_survey === false);
        var pre_instance_ids = pre_survey_instance.map((q: { id: any; }) => q.id);
        var pre_query = { ...activeNonDeletedQuery, ...{ "survey_instance_id": inQuery(pre_instance_ids) } };
        let pre_survey_instance_details = await this._survey_instance_detailRepo.find(pre_query);

        var post_survey_instance = user_survey_instance.filter(x => x.is_post_survey === true);
        var post_instance_ids = post_survey_instance.map((q: { id: any; }) => q.id);
        var post_query = { ...activeNonDeletedQuery, ...{ "survey_instance_id": inQuery(post_instance_ids) } };
        let post_survey_instance_details = await this._survey_instance_detailRepo.find(post_query);

        var single_questions = questions.filter(x => x.question_type === 'single_select');

        var question_data = new Array<ExportReportData>();

        for await (const question of single_questions) {
            let result = new ExportReportData();
            result.question = question.title;

            var question_pre_survey_instance_details = pre_survey_instance_details.filter(e => e.survey_question_id === question.id);
            result.pre_program = this.getCohortReportData(pre_survey_instance, question_pre_survey_instance_details);

            var question_post_survey_instance_details = post_survey_instance_details.filter(e => e.survey_question_id === question.id);
            result.post_program = this.getCohortReportData(post_survey_instance, question_post_survey_instance_details);

            question_data.push(result);
        }

        let row_number_jc = 3;
        for (let item of question_data) {
            var row = worksheet.getRow(1 + row_number_jc++);
            row.getCell('questions').value = item.question;
            row.getCell('pre_program_sdi_self').value = item.pre_program.self.start_doing_it;
            row.getCell('pre_program_dmc_self').value = item.pre_program.self.do_more_consistently;
            row.getCell('pre_program_adw_self').value = item.pre_program.self.already_doing_well;
            row.getCell('pre_program_ute_self').value = item.pre_program.self.unable_to_evaluate;

            row.getCell('pre_program_sdi_manager').value = item.pre_program.manager.start_doing_it;
            row.getCell('pre_program_dmc_manager').value = item.pre_program.manager.do_more_consistently;
            row.getCell('pre_program_adw_manager').value = item.pre_program.manager.already_doing_well;
            row.getCell('pre_program_ute_manager').value = item.pre_program.manager.unable_to_evaluate;

            row.getCell('pre_program_sdi_direct').value = item.pre_program.direct.start_doing_it;
            row.getCell('pre_program_dmc_direct').value = item.pre_program.direct.do_more_consistently;
            row.getCell('pre_program_adw_direct').value = item.pre_program.direct.already_doing_well;
            row.getCell('pre_program_ute_direct').value = item.pre_program.direct.unable_to_evaluate;

            row.getCell('pre_program_sdi_peer').value = item.pre_program.peer.start_doing_it;
            row.getCell('pre_program_dmc_peer').value = item.pre_program.peer.do_more_consistently;
            row.getCell('pre_program_adw_peer').value = item.pre_program.peer.already_doing_well;
            row.getCell('pre_program_ute_peer').value = item.pre_program.peer.unable_to_evaluate;

            row.getCell('post_program_sdi_self').value = item.post_program.self.start_doing_it;
            row.getCell('post_program_dmc_self').value = item.post_program.self.do_more_consistently;
            row.getCell('post_program_adw_self').value = item.post_program.self.already_doing_well;
            row.getCell('post_program_ute_self').value = item.post_program.self.unable_to_evaluate;

            row.getCell('post_program_sdi_manager').value = item.post_program.manager.start_doing_it;
            row.getCell('post_program_dmc_manager').value = item.post_program.manager.do_more_consistently;
            row.getCell('post_program_adw_manager').value = item.post_program.manager.already_doing_well;
            row.getCell('post_program_ute_manager').value = item.post_program.manager.unable_to_evaluate;

            row.getCell('post_program_sdi_direct').value = item.post_program.direct.start_doing_it;
            row.getCell('post_program_dmc_direct').value = item.post_program.direct.do_more_consistently;
            row.getCell('post_program_adw_direct').value = item.post_program.direct.already_doing_well;
            row.getCell('post_program_ute_direct').value = item.post_program.direct.unable_to_evaluate;

            row.getCell('post_program_sdi_peer').value = item.post_program.peer.start_doing_it;
            row.getCell('post_program_dmc_peer').value = item.post_program.peer.do_more_consistently;
            row.getCell('post_program_adw_peer').value = item.post_program.peer.already_doing_well;
            row.getCell('post_program_ute_peer').value = item.post_program.peer.unable_to_evaluate;

            row.commit();
        }

        const worksheet1 = workbook.addWorksheet('Overview Summary');

        worksheet1.getRow(1).values = [
            'Respondents Category', 'Pre-Programme Responses', 'Post-Programme Responses'
        ];

        worksheet1.columns = [
            { key: 'category', width: 30 },
            { key: 'pre_program_response', width: 30 },
            { key: 'post_program_response', width: 30 },
        ];

        const ws1_row = worksheet1.getRow(1);
        ws1_row.alignment = centerAlignment;
        ws1_row.font = { bold: true };
        ws1_row.fill = headerFill;
        ws1_row.eachCell({ includeEmpty: true }, (cell) => {
            cell.border = headerBorder;
        });

        var wsrow_sf = worksheet1.getRow(2);
        wsrow_sf.getCell('category').value = 'Self-assessment';
        wsrow_sf.getCell('pre_program_response').value = pre_survey_instance.filter(r => r.survey_type === SurveyType.Self).length;
        wsrow_sf.getCell('post_program_response').value = post_survey_instance.filter(r => r.survey_type === SurveyType.Self).length;
        wsrow_sf.commit();

        var wsrow_sf1 = worksheet1.getRow(3);
        wsrow_sf1.getCell('category').value = 'Manager';
        wsrow_sf1.getCell('pre_program_response').value = pre_survey_instance.filter(r => r.survey_type === SurveyType.Manager).length;
        wsrow_sf1.getCell('post_program_response').value = post_survey_instance.filter(r => r.survey_type === SurveyType.Manager).length;
        wsrow_sf1.commit();

        var wsrow_sf2 = worksheet1.getRow(4);
        wsrow_sf2.getCell('category').value = 'Direct';
        wsrow_sf2.getCell('pre_program_response').value = pre_survey_instance.filter(r => r.survey_type === SurveyType.Direct).length;
        wsrow_sf2.getCell('post_program_response').value = post_survey_instance.filter(r => r.survey_type === SurveyType.Direct).length;
        wsrow_sf2.commit();

        var wsrow_sf3 = worksheet1.getRow(5);
        wsrow_sf3.getCell('category').value = 'Peer';
        wsrow_sf3.getCell('pre_program_response').value = pre_survey_instance.filter(r => r.survey_type === SurveyType.Peer).length;
        wsrow_sf3.getCell('post_program_response').value = post_survey_instance.filter(r => r.survey_type === SurveyType.Peer).length;
        wsrow_sf3.commit();

        var wsrow_sf4 = worksheet1.getRow(6);
        wsrow_sf4.getCell('category').value = 'Total';
        wsrow_sf4.getCell('pre_program_response').value = pre_survey_instance.length;
        wsrow_sf4.getCell('post_program_response').value = post_survey_instance.length;
        wsrow_sf4.commit();

        // wsrow_sf4.alignment = centerAlignment;
        wsrow_sf4.font = { bold: true };

        const worksheet2 = workbook.addWorksheet('Leadership practices');
        worksheet2.getRow(1).values = [
            'Pre Program', '', 'Post Program', ''
        ];

        worksheet2.mergeCells('A1:B1'); // Merge "Pre Program"
        worksheet2.mergeCells('C1:D1'); // Merge "Post Program"

        worksheet2.getRow(2).values = [
            'Question', 'Response (%)', 'Question', 'Response (%)'
        ];

        worksheet2.columns = [
            { key: 'pre_question', width: 80 },
            { key: 'pre_response', width: 20 },
            { key: 'post_question', width: 80 },
            { key: 'post_response', width: 20 }
        ];

        for (let i = 1; i <= 2; i++) {
            const row = worksheet2.getRow(i);
            row.alignment = centerAlignment;
            row.font = { bold: true };
            row.fill = headerFill;
            row.eachCell({ includeEmpty: true }, (cell) => {
                cell.border = headerBorder;
            });
        };

        var leadership_pre_data = this.processLeadershipExportSurveyData(request.master_id, pre_survey_instance_details);
        var leadership_post_data = this.processLeadershipExportSurveyData(request.master_id, post_survey_instance_details);

        let row_number_ls_pre = 2;
        for (let item of leadership_pre_data) {
            var row = worksheet2.getRow(1 + row_number_ls_pre++);
            row.getCell('pre_question').value = item.name;
            row.getCell('pre_response').value = _.round(item.value, 2) + '%';
            row.commit();
        }

        let row_number_ls_post = 2;
        for (let item of leadership_post_data) {
            var row = worksheet2.getRow(1 + row_number_ls_post++);
            row.getCell('post_question').value = item.name;
            row.getCell('post_response').value = _.round(item.value, 2) + '%';
            row.commit();
        }


        const worksheet3 = workbook.addWorksheet('Our Principles');
        worksheet3.getRow(1).values = [
            'Our Principles',
            'Pre Program', '', '', '', '', '', '', '',
            // 'Post Prgram', '', '', '', '', '', '', '',
        ];

        worksheet3.mergeCells('B1:I1');
        // worksheet3.mergeCells('J1:Q1');

        worksheet3.getRow(2).values = [
            'Our Principles',
            'Doing well(%)', '', '', '', 'Improvement needed(%)', '', '', '',
            // 'Doing well(%)', '', '', '', 'Improvement needed(%)', '', '', '',
        ];

        worksheet3.mergeCells('B2:E2');
        worksheet3.mergeCells('F2:I2');
        // worksheet3.mergeCells('J2:M2');
        // worksheet3.mergeCells('N2:Q2');

        worksheet3.getRow(3).values = [
            'Our Principles',
            'Self', 'Manager', 'Direct', 'Peer',
            'Self', 'Manager', 'Direct', 'Peer',
            // 'Self', 'Manager', 'Direct', 'Peer',
            // 'Self', 'Manager', 'Direct', 'Peer',
        ];

        worksheet3.mergeCells('A1:A3');

        worksheet3.columns = [
            { key: 'title_op', width: 25 },

            { key: 'self_pre_dw', width: 10 },
            { key: 'manager_pre_dw', width: 10 },
            { key: 'direct_pre_dw', width: 10 },
            { key: 'peer_pre_dw', width: 10 },
            { key: 'self_pre_in', width: 10 },
            { key: 'manager_pre_in', width: 10 },
            { key: 'direct_pre_in', width: 10 },
            { key: 'peer_pre_in', width: 10 },

            // { key: 'self_post_dw', width: 10 },
            // { key: 'manager_post_dw', width: 10 },
            // { key: 'direct_post_dw', width: 10 },
            // { key: 'peer_post_dw', width: 10 },
            // { key: 'self_post_in', width: 10 },
            // { key: 'manager_post_in', width: 10 },
            // { key: 'direct_post_in', width: 10 },
            // { key: 'peer_post_in', width: 10 },
        ];

        for (let i = 1; i <= 3; i++) {
            const row = worksheet3.getRow(i);
            row.alignment = centerAlignment;
            row.font = { bold: true };
            row.fill = headerFill;
            row.eachCell({ includeEmpty: true }, (cell) => {
                cell.border = headerBorder;
            });
        };

        let op_result = new ExportReportData();
        op_result.pre_program = new ExportReportDataType();
        // op_result.post_program = new ExportReportDataType();

        var op_doing_well_question_id = request.master_id === 1 ? 12 : 24;
        var op_improment_question_id = request.master_id === 1 ? 14 : 26

        var pre_doing_well_details = pre_survey_instance_details.filter(e => e.survey_question_id === op_doing_well_question_id);
        op_result.pre_program.doing_well = this.getCohortReportPricipleData(pre_survey_instance, pre_doing_well_details);

        var pre_improment_details = pre_survey_instance_details.filter(e => e.survey_question_id === op_improment_question_id);
        op_result.pre_program.improment_needed = this.getCohortReportPricipleData(pre_survey_instance, pre_improment_details);

        // var post_doing_well_details = post_survey_instance_details.filter(e => e.survey_question_id === op_doing_well_question_id);
        // op_result.post_program.doing_well = this.getCohortReportPricipleData(post_survey_instance, post_doing_well_details);

        // var post_improment_details = post_survey_instance_details.filter(e => e.survey_question_id === op_improment_question_id);
        // op_result.post_program.improment_needed = this.getCohortReportPricipleData(post_survey_instance, post_improment_details);

        const operations = [
            {
                row: 4,
                title: 'Collaborate to Win',
                key: 'collaborate_to_win'
            },
            {
                row: 5,
                title: 'Deliver Growth',
                key: 'deliver_growth'
            },
            {
                row: 6,
                title: 'Prioritise Customers',
                key: 'prioritise_customers'
            },
            {
                row: 7,
                title: 'Build for a Better Future',
                key: 'build_for_better_future'
            },
            {
                row: 8,
                title: 'Adapt and Evolve',
                key: 'adapt_and_evolve'
            }
        ];

        operations.forEach(op => {
            let op_row = worksheet3.getRow(op.row);
            op_row.getCell('title_op').value = op.title;
            op_row.getCell('self_pre_dw').value = op_result.pre_program.doing_well.self[op.key];
            op_row.getCell('manager_pre_dw').value = op_result.pre_program.doing_well.manager[op.key];
            op_row.getCell('direct_pre_dw').value = op_result.pre_program.doing_well.direct[op.key];
            op_row.getCell('peer_pre_dw').value = op_result.pre_program.doing_well.peer[op.key];
            op_row.getCell('self_pre_in').value = op_result.pre_program.improment_needed.self[op.key];
            op_row.getCell('manager_pre_in').value = op_result.pre_program.improment_needed.manager[op.key];
            op_row.getCell('direct_pre_in').value = op_result.pre_program.improment_needed.direct[op.key];
            op_row.getCell('peer_pre_in').value = op_result.pre_program.improment_needed.peer[op.key];
            // op_row.getCell('self_post_dw').value = op_result.post_program.doing_well.self[op.key];
            // op_row.getCell('manager_post_dw').value = op_result.post_program.doing_well.manager[op.key];
            // op_row.getCell('direct_post_dw').value = op_result.post_program.doing_well.direct[op.key];
            // op_row.getCell('peer_post_dw').value = op_result.post_program.doing_well.peer[op.key];
            // op_row.getCell('self_post_in').value = op_result.post_program.improment_needed.self[op.key];
            // op_row.getCell('manager_post_in').value = op_result.post_program.improment_needed.manager[op.key];
            // op_row.getCell('direct_post_in').value = op_result.post_program.improment_needed.direct[op.key];
            // op_row.getCell('peer_post_in').value = op_result.post_program.improment_needed.peer[op.key];
            op_row.commit();
        });


        return await workbook.xlsx.writeBuffer();
    }

    public async exportcohortParticipantReport(cohort: any) {
        let survey_instance = await this._survey_instanceRepo.find({
            ...activeNonDeletedQuery,
            ...{ "cohort_id": Number(cohort.id) },
            ...orQuery([{ 'workflow_status': WorkflowStatus.Submitted }, { 'workflow_status': WorkflowStatus.Completed }, { 'workflow_status': WorkflowStatus.InProgress }]),
        });

        var pre_survey_instance = survey_instance.filter(x => x.is_post_survey === false);
        var post_survey_instance = survey_instance.filter(x => x.is_post_survey === true);

        var pre_grouped_user_survey_instance = _.groupBy(pre_survey_instance, x => x.master_id);

        const workbook = new excel.Workbook();

        const worksheet_pre = workbook.addWorksheet('Pre Program Participants');

        // Define the main headers
        worksheet_pre.getRow(1).values = [
            'Paricipant Name', 'Assigned To', 'Survey Type', 'Status'
        ];

        worksheet_pre.columns = [
            { key: 'paricipant_name', width: 50 },
            { key: 'assigned_to', width: 50 },
            { key: 'survey_type', width: 15 },
            { key: 'status', width: 15 }
        ];

        const headerFill: Fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFCCCCFF' }
        };

        const centerAlignment: any = { vertical: 'middle', horizontal: 'center' };
        const vcenterAlignment: any = { vertical: 'middle', };

        const row_pre = worksheet_pre.getRow(1);
        row_pre.alignment = centerAlignment;
        row_pre.font = { bold: true };
        row_pre.fill = headerFill;

        let pre_row_number = 1;

        Object.keys(pre_grouped_user_survey_instance).forEach(survey_instance => {
            const pre_start_row_number = pre_row_number + 1;
            var user_survey_instance = pre_grouped_user_survey_instance[survey_instance];
            for (let item of user_survey_instance) {
                var row = worksheet_pre.getRow(1 + pre_row_number++);
                row.alignment = vcenterAlignment;
                row.getCell('paricipant_name').value = item.assigned_for_data.full_name;
                row.getCell('assigned_to').value = item.assigned_to_data.full_name;
                row.getCell('survey_type').value = item.survey_type;
                row.getCell('status').value = item.workflow_status === WorkflowStatus.InProgress ? "Not Submitted" : "Submitted";
                row.commit();
            }

            worksheet_pre.mergeCells('A' + pre_start_row_number + ':A' + pre_row_number);
        });

        if (post_survey_instance != null && post_survey_instance.length > 0) {
            var post_grouped_user_survey_instance = _.groupBy(post_survey_instance, x => x.master_id);

            const worksheet_post = workbook.addWorksheet('Post Program Participants');

            // Define the main headers
            worksheet_post.getRow(1).values = [
                'Paricipant Name', 'Assigned To', 'Survey Type', 'Status'
            ];

            worksheet_post.columns = [
                { key: 'paricipant_name', width: 50 },
                { key: 'assigned_to', width: 50 },
                { key: 'survey_type', width: 15 },
                { key: 'status', width: 15 }
            ];

            const row_post = worksheet_post.getRow(1);
            row_post.alignment = centerAlignment;
            row_post.font = { bold: true };
            row_post.fill = headerFill;

            let post_row_number = 1;

            Object.keys(post_grouped_user_survey_instance).forEach(survey_instance => {
                const post_start_row_number = post_row_number + 1;
                var user_survey_instance = post_grouped_user_survey_instance[survey_instance];
                for (let item of user_survey_instance) {
                    var row = worksheet_post.getRow(1 + post_row_number++);
                    row.alignment = vcenterAlignment;
                    row.getCell('paricipant_name').value = item.assigned_for_data.full_name;
                    row.getCell('assigned_to').value = item.assigned_to_data.full_name;
                    row.getCell('survey_type').value = item.survey_type;
                    row.getCell('status').value = item.workflow_status === WorkflowStatus.InProgress ? "Not Submitted" : "Submitted";
                    row.commit();
                }
                worksheet_post.mergeCells('A' + post_start_row_number + ':A' + post_row_number);
            });
        }

        return await workbook.xlsx.writeBuffer();

    }

    private getCohortReportData(user_survey_instance: any, survey_instance_details: any) {
        const reportData = new ExportReportDataType();

        // Process each survey type
        reportData.self = this.processExportSurveyData(user_survey_instance, SurveyType.Self, survey_instance_details);
        reportData.manager = this.processExportSurveyData(user_survey_instance, SurveyType.Manager, survey_instance_details);
        reportData.direct = this.processExportSurveyData(user_survey_instance, SurveyType.Direct, survey_instance_details);
        reportData.peer = this.processExportSurveyData(user_survey_instance, SurveyType.Peer, survey_instance_details);

        return reportData;
    }

    private processExportSurveyData(surveyInstances, surveyType, surveyDetails) {
        const surveyData = new ExportReportDataTypeAnswer();

        const surveys = surveyInstances.filter(survey => survey.survey_type === surveyType);
        if (surveys.length > 0) {
            const instanceIds = surveys.map(survey => survey.id);
            const answersList = surveyDetails
                .filter(detail => instanceIds.includes(detail.survey_instance_id))
                .flatMap(detail => detail.answer_data);

            let start_doing_it = answersList.filter(answer => answer.title === 'Start Doing It');
            surveyData.start_doing_it = start_doing_it.length > 0 ? _.round(((start_doing_it.length * 100) / answersList.length), 2) : 0;

            let do_more_consistently = answersList.filter(answer => answer.title === 'Do More Consistently');
            surveyData.do_more_consistently = do_more_consistently.length > 0 ? _.round(((do_more_consistently.length * 100) / answersList.length), 2) : 0;

            let already_doing_well = answersList.filter(answer => answer.title === 'Already Doing Well');
            surveyData.already_doing_well = already_doing_well.length > 0 ? _.round(((already_doing_well.length * 100) / answersList.length), 2) : 0;

            let unable_to_evaluate = answersList.filter(answer => answer.title === 'Unable to evaluate');
            surveyData.unable_to_evaluate = unable_to_evaluate.length > 0 ? _.round(((unable_to_evaluate.length * 100) / answersList.length), 2) : 0;

        } else {
            surveyData.start_doing_it = 0;
            surveyData.do_more_consistently = 0;
            surveyData.already_doing_well = 0;
            surveyData.unable_to_evaluate = 0;
        }

        return surveyData;
    }

    private getCohortReportPricipleData(user_survey_instance: any, survey_instance_details: any) {
        const reportData = new ExportReportDataType();

        // Process each survey type
        reportData.self = this.processExportSurveyDataPrinciple(user_survey_instance, SurveyType.Self, survey_instance_details);
        reportData.manager = this.processExportSurveyDataPrinciple(user_survey_instance, SurveyType.Manager, survey_instance_details);
        reportData.direct = this.processExportSurveyDataPrinciple(user_survey_instance, SurveyType.Direct, survey_instance_details);
        reportData.peer = this.processExportSurveyDataPrinciple(user_survey_instance, SurveyType.Peer, survey_instance_details);

        return reportData;
    }

    private processExportSurveyDataPrinciple(surveyInstances, surveyType, surveyDetails) {
        const surveyData = new ExportReportDataTypeAnswer();

        const surveys = surveyInstances.filter(survey => survey.survey_type === surveyType);
        if (surveys.length > 0) {
            const instanceIds = surveys.map(survey => survey.id);
            const answersList = surveyDetails
                .filter(detail => instanceIds.includes(detail.survey_instance_id))
                .flatMap(detail => detail.answer_data);

            let collaborate_to_win = answersList.filter(answer => answer.title === 'Collaborate to Win');
            surveyData.collaborate_to_win = collaborate_to_win.length > 0 ? _.round(((collaborate_to_win.length * 100) / answersList.length), 2) : 0;

            let deliver_growth = answersList.filter(answer => answer.title === 'Deliver Growth');
            surveyData.deliver_growth = deliver_growth.length > 0 ? _.round(((deliver_growth.length * 100) / answersList.length), 2) : 0;

            let adapt_and_evolve = answersList.filter(answer => answer.title === 'Adapt and Evolve');
            surveyData.adapt_and_evolve = adapt_and_evolve.length > 0 ? _.round(((adapt_and_evolve.length * 100) / answersList.length), 2) : 0;

            let prioritise_customers = answersList.filter(answer => answer.title === 'Prioritise Customers');
            surveyData.prioritise_customers = prioritise_customers.length > 0 ? _.round(((prioritise_customers.length * 100) / answersList.length), 2) : 0;

            let build_for_better_future = answersList.filter(answer => answer.title === 'Build for a Better Future');
            surveyData.build_for_better_future = build_for_better_future.length > 0 ? _.round(((build_for_better_future.length * 100) / answersList.length), 2) : 0;

        } else {
            surveyData.collaborate_to_win = 0;
            surveyData.deliver_growth = 0;
            surveyData.adapt_and_evolve = 0;
            surveyData.prioritise_customers = 0;
            surveyData.build_for_better_future = 0;
        }

        return surveyData;
    }

    private processLeadershipExportSurveyData(master_id: number, survey_instance_details: any) {
        const leadership_question_id = master_id === 1 ? 1 : 16;

        var leadership_survey_instance_details = survey_instance_details.filter(e => e.survey_question_id === leadership_question_id);
        const pre_answers_list = leadership_survey_instance_details.flatMap(detail => detail.answer_data);

        var result = [];
        let uniqueAnswer = _.groupBy(pre_answers_list, n => n.title);
        Object.keys(uniqueAnswer).forEach(question => {
            var question_data = uniqueAnswer[question].filter(n => n.title == question);
            if (question_data?.length > 0) {
                result.push({
                    value: (question_data.length * 100) / pre_answers_list.length,
                    name: question
                });
            }
        });

        return result;
    }

    private GetImages(FileName: string) {
        //var image = fs.readFileSync('./src/images/' + FileName, 'base64');
        var image = fs.readFileSync(this.imagespath + '/' + FileName, 'base64');
        return "data:image/png;base64," + image.toString();
    }

    private GetFontBase64(FileName: string) {
        //var image = fs.readFileSync('./src/templates/fonts/' + FileName, 'base64');
        var image = fs.readFileSync(this.templatepath + '/fonts/' + FileName, 'base64');
        return "data:font/truetype;charset=utf-8;base64," + image.toString();
    }
}