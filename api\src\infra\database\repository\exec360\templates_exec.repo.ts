import { templates } from "../../../../domain/entities/exec360/templates";
import { BaseRepository } from "../../../../domain/interfaces/repo/baserepository";
import { itemplates_execRepo } from "../../../../domain/interfaces/repo/exec360/itemplates_exec.repo";
import { Config } from "../../../config/model";
import { LoggerService } from "../../../services/loggerservice";

export class templates_execRepo extends BaseRepository<templates> implements itemplates_execRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, templates);
    }
}