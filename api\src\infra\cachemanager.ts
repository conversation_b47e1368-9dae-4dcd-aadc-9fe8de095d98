import * as cache from 'node-cache';
import { Config } from './config/model';
import { LoggerService } from './services/loggerservice';

export class CacheManager {
    _logger: LoggerService;
    _client: cache;

    constructor(config: Config, logger: LoggerService) {
        this._logger = logger;
        this._client = new cache.default();
  

    }

    public get(type: string, key: string) {
        var data = this._client.get(type + "_" + key) as string;;
        if (data == undefined || data == null)
            return null
        else
            return JSON.parse(data);
    }

    public set(type: string, key: string, data: any, expirySeconds?: number) {
        this._client.set(type + "_" + key, JSON.stringify(data), (expirySeconds && expirySeconds != undefined && expirySeconds != null && expirySeconds > 0) ? expirySeconds : 0);
    }

    public clear(key: string, type?: string) {
        if (type && type != null) {
            key = type + "_" + key;
        }

        this._client.del(key);
    }

    public clearAll() {
        this._client.flushAll();
    }

}