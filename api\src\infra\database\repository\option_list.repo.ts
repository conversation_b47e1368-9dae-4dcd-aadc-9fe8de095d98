import { option_list } from "../../../domain/entities/option_list";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { ioption_listRepo } from "../../../domain/interfaces/repo/ioption_list.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class option_listRepo extends BaseRepository<option_list> implements ioption_listRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, option_list);
    }
}