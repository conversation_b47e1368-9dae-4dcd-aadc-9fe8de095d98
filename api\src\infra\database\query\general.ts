import { literal, Op } from "sequelize";

//filters
export const activeQuery = { "active": true };
export const inActiveQuery = { "active": false };
export const nonDeletedQuery = { "deleted": false };
export const activeNonDeletedQuery = { ...activeQuery, ...nonDeletedQuery };
export const statusCompletedQuery = { "workflow_status": 'completed' };

export function createdByUserQuery(userName: string, includeActive?: boolean, includeNonDeleted?: boolean): any {
    var userQuery = { "created_by": userName };

    if (includeActive == true) {
        userQuery = { ...userQuery, ...activeQuery };
    }
    if (includeNonDeleted == true) {
        userQuery = { ...userQuery, ...nonDeletedQuery };
    }

    return userQuery;
}

export function textSearchQueryLike(fieldName: string, value: string): any {
    
    return { [fieldName]: { [Op.iLike]: '%' + value + '%' } };
}

export function textSearchQuery(fieldName: string, value: string): any {
    return { [fieldName]: { [Op.iLike]: value } };
}

export function notQuery(fieldName: string, value: string): any {
    return { [fieldName]: { [Op.not]: value } };
}
export function navQuery(type: string) {
    return { "nav_type": type };
}
export function orQuery(pram: any): any {
    var options = {};
    if (pram != undefined && pram != null) {
        options = { [Op.or]: pram }
    }
    return options;
}

export function andQuery(pram: any): any {
    var options = {};
    if (pram != undefined && pram != null) {
        options = { [Op.and]: pram }
    }
    return options;
}

export function inQuery(pram: any): any {
    var options = {};
    if (pram != undefined && pram != null) {
        options = { [Op.in]: pram }
    }
    return options;
}
export function notInQuery(pram: any): any {
    var options = {};
    if (pram != undefined && pram != null) {
        options = { [Op.notIn]: pram }
    }
    return options;
}

export function isNullQuery(fieldName: string): any {
    return { [fieldName]: { [Op.is]: null } };
}

export function idQuery(id: number): any {
    var appQuery = { "id": id };
    return appQuery;
}

export function usernameQuery(username: string): any{
    return { "user_name": username };
}

export function systemnameQuery(systemname: string): any {
    return { "system_name": systemname };
}

export function betweenQuery(from: Date, to: Date): any {
    var options = {};
    if (!!from && !!to) {
        options = { [Op.between]: [from, to] }
    }
    return options;
}

export function greaterOrequalQuery(param: any): any {
    var options = {};
    if (!!param) {
        options = { [Op.gte]: param }
    }
    return options;
}

export function lessOrequalQuery(param: any): any {
    var options = {};
    if (!!param) {
        options = { [Op.lte]: param }
    }
    return options;
}
export function lessQuery(param: any): any {
    var options = {};
    if (!!param) {
        options = { [Op.lt]: param }
    }
    return options;
}
export function textSearchQueryLikeJsonWithoutDelete(fieldName: string, value: string): any {
    var options = {
        [Op.and]: [
            literal(`CAST("${fieldName}" AS VARCHAR) ILIKE '%${value}%'`)
        ]
    }
    return options;
}
export function textSearchQueryNotLikeJsonWithoutDelete(fieldName: string, value: string): any {
    var options = {
        [Op.and]: [
            literal(`CAST("${fieldName}" AS VARCHAR) NOT ILIKE '%${value}%'`)
        ]
    }
    return options;
}
export function textSearchQueryLikeJson(fieldName: string, value: string): any {
    var options = {
        [Op.and]: [
            literal(`CAST("${fieldName}" AS VARCHAR) ILIKE '%${value}%'`),
            { deleted: false }
        ]
    }
    return options;
}
export function userIdQuery(userName: string, includeActive?: boolean, includeNonDeleted?: boolean): any {

    var userQuery = { "uid": { [Op.iLike]: userName } };
    if (includeActive == true) {
        userQuery = { ...userQuery, ...activeQuery };
    }
    if (includeNonDeleted == true) {
        userQuery = { ...userQuery, ...nonDeletedQuery };
    }
    return userQuery;
}

export function notequalQuery(param: any): any {
    var options = {};
    if (!!param) {
        options = { [Op.notILike]: param }
    }
    return options;
}
