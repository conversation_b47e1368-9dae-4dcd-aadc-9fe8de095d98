import { createContainer, asClass, asValue, InjectionMode } from 'awilix';
import { Application } from './application';
import { CacheManager } from './infra/cachemanager';
import { env } from './infra/config';
import Database from './infra/database';
import { LoggerService } from './infra/services/loggerservice';
import { Server } from './web';

import { CacheService } from './infra/services/cache/service';
import { CacheStartupService } from './infra/services/cache/populate';
import { UserSearchService } from './infra/services/graphapi/usersearch.service';
import { WorkContext } from './shared/context/workcontext';
import { PermissionCheck } from './shared/utils/permissioncheck';
import { MessageUtil } from './shared/utils/MessageUtil';
import { CommonService } from './services/common.service';
import { option_listRepo } from './infra/database/repository/option_list.repo';
import { survey_instance_detailRepo } from './infra/database/repository/survey_instance_detail.repo';
import { survey_instanceRepo } from './infra/database/repository/survey_instance.repo';
import { survey_questionRepo } from './infra/database/repository/survey_question.repo';
import {survey_sectionRepo} from './infra/database/repository/survey_section.repo';
import { userprofileRepo } from './infra/database/repository/userprofile.repo';
import { ProfileService } from './services/profile.service';
import { EmployeeDataService } from './services/employeeData.service';
import { SurveyService } from './services/survey.service';
import { ReportService } from './services/report.service';
import { survey_masterRepo } from './infra/database/repository/survey_master.repo';
import { survey_instance_cohortRepo } from './infra/database/repository/survey_instance_cohort.repo';
import { SyncTask } from './infra/jobs/synctask';
import { rating_scale_execRepo } from './infra/database/repository/exec360/rating_scale_exec.repo';
import { survey_instance_detail_execRepo } from './infra/database/repository/exec360/survey_instance_detail_exec.repo';
import { survey_instance_execRepo } from './infra/database/repository/exec360/survey_instance_exec.repo';
import { survey_master_execRepo } from './infra/database/repository/exec360/survey_master_exec.repo';
import { survey_question_execRepo } from './infra/database/repository/exec360/survey_question_exec.repo';
import { templates_execRepo } from './infra/database/repository/exec360/templates_exec.repo';
import { SurveyServiceExec } from './services/exec360/survey_exec.service';
import { ReportServiceExec } from './services/exec360/report_exec.service';
import { setup_data_execRepo } from './infra/database/repository/exec360/setup_data_exec.repo';

const container = createContainer({ injectionMode: InjectionMode.CLASSIC });

container.register({
  // System
  app: asClass(Application).singleton(),
  config: asValue(env),
  logger: asClass(LoggerService).singleton(),
  server: asClass(Server).singleton(),
  database: asClass(Database).singleton(),
  cacheManager: asClass(CacheManager).singleton(),

  //Context
  //workContext: asClass(WorkContext).scoped(),

  //Repositories
  option_listRepo: asClass(option_listRepo).scoped(),
  survey_instance_detailRepo: asClass(survey_instance_detailRepo).scoped(),
  survey_instanceRepo: asClass(survey_instanceRepo).scoped(),
  survey_questionRepo: asClass(survey_questionRepo).scoped(),
  survey_sectionRepo: asClass(survey_sectionRepo).scoped(),
  userprofileRepo: asClass(userprofileRepo).scoped(),
  survey_masterRepo: asClass(survey_masterRepo).scoped(),
  survey_instance_cohortRepo: asClass(survey_instance_cohortRepo).scoped(),

  rating_scale_execRepo: asClass(rating_scale_execRepo).scoped(),
  survey_instance_detail_execRepo: asClass(survey_instance_detail_execRepo).scoped(),
  survey_instance_execRepo: asClass(survey_instance_execRepo).scoped(),
  survey_master_execRepo: asClass(survey_master_execRepo).scoped(),
  survey_question_execRepo: asClass(survey_question_execRepo).scoped(),
  templates_execRepo: asClass(templates_execRepo).scoped(),
  setup_data_execRepo: asClass(setup_data_execRepo).scoped(),

  // Services
  commonService: asClass(CommonService).scoped(),
  profileService: asClass(ProfileService).scoped(),
  employeeDataService: asClass(EmployeeDataService).scoped(),
  surveyService: asClass(SurveyService).scoped(),
  reportService: asClass(ReportService).scoped(),

  surveyServiceExec: asClass(SurveyServiceExec).scoped(),
  reportServiceExec: asClass(ReportServiceExec).scoped(),
  
  //Infra
  cacheService: asClass(CacheService).scoped(),
  cacheStartup: asClass(CacheStartupService).scoped(),
  userSearchService: asClass(UserSearchService).scoped(),
  workContext: asClass(WorkContext).scoped(),
  permissionCheck: asClass(PermissionCheck).scoped(),
  messageUtil: asClass(MessageUtil).scoped(),

  //Jobs
  syncTaskJob: asClass(SyncTask).singleton(),
});

export { container }
