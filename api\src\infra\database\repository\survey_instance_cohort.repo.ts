import { survey_instance_cohort } from "../../../domain/entities/survey_instance_cohort";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { isurvey_instance_cohortRepo } from "../../../domain/interfaces/repo/isurvey_instance_cohort.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class survey_instance_cohortRepo extends BaseRepository<survey_instance_cohort> implements isurvey_instance_cohortRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_instance_cohort);
    }
}