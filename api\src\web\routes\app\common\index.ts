import express, { Router } from "express";
import { container } from "../../../../container";
import { CommonService } from "../../../../services/common.service";
import { ProfileService } from "../../../../services/profile.service";
import { EmployeeDataService } from "../../../../services/employeeData.service";
import { WorkContext } from "../../../../shared/context/workcontext";
import { ResponseController } from "../../../../shared/utils/ResponseController";
import { DTORequestHistory } from "./model";
import { SurveyService } from "../../../../services/survey.service";

export function register(router: Router) {
    var commonService: CommonService = container.resolve('commonService');
    var profileService: ProfileService = container.resolve('profileService');
    var employeeDataService: EmployeeDataService = container.resolve('employeeDataService');
    var surveyService: SurveyService = container.resolve('surveyService');

    const innerRouter = express.Router();

    innerRouter.get('/getUserDetails', async function (req, res, next) {
        try {
            var data = (req.user as WorkContext).currentUser;
            await profileService.getOrAddUserProfile(data);
            return ResponseController.ok(res, data);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.post('/getEmployeesbyEntity', async function (req, res, next) {
        try {
            var data = (req.user as WorkContext).currentUser;
            var response = await employeeDataService.getEmployeesbyEntity(req.body);
            return ResponseController.ok(res, response);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.post('/getEmployee', async function (req, res, next) {
        try {
            var data = (req.user as WorkContext).currentUser;
            var response = await employeeDataService.getEmployeedata(req.body.search_text);
            return ResponseController.ok(res, response);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.get('/getCurrentUserRolePermissions', async function (req, res, next) {
        try {
            var workcontext = req.user as WorkContext;
            var resp = await commonService.getCurrentUserRolePermissions(workcontext.currentUser.userName);
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.post('/getAllUser', async function (req, res, next) {
        try {
            var resp = await commonService.getAllUser(req.body.username);
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.get('/getMenus', async function (req, res, next) {
        try {
            let workcontext = req.user as WorkContext;
            var resp = await commonService.getMenus(workcontext.currentUser.userName);
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });
    innerRouter.get('/getBusinessentityLevels', async function (req, res, next) {
        try {
            var resp = await commonService.getbusinessentitylevels();
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.post('/getAllBusinessEntity', async function (req, res, next) {
        try {
            var workcontext = req.user as WorkContext;
            var resp = await commonService.getallbusinessentity(workcontext.currentUser.userName, req.body.permission);
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.post('/getHistory', async function (req, res, next) {
        try {
            let workContext = req.user as WorkContext;
            var history = req.body as DTORequestHistory;
            var resp = await commonService.getHistory(history.entity_id, history.entity_type);
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.post('/getUsertask', async function (req, res, next) {
        try {
            var resp = await commonService.GetUsertask(req.body.user_name);
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });

    innerRouter.get('/downloadFileById/:fileid', async function (req, res, next) {
        try {
            let workContext = req.user as WorkContext;
            var resp = await commonService.downloadFileById(req.params.fileid);
            if (!!resp) {
                {
                    res.status(200);
                    res.setHeader('Content-Type', resp.attachment_content_type);
                    res.setHeader(
                        'Content-Disposition',
                        'attachment; filename=' + resp.attachment_name
                    );
                    var buffer: Buffer = Buffer.from(resp.contents.data);
                    res.write(buffer);
                    res.end();
                }
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            next(error);
        }
    });

    innerRouter.get('/deleteAttachmentbyFileId/:fileid', async function (req, res, next) {
        try {
            var attachment = await commonService.getattchmentbyfileid(req.body.fileid);
            if (!!attachment) {
                {
                    var resp = await commonService.deleteAttachmentbyFileId(req.body.fileid);
                    return ResponseController.ok(res, resp);
                }
            } else {
                return ResponseController.unauthorized(res);
            }
        } catch (error) {
            next(error);
        }
    });

    innerRouter.get('/getBusinessEntityById/:id', async function (req, res, next) {
        try {
            var resp = await commonService.getbusinessentitybyid(Number(req.params.id));
            return ResponseController.ok(res, resp);
        } catch (error) {
            next(error);
        }
    });

    return innerRouter;
}