import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../../infra/config';
import { AppModel } from '../../base/AppModel';

export type survey_instancePk = "id";
export type survey_instanceId = survey_instance[survey_instancePk];

export class survey_instance extends AppModel {
  id!: number;
  division?: string;
  region?: string;
  due_date?: Date;
  assigned_to?: string;
  assigned_to_data?: object;
  assigned_for?: string;
  assigned_for_data?: object;
  unique_id?: string;
  survey_type?: string;
  user_status?: string;
  workflow_status?: string;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;
  master_id?: number;
  last_notification_sent?: Date;
  survey_master_id?: number;
  reminder_frequency_days?: number;
  applicable_principles?: string;
  business_unit?: string;
  country?: string;
  user_preferred_lang?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof survey_instance {
    this.HasSoftDelete = true;
    survey_instance.init({
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true
      },
      division: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      region: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      due_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      assigned_to: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      assigned_for: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      assigned_to_data: {
        type: DataTypes.JSON,
        allowNull: true
      },
      assigned_for_data: {
        type: DataTypes.JSON,
        allowNull: true
      },
      unique_id: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      survey_type: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      user_status: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      workflow_status: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      active: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      deleted: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      created_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      created_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      modified_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      modified_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      master_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      last_notification_sent: {
        type: DataTypes.DATE,
        allowNull: true
      },
      survey_master_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      reminder_frequency_days: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      applicable_principles: {
        type: DataTypes.STRING(1000),
        allowNull: true
      },
      business_unit: {
        type: DataTypes.STRING(250),
        allowNull: true
      },
      country: {
        type: DataTypes.STRING(250),
        allowNull: true
      },
      user_preferred_lang: {
        type: DataTypes.STRING(50),
        allowNull: true
      }
    },
    {
      sequelize,
      tableName: 'survey_instance',
      schema: env.database.schema_exec,
      timestamps: false,
      indexes: [
        {
          name: "survey_instance_pk",
          unique: true,
          fields: [
            { name: "id" },
          ]
        },
      ]
    });
    return survey_instance;
  }
}
