export class ExportSurveyData {
    img_founder_principle: string;
    img_dp_world_logo: string;
    img_play: string;
    img_blank: string;
    img_hand: string;
    img_adapt_evolve: string;
    img_build_better_future: string;
    img_collaborate_to_win: string;
    img_deliver_growth: string;
    img_prioritise_customers: string;
    img_freight_train: string;
    img_title_pagebg: string;
    img_thankyou_page: string;
    img_first_bg: string;
    font_pilate_light: string
    font_pilate_demi: string
    font_pilate_heavy: string
    html_introduction_report_1: string;
    html_introduction_report_2: string;
    lang_code: string;
    full_name: string;
    report_date: string;
    img_first_bg_style: string;

    demographic_summary: SurveySummary[];
    principle_overview: SurveyPrincipleOverviewExport[];
    behavioural_summary: BehaviouralSummary;
    principle_enabler_question_score: PrincipleEnablerQuestionScoreExport[];
    strength_datas: RatingTypeData[];
    development_area_datas: RatingTypeData[];
}

export class RatingTypeData {
    type: any;
    comments: any[];
}

export class SurveySummary {
    rater_group: string;
    invited: number;
    completed: number;
    percentage: string;
}

export class SurveyPrincipleOverview {
    principle: string;
    principle_code: string;
    chart: any;
    data: SurveyTypeData[];
}

export class SurveyQuestionOverview extends SurveyPrincipleOverview {
    question: string
    image: string;
    self_data: number;
    all_data: number;
}

export class SurveyTypeData {
    type: string;
    value: number;
    min: number;
    max: number;
}

export class BehaviouralSummary {
    effective_highest_rated: SurveyQuestionOverview[];
    effective_lowest_rated: SurveyQuestionOverview[];
    ineffective_highest_rated: SurveyQuestionOverview[];
}

export class SurveyPrincipleOverviewExport {
    principle: string;
    image: string;
    chart_data: any;
}

export class PrincipleEnablerQuestionScoreExport {
    principle: string;
    principle_data: any;
    html1: string;
    html2: string;
    combined_enabler_bar_chart: any;
    combined_enabler_pie_chart: any;
    enabler_question_scores: EnablerQuestionScoreExec[];
    ineffective_questions: QuestionScoreExec[];
}

export class QuestionScoreExec {
    question: string;
    question_chart: any;
}

export class EnablerQuestionScoreExec {
    enabler: string;
    enabler_bar_chart: any;
    enabler_pie_chart: any;
    effective_questions: QuestionScoreExec[];
}

export class GroupReportData {
    questions: Questions;
    templates: any;
    demographic_summary: SurveySummary[];
    principle_overview: SurveyPrincipleOverview[];
    behavioural_summary: BehaviouralSummary;
    principle_enabler_question_scores: PrincipleEnablerQuestionScore[];
}

export class Questions {
    question_list: any;
    effective_question_list: any;
    ineffective_question_list: any;
}

export class PrincipleEnablerQuestionScore {
    principle: string;
    principle_code: string;
    principle_data: any;
    html1: string;
    html2: string;
    combined_enabler_rating: SurveyTypeData[];
    combined_enabler_bar_chart: any;
    combined_enabler_pie_chart: any;
    enabler_question_scores: EnablerQuestionScore[];
    ineffective_questions: QuestionScore[];
}

export class EnablerQuestionScore {
    enabler: string;
    enabler_bar_chart: any;
    enabler_pie_chart: any;
    rating: SurveyTypeData[];
    effective_questions: QuestionScore[];
}

export class QuestionScore {
    question: string;
    rating: SurveyTypeData[];
    question_chart: any;
}