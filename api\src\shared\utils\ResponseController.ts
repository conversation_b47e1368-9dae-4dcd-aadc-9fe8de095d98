
import * as express from 'express'

export abstract class ResponseController {
  
  public static jsonResponse (res: express.Response, code: number, message: string) {
    return res.status(code).json({ message })
  }

  public static ok<T> (res: express.Response, dto?: T) {
    if (!!dto) {
      return res.status(200).json(dto);
    } else {
      return res.sendStatus(200);
    }
  }

  public static created (res: express.Response) {
    return res.sendStatus(201);
  }

  public static clientError (res: any, message?: string) {
    return this.jsonResponse(res, 400, message ? message : 'Unauthorized');
  }

  public static unauthorized (res: any, message?: string) {
    return this.jsonResponse(res, 401, message ? message : 'Unauthorized');
  }

  public static forbidden (res: any, message?: string) {
    return this.jsonResponse(res, 403, message ? message : 'Forbidden');
  }

  public static notFound (res: any, message?: string) {
    return this.jsonResponse(res, 404, message ? message : 'Not found');
  }

  public static fail (res: express.Response, error: Error | string) {
    console.log(error);
    return res.status(500).json({
      message: error.toString()
    })
  }
}