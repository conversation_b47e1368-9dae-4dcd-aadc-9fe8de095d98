import { survey_instance_detail } from "../../../../domain/entities/exec360/survey_instance_detail";
import { BaseRepository } from "../../../../domain/interfaces/repo/baserepository";
import { isurvey_instance_detail_execRepo } from "../../../../domain/interfaces/repo/exec360/isurvey_instance_detail_exec.repo";
import { Config } from "../../../config/model";
import { LoggerService } from "../../../services/loggerservice";

export class survey_instance_detail_execRepo extends BaseRepository<survey_instance_detail> implements isurvey_instance_detail_execRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_instance_detail);
    }
}