<!DOCTYPE html>
<html>

<head>
    <link rel="stylesheet" href="/node_modules/@fortawesome/fontawesome-free/css/all.min.css">

    <style type="text/css">
        @font-face {
            font-family: '<PERSON>lat-Demi';
            src: url({{font_pilate_demi}}) format('truetype');
        }

        @font-face {
            font-family: 'Pilat-Light';
            src: url({{font_pilate_light}}) format('truetype');
        }

        @font-face {
            font-family: 'Pilat-Heavy';
            src: url({{font_pilate_heavy}}) format('truetype');
        }

        * {
            font-family: 'Pilat-Light', sans-serif !important;
            font-size: 14px;
        }

        .description {
            font-size: 14px;
        }

        h1 { 
            font-family: 'Pilat-Heavy', sans-serif !important;
            font-size: 18px !important;
            margin-top: 10px !important;
            margin-bottom: 5px !important;
        }

        h2, h3, h4, h5, h6 { 
            font-family: '<PERSON>lat-Demi', sans-serif !important;         
            margin-top: 10px !important;
            margin-bottom: 5px !important;
        }

        h2 {
            font-size: 15px !important;
        }

        h3, h4, h5, h6 {
            font-size: 14px !important;
        }

        b {
            font-family: 'Pilat-Demi', sans-serif !important;
        }

        a {
            color: blue;
            text-decoration: underline;
            pointer-events: all;
        }

        .description th,
        .description td,
        p,
        ul li,
        ol li {
            font-size: 14px;
        }

        .fs-3 {
            font-size: 10px;
        }

        p,
        h1,
        h4 {
            margin-top: 0;
            margin-bottom: 5px;
        }

        .titlePage {
            width: 1110px;
            height:1600px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            color: white;
            overflow: hidden;
        }

        .text-overlay {
            width: 500px;
            position: absolute;
            left: 75px;
            text-align: left;
            padding: 20px;
        }

        .main-title {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .subtitle {
            font-size: 28px;
            font-weight: light;
            margin: 5px 0;
        }

        .subtitle2 {
            font-size: 24px;
            font-weight: light;
        }

        .person {
            margin-top: 20%;
        }

        .lastPage {
            width: 100%;
            height: 100vh;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .disclimanationColumn {
            position: relative;
            overflow: hidden;
            padding: 0;
        }

        .disclimanationColumn img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .tableBorder tbody tr,
        .tableBorder tbody tr td {
            border: 1px solid #666666;
            font-size: 14px;
        }

        table {
            border-collapse: collapse;
        }

        .separator {
            height: 1px;
            background-color: #d1d1d1;
            margin: 10px 0;
        }

    </style>
</head>

<body>
    {{!-- <div style="text-align: center">
        <img src="{{base64DpWorldLogo}}" width="200" /><br />
    </div> --}}

    <div class="card">
        <div class="card-body">
            <div>
                <div>
                    <div class="titlePage" style="{{img_first_bg_style}}">
                        <div class="text-overlay">
                            <div>
                                <p class="main-title">
                                    DP WORLD<br>
                                    INFORME DE RETROALIMENTACIÓN 360° DE NUESTROS PRINCIPIOS
                                </p>
                            </div>
                            <div class="person">
                                <p class="subtitle">{{full_name}}</p>
                                <p class="subtitle2"> {{report_date }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{!-- <h5 style="page-break-after: always;"></h5> --}}
            {{!-- <div class="description">{{{html_introduction_report_1}}}</div>
            <h5 style="page-break-after: always;"></h5> --}}
            <div class="description">{{{html_introduction_report_2}}}</div>
            <h5 style="page-break-after: always;"></h5>
            <div
                style="background: rgb(19,19,28);display: flex; justify-content: space-between; align-items: center; padding-bottom: 1px;">
                <h1 style="margin: 0; font-size: 16px; color: white; padding-left: 20px;text-transform:uppercase;">
                    VISIÓN GENERAL DE LOS PRINCIPIOS</h1>
                <img src="{{img_blank}}" alt="Icon" style="width: 35px; height: 35px; object-fit: contain;">
            </div>
            <div class="description" style="margin-top: 5px;">
                <p style="margin-bottom: 5px;">El gráfico a continuación proporciona un resumen de los resultados para cada uno de los Principios
                    evaluados,
                    comparando tu autoevaluación con los puntajes promedio de todos los
                    evaluadores. Utiliza esta información para identificar tus fortalezas y áreas de desarrollo a alto nivel,
                    en todos los Principios.</p>
            </div>
            <div style="padding-left: 20px; padding-bottom: 25px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="width: 100%;">
                            <table style="width: 100%; border-collapse: collapse;">
                                {{#if principle_overview }}
                                {{#each principle_overview}}
                                <tr style="border-bottom: 2px solid #666666;">
                                    <td style="padding: 10px; text-align: left; width: 5%;"><img src="{{this.image}}"
                                            alt="Icon 1" width="35px"></td>
                                    <td style="padding: 10px; text-align: left;">
                                        <p style=" font-size: 13px;">{{ this.principle }}</p>
                                    </td>
                                    <td style="padding: 10px;">
                                        <img src="{{this.chart_data}}" width="100%" />
                                    </td>
                                </tr>
                                {{/each}}
                                {{/if}}
                            </table>
                        </td>
                    </tr>
                </table>
            </div>
            <h5 style="page-break-after: always;"></h5>
            <div
                style="background: rgb(19,19,28);display: flex; justify-content: space-between; align-items: center; padding-bottom: 1px;">
                <h1 style="margin: 0; font-size: 16px; color: white; padding-left: 20px;text-transform:uppercase;">
                    RESUMEN DE COMPORTAMIENTOS</h1>
                <img src="{{img_blank}}" alt="Icon" style="width: 35px; height: 35px; object-fit: contain;">
            </div>
            <div class="description" style="margin-top: 5px;">
                <p>Esta página muestra tus <strong>Comportamientos Deseados</strong> con las <strong>calificaciones más altas</strong> y <strong>más bajas</strong>, y tus comportamientos <strong>Inefectivos</strong>
                    con las <span>calificaciones más altas</span>,
                    en todos los Principios, basado en las calificaciones de 'Todos los demás'. Se
                    muestra el Principio al que está alineado cada comportamiento. Utiliza esta página para identificar áreas clave de enfoque y
                    Principios prioritarios.</p>
            </div>
            <div>
                <h1 style="margin: 0; font-size: 18px; color: rgb(0, 0, 0); border-bottom: 2px solid #666666;">
                    Comportamientos Deseados</h1>
                <table style="width: 100%; border-collapse: collapse;font-size:16px;">
                    <tr>
                        <td style="padding: 5px; width: 100%;">
                            <p class="description">Tus Comportamientos Deseados <b>mejor evaluados</b> - posibles <b>áreas de
                                    fortaleza</b></p>
                            <table style="width: 100%; border-collapse: collapse;" class="description">
                                <tr style="background-color: #000; color: #fff;">
                                    <th style="padding: 5px; text-align: left; width:70%;">Comportamiento Deseado</th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">
                                        Principio
                                    </th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">Todos los
                                        Evaluadores
                                        Combinados</th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">Autoevaluación
                                    </th>
                                </tr>
                                {{#if behavioural_summary.effective_highest_rated }}
                                {{#each behavioural_summary.effective_highest_rated }}
                                <tr style="background-color: #f2f2f2;">
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc;" class="fs-3">{{
                                        this.question }}</td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;"><img
                                            src="{{this.image}}" width="25px" alt="setting_icon"></td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;"> {{
                                        this.all_data }} </td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;">{{
                                        this.self_data }}</td>
                                </tr>
                                {{/each}}
                                {{/if}}
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 5px; width: 100%;">
                            <p class="description">Tus Comportamientos Deseados <strong>peor evaluados</strong> - posibles
                                <strong>áreas de
                                    desarrollo</strong>
                            </p>
                            <table style="width: 100%; border-collapse: collapse;" class="description">
                                <tr style="background-color: #000; color: #fff;">
                                    <th style="padding: 5px; text-align: left; width:70%;">Comportamiento Deseado</th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">
                                        Principio
                                    </th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">Todos los
                                        Evaluadores
                                        Combinados</th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">Autoevaluación
                                    </th>
                                </tr>
                                {{#if behavioural_summary.effective_lowest_rated }}
                                {{#each behavioural_summary.effective_lowest_rated }}
                                <tr style="background-color: #f2f2f2;">
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc;" class="fs-3">{{
                                        this.question }}</td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;"><img
                                            src="{{this.image}}" width="30px" alt="setting_icon"></td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;"> {{
                                        this.all_data }} </td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;">{{
                                        this.self_data }}</td>
                                </tr>
                                {{/each}}
                                {{/if}}
                            </table>
                        </td>
                    </tr>
                </table>
            </div>
            <div>
                <h1
                    style="margin: 0; font-size: 18px; color: rgb(0, 0, 0); border-bottom: 2px solid #666666;padding-bottom: 5px;">
                    Comportamientos Inefectivos</h1>
                <table style="width: 100%; border-collapse: collapse;font-size:16px;" class="description">
                    <tr>
                        <td style="padding: 5px; width: 100%;">
                            <p class="description">Tus Comportamientos Inefectivos <b>mejor evaluados</b> - posibles
                                <b>áreas de desarrollo</b>
                            </p>
                            <table style="width: 100%; border-collapse: collapse;" class="description">
                                <tr style="background-color: #000; color: #fff;">
                                    <th style="padding: 5px; text-align: left; width:70%;">Comportamiento Inefectivo</th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">
                                        Principio
                                    </th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">Todos los
                                        Evaluadores
                                        Combinados</th>
                                    <th style="padding: 5px; text-align: left; width:10%; text-align: center;">Autoevaluación
                                    </th>
                                </tr>
                                {{#if behavioural_summary.ineffective_highest_rated }}
                                {{#each behavioural_summary.ineffective_highest_rated }}
                                <tr style="background-color: #f2f2f2;">
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc;" class="fs-3">{{
                                        this.question }}</td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;"><img
                                            src="{{this.image}}" width="30px" alt="setting_icon"></td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;"> {{
                                        this.all_data }} </td>
                                    <td style="padding: 5px; border-bottom: 1px solid #ccc; text-align: center;">{{
                                        this.self_data }}</td>
                                </tr>
                                {{/each}}
                                {{/if}}
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; width:100%; vertical-align: top;">
                            <h2><b>Clave</b></h2>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 10px; text-align: center;"><img src="{{img_adapt_evolve}}"
                                            alt="Icon 1" width="40px">
                                    </td>
                                    <td style="padding: 10px;">
                                        <p style="font-size: 14px;">Adaptarse y Evolucionar</p>
                                    </td>
                                    <td style="padding: 10px; text-align: center;"><img
                                            src="{{img_build_better_future}}" alt="Icon 1" width="40px"></td>
                                    <td style="padding: 10px;">
                                        <p style="font-size: 14px;">Construir un Futuro Mejor</p>
                                    </td>
                                    <td style="padding: 10px; text-align: center;"><img src="{{img_collaborate_to_win}}"
                                            alt="Icon 1" width="40px">
                                    </td>
                                    <td style="padding: 10px;">
                                        <p style="font-size: 14px;">Colaborar para Ganar</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; text-align: center;"><img src="{{img_deliver_growth}}"
                                            alt="Icon 1" width="40px"></td>
                                    <td style="padding: 10px;">
                                        <p style="font-size: 14px;">Generar Crecimiento</p>
                                    </td>
                                    <td style="padding: 10px; text-align: center;"><img
                                            src="{{img_prioritise_customers}}" alt="Icon 1" width="40px">
                                    </td>
                                    <td style="padding: 10px;">
                                        <p style="font-size: 14px;">Priorizar a los Clientes</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </div>
            <h5 style="page-break-after: always;"></h5>
            {{#if principle_enabler_question_score }}
            {{#each principle_enabler_question_score }}
            <div>
                <div
                    style="background-color: {{this.principle_data.color}};display: flex; justify-content: space-between; align-items: center; padding-bottom: 1px;">
                    <h1 style="margin: 0; font-size: 16px; color: white; padding-left: 20px;text-transform:uppercase;">
                        <span>{{this.principle_data.name}}</span>&nbsp;-&nbsp;<span>COMPORTAMIENTOS EFECTIVOS</span>
                    </h1>
                    <img src="{{this.principle_data.image}}" alt="Icon"
                        style="width: 35px; height: 35px; object-fit: contain;">
                </div>
                <div class="description">{{{ this.html1 }}}</div>
                <h1
                    style="margin: 0; font-size: 18px; color: rgb(0, 0, 0); border-bottom: 2px solid #666666;padding-bottom: 5px;padding-top: 10px">
                    Puntuación del Principio</h1>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 10px;  width:25%; vertical-align: top;">
                            <img src="{{this.combined_enabler_pie_chart}}" width="250px" />
                        </td>
                        <td style="padding: 10px;  width:75%; vertical-align: top;">
                            <img src="{{this.combined_enabler_bar_chart}}" width="100%" />
                        </td>
                    </tr>
                </table>
                <h1
                    style="margin: 0; font-size: 20px; color: rgb(0, 0, 0); border-bottom: 2px solid #666666;padding-bottom: 5px;">
                    Puntuaciones de los Facilitadores</h1>
                {{#if this.enabler_question_scores }}
                {{#each this.enabler_question_scores }}
                <table style="width: 100%; border-collapse: collapse;" class="description">
                    <tr>
                        <td style="padding: 10px;  width:25%; vertical-align: middle;" class="description">
                            {{this.enabler}}
                        </td>
                        <td style="padding: 10px;  width:75%; vertical-align: top;">
                            <img src="{{this.enabler_bar_chart}}" width="100%" />
                        </td>
                    </tr>
                </table>
                {{/each}}
                {{/if}}
                <h5 style="page-break-after: always;"></h5>
                {{#if this.enabler_question_scores }}
                {{#each this.enabler_question_scores }}
                <div>
                    <div
                        style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 1px; background-color: {{ ../principle_data.color }}">
                        <h1
                            style="margin: 0; font-size: 16px; color: white; padding-left: 20px;text-transform:uppercase;">
                            <span>{{../principle_data.name}}</span>&nbsp;-&nbsp;<span>COMPORTAMIENTOS EFECTIVOS</span>
                        </h1>
                        <img src="{{../principle_data.image}}" alt="Icon"
                            style="width: 35px; height: 35px; object-fit: contain;">
                    </div>
                    <h2 class="mt-3">{{ this.enabler }}</h2>
                    <div class="description">Esta página muestra el desglose por Grupo de Evaluadores bajo este Facilitador. Revisa cómo
                        diferentes evaluadores perciben
                        tu comportamiento en este Facilitador, e identifica tus fortalezas y áreas de desarrollo relativas a los comportamientos subyacentes.
                    </div>
                    <table style="width: 100%; border-collapse: collapse;" class="description">
                        <tr>
                            <td style="padding: 10px;  width:25%; vertical-align: top;">
                                <img src="{{this.enabler_pie_chart}}" width="235px" />
                            </td>
                            <td style="padding: 10px;  width:75%; vertical-align: top;">
                                <img src="{{this.enabler_bar_chart}}" width="100%" />
                            </td>
                        </tr>
                    </table>
                    <h1
                        style="margin: 0; font-size: 18px; color: rgb(0, 0, 0); border-bottom: 2px solid #666666;padding-bottom: 5px;">
                        Calificaciones de Comportamiento</h1>
                    <div style="display: grid; grid-template-columns: 25% 75%; gap: 10px;">
                        {{#if this.effective_questions }}
                        {{#each this.effective_questions }}
                        <div style="margin:auto;" class="description">{{this.question}}</div>
                        <div style="margin:auto;"><img src="{{ this.question_chart }}" width="100%" /></div>
                        {{/each}}
                        {{/if}}
                    </div>
                </div>
                <h5 style="page-break-after: always;"></h5>
                {{/each}}
                {{/if}}
                <div
                    style="background-color: {{this.principle_data.color}};display: flex; justify-content: space-between; align-items: center; padding-bottom: 1px;">
                    <h1 style="margin: 0; font-size: 16px; color: white; padding-left: 20px;text-transform:uppercase;">
                        <span>{{this.principle_data.name}}</span>&nbsp;-&nbsp;<span>COMPORTAMIENTOS INEFECTIVOS</span>
                    </h1>
                    <img src="{{this.principle_data.image}}" alt="Icon"
                        style="width: 35px; height: 35px; object-fit: contain;">
                </div>
                <div style="margin-top: 5px;">
                    <h2 class="mt-3">COMPORTAMIENTOS INEFECTIVOS</h2>
                    <div class="description">Esta página muestra la frecuencia con la que se observó que demostrabas
                        'Comportamientos inefectivos' bajo
                        este Principio. Utiliza esta página para identificar comportamientos que puedas estar
                        demostrando con demasiada frecuencia y que puedan estar inhibiendo tu desempeño. Ten en cuenta que una calificación
                        más baja
                        en estos comportamientos es deseable.
                    </div>
                    <div style="display: grid; grid-template-columns: 25% 75%; gap: 10px;">
                        {{#if this.ineffective_questions }}
                        {{#each this.ineffective_questions }}
                        <div style="margin:auto;" class="description">{{this.question}}</div>
                        <div style="margin:auto;"><img src="{{ this.question_chart }}" width="100%" /></div>
                        {{/each}}
                        {{/if}}
                    </div>
                </div>
                <h5 style="page-break-after: always;"></h5>
                <div class="description">{{{ this.html2 }}}</div>
                <h5 style="page-break-after: always;"></h5>
            </div>
            {{/each}}
            {{/if}}
            {{!-- <h5 style="page-break-after: always;"></h5> --}}
            <div>
                <div style="display: flex; justify-content: start; align-items: center; padding-bottom: 1px;">
                    <img src="{{img_play}}" alt="Icon" style="width: 35px; height: 35px; object-fit: contain;">
                    <h1 style="margin: 0; font-size: 18px; padding-left: 20px;">¿QUÉ COMPORTAMIENTOS SON TUS PRINCIPALES FORTALEZAS
                        QUE
                        TE AYUDARÁN A ALINEARTE CON LOS PRINCIPIOS DE DP WORLD?</h1>
                </div>
                {{#if this.strength_datas }}
                {{#each this.strength_datas }}
                <div style="padding-top: 5px;" class="description">
                    <h4 style="padding-bottom: 0px; margin-bottom:0px">{{ this.type }}</h4>
                    <ul style="padding-left: 15px;">
                        {{#each this.comments }}
                        <li> {{this}}</li>
                        {{/each}}
                    </ul>
                </div>
                {{/each}}
                {{/if}}
            </div>
            <h5 style="page-break-after: always;"></h5>
            <div>
                <div style="display: flex; justify-content: start; align-items: center; padding-bottom: 1px;">
                    <img src="{{img_play}}" alt="Icon" style="width: 35px; height: 35px; object-fit: contain;">
                    <h1 style="margin: 0; font-size: 18px; padding-left: 20px;">¿CUÁLES SON LAS ÁREAS DE DESARROLLO EN LAS QUE
                        NECESITAS
                        TRABAJAR PARA SER MÁS EFECTIVO?</h1>
                </div>
                {{#if this.development_area_datas }}
                {{#each this.development_area_datas }}
                <div style="padding-top: 5px;" class="description">
                    <h4 style="padding-bottom: 0px; margin-bottom:0px">{{ this.type }}</h4>
                    <ul style="padding-left: 15px;">
                        {{#each this.comments }}
                        <li> {{this}}</li>
                        {{/each}}
                    </ul>
                </div>
                {{/each}}
                {{/if}}
            </div>
        </div>
    </div>
</body>
</html>