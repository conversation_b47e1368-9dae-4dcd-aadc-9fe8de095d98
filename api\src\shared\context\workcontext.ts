
export class WorkContextUser {
    userName: string = "";
    email: string = "";
    fullName: string = "";
    firstName: string = "";
    profileId: number = 0;
    businessUnit: string = "";
    title: string = "";
}

export class WorkContextUserPermission {
    permissionName: string = "";
    applicationId: number = 0;
    locations: number[] = [];
}


export class WorkContextUserRoles {
    roleName: string = "";
    applicationId: number = 0;
    locations: number[] = [];
}

export class WorkContext {
    currentUser: WorkContextUser;
    currentUserPermission: WorkContextUserPermission[];
    currentUserRoles: WorkContextUserRoles[];

    constructor() {
        this.currentUser = new WorkContextUser();
        this.currentUserPermission = [];
        this.currentUserRoles = [];

    }
}
