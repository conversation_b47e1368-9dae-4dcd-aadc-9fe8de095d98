// import all interfaces
import { IWrite } from './iwrite';
import { IRead } from './iread';
import { WhereOptions } from 'sequelize';
import { LoggerService } from '../../../infra/services/loggerservice';
import { AppModel } from '../../base/AppModel';
import { Config } from '../../../infra/config/model';


// that class only can be extended
export abstract class BaseRepository<T extends AppModel> implements IWrite<T>, IRead<T> {

    private _modelType: { new(): T } & typeof AppModel;
    private _logger: LoggerService;
    private _config: Config;

    constructor(config: Config, logger: LoggerService, modelType: { new(): T } & typeof AppModel) {
        this._logger = logger;
        this._modelType = modelType;

        this._config = config;

    }

    async create(item: T, operation_by: string): Promise<any> {
        try {
            item.setDataValue("created_by", operation_by);
            item.setDataValue("created_on", new Date().toUTCString());
            item.setDataValue("modified_by", operation_by);
            item.setDataValue("modified_on", new Date().toUTCString());
            item.setDataValue("tennant_id", this._config.tennantId);
            item.setDataValue("deleted", false);
            item.setDataValue("active", true);

            var d = await item.save();
            return d;
        }
        catch (err) {
            this._logger.error(err);
            return d;
        }
    }

    async createBulk(item: any[], operation_by: string): Promise<boolean> {
        try {

            var data = item.map(e => {
                let model = this._modelType.build(e);
                model.setDataValue("created_by", operation_by);
                model.setDataValue("created_on", new Date().toUTCString());
                model.setDataValue("modified_by", operation_by);
                model.setDataValue("modified_on", new Date().toUTCString());
                model.setDataValue("tennant_id", this._config.tennantId);
                model.setDataValue("deleted", false);
                model.setDataValue("active", true);
                //data.push(model);
                model.save();
            });

            // this._modelType.bulKCCreate(data);

            return true;
        }
        catch (err) {
            this._logger.error(err);
            return false;
        }
    }

    async update(item: T, operation_by: string): Promise<boolean> {
        try {
            item.setDataValue("modified_by", operation_by);
            item.setDataValue("modified_on", new Date().toUTCString());

            item.save().then(e => {
                console.log(e);
            });
            //await this._modelType.update(item ,{where: {"id": id}}).then(e => {

            ///}).catch((err) => { throw err });
            //
            return true;
        }
        catch (err) {
            this._logger.error(err);
            return false;
        }
    }

    async delete(id: number, operation_by: string): Promise<boolean> {
        try {
            var item = await this._modelType.findByPk(id);
            if (item != null) {
                if (this._modelType.HasSoftDelete) {
                    await this._modelType.update({ "deleted": true, "modified_by": operation_by, "modified_on": new Date().toUTCString() },
                        { where: { "id": id } }).catch((err) => { throw err });

                }
                else {
                    await this._modelType.destroy({ where: { "id": id } }).catch((err) => { throw err });
                }
            }
            return true;
        }
        catch (err) {
            this._logger.error(err);
            return false;
        }
    }


    async deletebycondition(filter: WhereOptions, operation_by: string): Promise<boolean> {
        try {
            if (this._modelType.HasSoftDelete) {
                await this._modelType.update({ "deleted": true, "modified_by": operation_by, "modified_on": new Date().toUTCString() },
                    { where: filter }).catch((err) => { throw err });

            }
            else {
                await this._modelType.destroy({ where: filter }).catch((err) => { throw err });
            }

            return true;
        }
        catch (err) {
            this._logger.error(err);
            return false;
        }
    }

    async find(filter?: WhereOptions, orderBy?: any, pageSize?: number, startRow?: number, includeReferences?: any, raw?: boolean): Promise<any> {
        var options = { raw: true };
        if (raw != undefined && raw != null && raw == false) {
            options.raw = false;
        }
        if (filter != undefined && filter != null) {
            var whereCondition = { where: filter };
            options = { ...options, ...whereCondition };
        }
        if (orderBy != undefined && orderBy != null) {
            var order = { order: orderBy }
            options = { ...options, ...order }
        }
        if (pageSize != undefined && pageSize != null) {
            var limit = { limit: pageSize }
            options = { ...options, ...limit }
        }
        if (startRow != undefined && startRow != null) {
            var startFrom = { offset: startRow }
            options = { ...options, ...startFrom }
        }
        if (includeReferences != undefined && includeReferences != null) {
            var include = { include: includeReferences, nest: true }
            options = { ...options, ...include }
        }

        var data = await this._modelType.findAll(options);
        return data;
    }

    async count(filter?: WhereOptions): Promise<number> {
        var options = {};
        if (filter != undefined && filter != null) {
            var whereCondition = { where: filter };
            options = { ...options, ...whereCondition };
        }
        var count = await this._modelType.count(options)
        return count;
    }

    async findFirst(filter: WhereOptions, searchRaw?: boolean): Promise<any> {
        if (searchRaw == undefined || searchRaw == null) {
            searchRaw = true
        }

        var data = await this._modelType.findOne({
            where: filter, raw: searchRaw,
        });

        return data;

    }


    async getById(id: number): Promise<any> {
        var data = await this._modelType.findByPk(id);
        return data;
    }
    async findQuery(query: string, nestData?: boolean, params?: string[], dataModel?: any): Promise<any> {
        var options = { raw: true };
        if (nestData != undefined && nestData != null) {

            options = { ...options, ...{ nest: nestData } };
        }
        if (dataModel != undefined && dataModel != null) {
            options = { ...options, ...{ model: dataModel, mapToModel: true } };
        }
        if (params != undefined && params != null && params.length > 0) {

            options = { ...options, ...{ bind: params } };
        }

        //var data = await this._modelType.sequelize.query(query, {raw: true, nest : (nestData != undefined && nestData != null && nestData)  });
        var data = await this._modelType.sequelize.query(query, options);
        return data;
    }

    async findSingleValue(query: string, nestData?: boolean, params?: string[]): Promise<any> {
        var options = { raw: true };
        if (nestData != undefined && nestData != null) {

            options = { ...options, ...{ nest: nestData } };
        }
        if (params != undefined && params != null && params.length > 0) {

            options = { ...options, ...{ bind: params } };
        }

        //var data = await this._modelType.sequelize.query(query, {raw: true, nest : (nestData != undefined && nestData != null && nestData)  });
        var data = await this._modelType.sequelize.query(query, options);
        return data[0];
    }

}