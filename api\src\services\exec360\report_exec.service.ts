import fs from "fs";
import moment from "moment";
import path from "path";
import { CacheManager } from "../../infra/cachemanager";
import { Config } from "../../infra/config/model";
import {
  activeNonDeletedQuery,
  inQuery,
  isNull<PERSON><PERSON>y,
  orQ<PERSON>y
} from "../../infra/database/query/general";
import { rating_scale_execRepo } from "../../infra/database/repository/exec360/rating_scale_exec.repo";
import { survey_instance_detail_execRepo } from "../../infra/database/repository/exec360/survey_instance_detail_exec.repo";
import { survey_instance_execRepo } from "../../infra/database/repository/exec360/survey_instance_exec.repo";
import { survey_master_execRepo } from "../../infra/database/repository/exec360/survey_master_exec.repo";
import { survey_question_execRepo } from "../../infra/database/repository/exec360/survey_question_exec.repo";
import { templates_execRepo } from "../../infra/database/repository/exec360/templates_exec.repo";
import { UserSearchService } from "../../infra/services/graphapi/usersearch.service";
import { LoggerService } from "../../infra/services/loggerservice";
import {
  ChartSurveyTypeExec,
  PrincipleData,
  SetupTypeExec,
  SurveyTypeExec,
  WorkflowStatusExec
} from "../../shared/enum/general";
import { PDFUtil } from "../../shared/utils/HtmlToPDF";
import {
  BehaviouralSummary,
  EnablerQuestionScore,
  ExportSurveyData,
  GroupReportData,
  PrincipleEnablerQuestionScore,
  QuestionScore,
  Questions,
  SurveyPrincipleOverview,
  SurveyQuestionOverview,
  SurveySummary,
  SurveyTypeData
} from "../../web/routes/app/report-exec/model";
import {} from "../../web/routes/app/survey/model";
import { CommonService } from "../common.service";
import { EmployeeDataService } from "../employeeData.service";
import _ from "lodash";
import { filters } from "../../web/routes/app/survey-exec/model";
import { setup_data_execRepo } from "../../infra/database/repository/exec360/setup_data_exec.repo";

export class ReportServiceExec {
  _logger: LoggerService;
  _cacheManager: CacheManager;
  _tennantId: string;
  _appId: string;
  _survey_instance_execRepo: survey_instance_execRepo;
  _survey_instance_detail_execRepo: survey_instance_detail_execRepo;
  _survey_question_execRepo: survey_question_execRepo;
  _employeeDataService: EmployeeDataService;
  _commonService: CommonService;
  _survey_master_execRepo: survey_master_execRepo;
  _rating_scale_execRepo: rating_scale_execRepo;
  _templates_execRepo: templates_execRepo;
  _userSearchService: UserSearchService;
  _setup_data_execRepo: setup_data_execRepo

  private templatepath = path.join(__dirname, "..", "..", "templates");
  private imagespath = path.join(__dirname, "..", "..", "images");

  constructor(
    config: Config,
    logger: LoggerService,
    cacheManager: CacheManager,
    survey_instance_execRepo: survey_instance_execRepo,
    survey_instance_detail_execRepo: survey_instance_detail_execRepo,
    survey_question_execRepo: survey_question_execRepo,
    survey_master_execRepo: survey_master_execRepo,
    templates_execRepo: templates_execRepo,
    employeeDataService: EmployeeDataService,
    rating_scale_execRepo: rating_scale_execRepo,
    commonService: CommonService,
    userSearchService: UserSearchService,
    setup_data_execRepo: setup_data_execRepo
  ) {
    this._logger = logger;
    this._cacheManager = cacheManager;
    this._tennantId = config.tennantId;
    this._appId = config.applicationId;
    this._survey_instance_execRepo = survey_instance_execRepo;
    this._survey_instance_detail_execRepo = survey_instance_detail_execRepo;
    this._employeeDataService = employeeDataService;
    this._survey_master_execRepo = survey_master_execRepo;
    this._survey_question_execRepo = survey_question_execRepo;
    this._rating_scale_execRepo = rating_scale_execRepo;
    this._commonService = commonService;
    this._templates_execRepo = templates_execRepo;
    this._userSearchService = userSearchService;
    this._setup_data_execRepo = setup_data_execRepo;
  }

  public async getUserCompletedSurvey(self_instance_id: number) {
    let user_survey = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ master_id: self_instance_id },
      ...orQuery([
        { workflow_status: WorkflowStatusExec.Completed },
        { workflow_status: WorkflowStatusExec.Pending_Validation },
        { workflow_status: WorkflowStatusExec.CompletedReportReady }
      ])
    });

    return user_survey;
  }

  public async getUserSurveyInstances(self_instance_id: number) {
    let user_survey = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ master_id: self_instance_id },
      ...orQuery([
        { workflow_status: WorkflowStatusExec.Submitted },
        { workflow_status: WorkflowStatusExec.Completed },
        { workflow_status: WorkflowStatusExec.CompletedReportReady },
        { workflow_status: WorkflowStatusExec.InProgress },
        { workflow_status: WorkflowStatusExec.Cancelled },
        { workflow_status: WorkflowStatusExec.Expired },
        { workflow_status: WorkflowStatusExec.Pending_Validation }
      ])
    });

    return user_survey;
  }

  public async getSurveyDetailBySurveyId(survey_instance_id: any) {
    let user_survey_instance = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...orQuery([
        { workflow_status: WorkflowStatusExec.Completed },
        { workflow_status: WorkflowStatusExec.CompletedReportReady },
        { workflow_status: WorkflowStatusExec.Pending_Validation }
      ]),
      ...{ master_id: survey_instance_id }
    });

    var answers_list_datas = await this.surveyDataByQuestionId(
      user_survey_instance
    );
    return answers_list_datas;
  }

  public async surveyDataByQuestionId(user_survey_instance: any) {
    if (user_survey_instance != null) {
      var instance_ids = user_survey_instance.map((q: { id: any }) => q.id);
      var query = {
        ...activeNonDeletedQuery,
        ...{ survey_instance_id: inQuery(instance_ids) }
      };

      return await this._survey_instance_detail_execRepo.find(query);
    }
    return null;
  }

  public async printUserSurveyReport(
    data: ExportSurveyData,
    survey_instance: any
  ) {
    var attachment_buffer: Buffer = await this.generateSurveyReportPDF(
      data,
      survey_instance
    );
    return attachment_buffer;
  }

  public async printGroupUserSurveyReport(data: ExportSurveyData) {
    var attachment_buffer: Buffer = await this.generateGroupSurveyReportPDF(
      data
    );
    return attachment_buffer;
  }

  public async getSurveyGroupedQuestionsForGroupReport(
    survey_instance_ids: any,
    survey_master_id: number,
    lang: string
  ): Promise<any> {
    let query = {
      ...activeNonDeletedQuery,
      survey_master_id: survey_master_id,
      lang_code: lang
    };

    if (survey_instance_ids?.length > 0) {
      let all_instance = await this._survey_instance_execRepo.find({
        id: inQuery(survey_instance_ids)
      });
      let applicable_principles = _.split(
        all_instance.map((x) => x.applicable_principles),
        ","
      );
      applicable_principles = _.uniqBy(applicable_principles, (x) => x);

      query = {
        ...query,
        ...orQuery([
          { principle: inQuery(applicable_principles) },
          isNullQuery("principle")
        ])
      };
    }

    let questionList = await this._survey_question_execRepo.find(query);
    questionList = _.orderBy(questionList, (x) => x.principle, "asc");
    const ratingScaleList = await this.getSortedRatingScales(lang);

    const effectiveRatingScale = ratingScaleList.find(
      (x) => x.system_name === "EFFECTIVE_BEHAVIOUR"
    );
    const ineffectiveRatingScale = ratingScaleList.find(
      (x) => x.system_name === "INEFFECTIVE_BEHAVIOUR"
    );

    const groupedByPrinciple = _.groupBy(
      questionList.filter((x) => x.principle !== null),
      (question) => question.principle
    );
    const principleQuestions = this._commonService.createPrincipleQuestions(
      groupedByPrinciple,
      effectiveRatingScale,
      ineffectiveRatingScale
    );

    // const freeTextQuestions = questionList.filter(question => question.question_type === 'free_text');

    return principleQuestions;
  }

  public async getGroupUserReportData(request: filters) {
    let query = {
      ...activeNonDeletedQuery,
      ...{ workflow_status: WorkflowStatusExec.Completed },
      ...{ workflow_status: WorkflowStatusExec.CompletedReportReady }
    };

    if (request.survey_instance_ids?.length > 0) {
      query = {
        ...query,
        ...{ master_id: inQuery(request.survey_instance_ids) }
      };
    } else {
      if (!!request.division) {
        query = { ...query, ...{ division: request.division } };
      }

      if (!!request.region) {
        query = { ...query, ...{ region: request.region } };
      }
    }

    var survey_instance_list = await this._survey_instance_execRepo.find(query);

    if (survey_instance_list?.length > 0) {
      let survey_instance_ids = survey_instance_list.map((x) => x.id);
      let query1 = {
        ...activeNonDeletedQuery,
        ...{ survey_instance_id: inQuery(survey_instance_ids) }
      };
      var survey_instance_detail_list =
        await this._survey_instance_detail_execRepo.find(query1);

      var principle_query = { ...activeNonDeletedQuery, ...{ setup_type: SetupTypeExec.PRINCIPLES_DATA } };
      let principle_data = await this._setup_data_execRepo.findFirst(principle_query);

      var principle_data_lang = principle_data.find(
        (x: any) => x.lang === request.lang_code
      )?.principle_data;

      var result = new GroupReportData();
      result.questions = new Questions();
      result.questions.effective_question_list = [];
      result.questions.ineffective_question_list = [];

      result.templates = await this._templates_execRepo.find({
        ...activeNonDeletedQuery,
        ...{ lang_code: request.lang_code }
      });

      result.questions.question_list =
        await this.getSurveyGroupedQuestionsForGroupReport(
          request.survey_instance_ids,
          request.survey_master_id,
          request.lang_code
        );
      result.questions.question_list?.forEach((a: any) => {
        a.effective.enablers?.forEach((b: any) => {
          b.questions?.forEach((c: any) => {
            result.questions.effective_question_list.push(c);
          });
        });

        a.ineffective.enablers?.forEach((b: any) => {
          b.questions?.forEach((c: any) => {
            result.questions.ineffective_question_list.push(c);
          });
        });
      });

      result.demographic_summary =
        this.getDemographicSummary(survey_instance_list);

      result.principle_overview = this.getPrincipleOverview(
        survey_instance_list,
        survey_instance_detail_list,
        result.questions.effective_question_list,
        result.questions.ineffective_question_list,
        principle_data_lang
      );

      result.behavioural_summary = this.getBehaviouralSummary(
        survey_instance_list,
        survey_instance_detail_list,
        result.questions.effective_question_list,
        result.questions.ineffective_question_list
      );

      result.principle_enabler_question_scores =
        this.getPrincipleEnablerQuestionScore(
          survey_instance_list,
          survey_instance_detail_list,
          result.questions.effective_question_list,
          result.questions.ineffective_question_list,
          result.templates,
          principle_data_lang
        );

      return result;
    }
  }

  private async generateSurveyReportPDF(
    data: ExportSurveyData,
    survey_instance: any
  ): Promise<Buffer> {
    const loadAssets = () => {
      const images = [
        { key: "img_founder_principle", file: "founderPrinciple.png" },
        { key: "img_dp_world_logo", file: "DP_World_Logo.png" },
        { key: "img_play", file: "play_img.png" },
        { key: "img_blank", file: "blank.png" },
        { key: "img_hand", file: "hand.png" },
        { key: "img_adapt_evolve", file: "Adapt and Evolve.png" },
        {
          key: "img_build_better_future",
          file: "Build for a Better Future.png"
        },
        { key: "img_collaborate_to_win", file: "Collaborate to Win.png" },
        { key: "img_deliver_growth", file: "Deliver Growth.png" },
        { key: "img_prioritise_customers", file: "Prioritise Customers.png" },
        { key: "img_freight_train", file: "Freight-train.jpg" },
        { key: "img_title_pagebg", file: "titlePageBG.jpg" },
        { key: "img_thankyou_page", file: "thankyouPage.jpg" },
        { key: "img_first_bg", file: "home-img.jpg" }
      ];
      const fonts = [
        { key: "font_pilate_light", file: "Pilat Light.ttf" },
        { key: "font_pilate_demi", file: "Pilat Demi.ttf" },
        { key: "font_pilate_heavy", file: "Pilat Wide Heavy.ttf" }
      ];

      images.forEach((img) => (data[img.key] = this.GetImages(img.file)));
      fonts.forEach((font) => (data[font.key] = this.GetFontBase64(font.file)));
    };

    loadAssets();

    data.img_first_bg_style = `background-image: url('${data.img_first_bg}') !important;`;

    data.full_name = survey_instance?.assigned_for_data?.full_name;
    data.report_date = moment
      .utc(survey_instance?.due_date)
      .format("MMM D, YYYY")
      .toLocaleString();

    const templates = await this._templates_execRepo.find({
      ...activeNonDeletedQuery,
      lang_code: data.lang_code
    });

    const processTemplate = (
      templateKey: string,
      replacements: Record<string, string>
    ) => {
      let html =
        templates.find((x: any) => x.system_name === templateKey)
          ?.template_data || "";
      if (replacements != null) {
        Object.entries(replacements).forEach(([key, value]) => {
          html = html.replace(key, value);
        });
      }
      return html;
    };

    data.html_introduction_report_1 = processTemplate(
      "INTRODUCTION_TO_YOUR_REPORT",
      {
        "assets/media/images/blank.png": data.img_blank,
        "assets/media/images/founderChart.jpg": data.img_founder_principle
      }
    );

    data.html_introduction_report_2 = processTemplate(
      "INTRODUCTION_TO_YOUR_REPORT_2",
      {
        "assets/media/images/blank.png": data.img_blank,
        "assets/media/images/sample_rating_bar.png": this.GetImages(
          "sample_rating_bar.png"
        ),
        "{#DemographicSummaryData#}":
          data.demographic_summary
            ?.map(
              (w) => `
                <tr style="background-color: #ffffff;">
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: left;">${w.rater_group}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: center;">${w.invited}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: center;">${w.completed}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: center;">${w.percentage}</td>
                </tr>`
            )
            .join("") || ""
      }
    );

    const processImages = (items: any[], key: string) => {
      items?.forEach((item) => {
        item.image = this.GetImages(`${item[key]}.png`);
      });
    };

    processImages(data.principle_overview, "principle_code");
    [
      "effective_highest_rated",
      "effective_lowest_rated",
      "ineffective_highest_rated"
    ].forEach((category) => {
      data.behavioural_summary[category]?.forEach((x: any) => {
        x.image = this.GetImages(`${x.principle_code}.png`);
        x.self_data = x.data[0]?.value;
        x.all_data = x.data[1]?.value;
      });
    });

    const principleMappings: Record<string, { key1: string; key2: string }> = {
      "Adapt and Evolve": {
        key1: "DESC_ADOPT_AND_EVOLVE_1",
        key2: "DESC_ADOPT_AND_EVOLVE_2"
      },
      "Build for a Better Future": {
        key1: "DESC_BUILD_BETTER_FUTURE_1",
        key2: "DESC_BUILD_BETTER_FUTURE_2"
      },
      "Collaborate to Win": {
        key1: "DESC_COLLABORATE_TO_WIN_1",
        key2: "DESC_COLLABORATE_TO_WIN_2"
      },
      "Deliver Growth": {
        key1: "DESC_DELIVER_GROWTH_1",
        key2: "DESC_DELIVER_GROWTH_2"
      },
      "Prioritise Customers": {
        key1: "DESC_PRIORITISE_CUSTOMERS_1",
        key2: "DESC_PRIORITISE_CUSTOMERS_2"
      }
    };

    data.principle_enabler_question_score?.forEach((e: any) => {
      e.principle_data.image = this.GetImages(`${e.principle_data.image}.png`);

      e.html1 = processTemplate(
        principleMappings[e.principle_data.code].key1,
        null
      );
      e.html2 = processTemplate(principleMappings[e.principle_data.code].key2, {
        [`assets/media/images/${e.principle_data.code} white.png`]:
          this.GetImages(`${e.principle_data.code} white.png`)
      });
    });

    let template_name = `survey-report-exec.${data.lang_code}`;

    const pdf = await PDFUtil.generatehbsToPdf(data, template_name, false);
    return pdf;
  }

  private async generateGroupSurveyReportPDF(
    data: ExportSurveyData
  ): Promise<Buffer> {
    const loadAssets = () => {
      const images = [
        { key: "img_founder_principle", file: "founderPrinciple.png" },
        { key: "img_dp_world_logo", file: "DP_World_Logo.png" },
        { key: "img_play", file: "play_img.png" },
        { key: "img_blank", file: "blank.png" },
        { key: "img_hand", file: "hand.png" },
        { key: "img_adapt_evolve", file: "Adapt and Evolve.png" },
        {
          key: "img_build_better_future",
          file: "Build for a Better Future.png"
        },
        { key: "img_collaborate_to_win", file: "Collaborate to Win.png" },
        { key: "img_deliver_growth", file: "Deliver Growth.png" },
        { key: "img_prioritise_customers", file: "Prioritise Customers.png" },
        { key: "img_freight_train", file: "Freight-train.jpg" },
        { key: "img_title_pagebg", file: "titlePageBG.jpg" },
        { key: "img_thankyou_page", file: "thankyouPage.jpg" },
        { key: "img_first_bg", file: "home-img.jpg" }
      ];
      const fonts = [
        { key: "font_pilate_light", file: "Pilat Light.ttf" },
        { key: "font_pilate_demi", file: "Pilat Demi.ttf" },
        { key: "font_pilate_heavy", file: "Pilat Wide Heavy.ttf" }
      ];

      images.forEach((img) => (data[img.key] = this.GetImages(img.file)));
      fonts.forEach((font) => (data[font.key] = this.GetFontBase64(font.file)));
    };

    loadAssets();

    data.img_first_bg_style = `background-image: url('${data.img_first_bg}') !important;`;

    const templates = await this._templates_execRepo.find({
      ...activeNonDeletedQuery,
      lang_code: data.lang_code
    });

    const processTemplate = (
      templateKey: string,
      replacements: Record<string, string>
    ) => {
      let html =
        templates.find((x: any) => x.system_name === templateKey)
          ?.template_data || "";
      if (replacements != null) {
        Object.entries(replacements).forEach(([key, value]) => {
          html = html.replace(key, value);
        });
      }
      return html;
    };

    data.html_introduction_report_1 = processTemplate(
      "INTRODUCTION_TO_YOUR_REPORT",
      {
        "assets/media/images/blank.png": data.img_blank,
        "assets/media/images/founderChart.jpg": data.img_founder_principle
      }
    );

    data.html_introduction_report_2 = processTemplate(
      "INTRODUCTION_TO_YOUR_REPORT_2",
      {
        "assets/media/images/blank.png": data.img_blank,
        "assets/media/images/sample_rating_bar.png": this.GetImages(
          "sample_rating_bar.png"
        ),
        "{#DemographicSummaryData#}":
          data.demographic_summary
            ?.map(
              (w) => `
                <tr style="background-color: #ffffff;">
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: left;">${w.rater_group}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: center;">${w.invited}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: center;">${w.completed}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ccc; text-align: center;">${w.percentage}</td>
                </tr>`
            )
            .join("") || ""
      }
    );

    const processImages = (items: any[], key: string) => {
      items?.forEach((item) => {
        item.image = this.GetImages(`${item[key]}.png`);
      });
    };

    processImages(data.principle_overview, "principle");
    [
      "effective_highest_rated",
      "effective_lowest_rated",
      "ineffective_highest_rated"
    ].forEach((category) => {
      data.behavioural_summary[category]?.forEach((x: any) => {
        x.image = this.GetImages(`${x.principle}.png`);
        x.self_data = x.data[0]?.value;
        x.all_data = x.data[1]?.value;
      });
    });

    const principleMappings: Record<string, { key1: string; key2: string }> = {
      "Adapt and Evolve": {
        key1: "DESC_ADOPT_AND_EVOLVE_1",
        key2: "DESC_ADOPT_AND_EVOLVE_2"
      },
      "Build for a Better Future": {
        key1: "DESC_BUILD_BETTER_FUTURE_1",
        key2: "DESC_BUILD_BETTER_FUTURE_2"
      },
      "Collaborate to Win": {
        key1: "DESC_COLLABORATE_TO_WIN_1",
        key2: "DESC_COLLABORATE_TO_WIN_2"
      },
      "Deliver Growth": {
        key1: "DESC_DELIVER_GROWTH_1",
        key2: "DESC_DELIVER_GROWTH_2"
      },
      "Prioritise Customers": {
        key1: "DESC_PRIORITISE_CUSTOMERS_1",
        key2: "DESC_PRIORITISE_CUSTOMERS_2"
      }
    };

    data.principle_enabler_question_score?.forEach((e: any) => {
      e.principle_data.image = this.GetImages(`${e.principle_data.image}.png`);

      e.html1 = processTemplate(
        principleMappings[e.principle_data.name].key1,
        null
      );
      e.html2 = processTemplate(principleMappings[e.principle_data.name].key2, {
        [`assets/media/images/${e.principle_data.name} white.png`]:
          this.GetImages(`${e.principle_data.name} white.png`)
      });
    });

    const pdf = await PDFUtil.generatehbsToPdf(
      data,
      "group-survey-report-exec",
      false
    );
    return pdf;
  }

  private async getSortedRatingScales(lang: string) {
    const ratingScales = await this._rating_scale_execRepo.find({
      ...activeNonDeletedQuery,
      lang_code: lang
    });
    return _.orderBy(ratingScales, (rating) => Number(rating.id), "asc");
  }

  private getDemographicSummary(survey_instance_list: any) {
    let demographic_summary = [];
    const calculateSummary = (surveyType: SurveyTypeExec) => {
      const instances = survey_instance_list.filter(
        (w: any) => w.survey_type === surveyType
      );
      if (instances.length > 0) {
        const summary = new SurveySummary();
        summary.rater_group = surveyType;
        summary.invited = instances.length;
        summary.completed = instances.filter(
          (q: any) =>
            q.workflow_status === WorkflowStatusExec.Completed ||
            q.workflow_status === WorkflowStatusExec.CompletedReportReady ||
            q.workflow_status === WorkflowStatusExec.Pending_Validation
        ).length;
        summary.percentage =
          ((summary.completed / summary.invited) * 100).toFixed(0) + "%";
        demographic_summary.push(summary);
      }
    };

    calculateSummary(SurveyTypeExec.Self);
    calculateSummary(SurveyTypeExec.Manager);
    calculateSummary(SurveyTypeExec.Peer);
    calculateSummary(SurveyTypeExec.Direct);
    calculateSummary(SurveyTypeExec.Other);

    return demographic_summary;
  }

  private getPrincipleOverview(
    survey_instance_list: any,
    survey_instance_detail_list: any,
    effective_question_list: any,
    ineffective_question_list: any,
    principle_data_lang: any
  ) {
    let principle_overview = new Array<SurveyPrincipleOverview>();
    const questionList = [
      ...effective_question_list,
      ...ineffective_question_list
    ];

    const principles = _.groupBy(questionList, (q) => q.principle);

    Object.entries(principles).forEach(([principle, questions]) => {
      const categoryOverview = new SurveyPrincipleOverview();
      categoryOverview.principle = principle;
      categoryOverview.principle_code = principle_data_lang.code;
      categoryOverview.data = [];

      const questionCodes = questions.map((q: any) => q.code);

      const selfRating = this.calculateRating(
        survey_instance_list,
        survey_instance_detail_list,
        questionCodes,
        [SurveyTypeExec.Self],
        ChartSurveyTypeExec.SELF
      );
      if (selfRating && selfRating.data)
        categoryOverview.data.push(selfRating.data);

      const allRatersRating = this.calculateRating(
        survey_instance_list,
        survey_instance_detail_list,
        questionCodes,
        [
          SurveyTypeExec.Manager,
          SurveyTypeExec.Direct,
          SurveyTypeExec.Peer,
          SurveyTypeExec.Other
        ],
        ChartSurveyTypeExec.ALL_RATERS_COMBINED
      );
      if (allRatersRating && allRatersRating.data)
        categoryOverview.data.push(allRatersRating.data);

      principle_overview.push(categoryOverview);
    });

    return principle_overview;
  }

  private getBehaviouralSummary(
    survey_instance_list: any,
    survey_instance_detail_list: any,
    effective_question_list: any,
    ineffective_question_list: any
  ) {
    let behavioural_summary = new BehaviouralSummary();

    const processQuestionList = (questionList: any[]) => {
      return questionList.map((question) => {
        let question_overview = new SurveyQuestionOverview();
        question_overview.data = new Array<SurveyTypeData>();
        question_overview.principle = question.principle;
        question_overview.question = question.title;

        const selfRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          [question.code],
          [SurveyTypeExec.Self],
          ChartSurveyTypeExec.SELF
        );
        if (selfRating && selfRating.data)
          question_overview.data.push(selfRating.data);

        const allRatersRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          [question.code],
          [
            SurveyTypeExec.Manager,
            SurveyTypeExec.Direct,
            SurveyTypeExec.Peer,
            SurveyTypeExec.Other
          ],
          ChartSurveyTypeExec.ALL_RATERS_COMBINED
        );
        if (allRatersRating && allRatersRating.data)
          question_overview.data.push(allRatersRating.data);

        return question_overview;
      });
    };

    const effective_question_overview = processQuestionList(
      effective_question_list || []
    );
    const ineffective_question_overview = processQuestionList(
      ineffective_question_list || []
    );

    behavioural_summary.effective_highest_rated = _(effective_question_overview)
      .orderBy(
        (x) => x.data.find((y) => y.type === "ALL RATERS COMBINED")?.value,
        "desc"
      )
      .take(5)
      .value();
    behavioural_summary.effective_lowest_rated = _(effective_question_overview)
      .orderBy(
        (x) => x.data.find((y) => y.type === "ALL RATERS COMBINED")?.value,
        "asc"
      )
      .take(5)
      .value();
    behavioural_summary.ineffective_highest_rated = _(
      ineffective_question_overview
    )
      .orderBy(
        (x) => x.data.find((y) => y.type === "ALL RATERS COMBINED")?.value,
        "desc"
      )
      .take(5)
      .value();

    return behavioural_summary;
  }

  private getPrincipleEnablerQuestionScore(
    survey_instance_list: any,
    survey_instance_detail_list: any,
    effective_question_list: any,
    ineffective_question_list: any,
    templates: any,
    principle_data_lang: any
  ) {
    // let principle_colors = PrincipleData;
    let principle_enabler_question_scores = [];
    const principles = _.groupBy(effective_question_list, (q) => q.principle);

    Object.keys(principles).forEach((principle) => {
      const principleQuestionCodes = principles[principle].map((q) => q.code);
      const principleScore = new PrincipleEnablerQuestionScore();
      principleScore.principle = principle;
      principleScore.principle_code = principle_data_lang
        ? principle_data_lang.code
        : "en";

      const principleMappings: Record<string, { key1: string; key2: string }> =
        {
          "Adapt and Evolve": {
            key1: "DESC_ADOPT_AND_EVOLVE_1",
            key2: "DESC_ADOPT_AND_EVOLVE_2"
          },
          "Build for a Better Future": {
            key1: "DESC_BUILD_BETTER_FUTURE_1",
            key2: "DESC_BUILD_BETTER_FUTURE_2"
          },
          "Collaborate to Win": {
            key1: "DESC_COLLABORATE_TO_WIN_1",
            key2: "DESC_COLLABORATE_TO_WIN_2"
          },
          "Deliver Growth": {
            key1: "DESC_DELIVER_GROWTH_1",
            key2: "DESC_DELIVER_GROWTH_2"
          },
          "Prioritise Customers": {
            key1: "DESC_PRIORITISE_CUSTOMERS_1",
            key2: "DESC_PRIORITISE_CUSTOMERS_2"
          }
        };

      const mapping = principleMappings[principle];
      if (mapping) {
        principleScore.html1 =
          templates.find((x: any) => x.system_name === mapping.key1)
            ?.template_data || "";
        principleScore.html2 =
          templates.find((x: any) => x.system_name === mapping.key2)
            ?.template_data || "";
      }

      if (principleScore.html2) {
        principleScore.html2 = principleScore.html2
          .replace("font-size: 18px;", "font-size: 24px;")
          .replace("width: 35px; height: 35px;", "width: 50px; height: 50px;")
          .replace('class="description"', 'style="font-size: 15px;"');
      }

      principleScore.combined_enabler_rating = new Array<SurveyTypeData>();
      principleScore.enabler_question_scores =
        new Array<EnablerQuestionScore>();

      const selfRating = this.calculateRating(
        survey_instance_list,
        survey_instance_detail_list,
        principleQuestionCodes,
        [SurveyTypeExec.Self],
        ChartSurveyTypeExec.SELF
      );
      if (selfRating && selfRating.data)
        principleScore.combined_enabler_rating.push(selfRating.data);

      const managerRating = this.calculateRating(
        survey_instance_list,
        survey_instance_detail_list,
        principleQuestionCodes,
        [SurveyTypeExec.Manager],
        ChartSurveyTypeExec.MANAGER
      );
      if (managerRating && managerRating.data)
        principleScore.combined_enabler_rating.push(managerRating.data);

      const directRating = this.calculateRating(
        survey_instance_list,
        survey_instance_detail_list,
        principleQuestionCodes,
        [SurveyTypeExec.Direct],
        ChartSurveyTypeExec.DIRECT_REPORTS
      );
      if (directRating && directRating.data && directRating.count >= 3) {
        principleScore.combined_enabler_rating.push(directRating.data);

        const peerOtherRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          principleQuestionCodes,
          [SurveyTypeExec.Peer, SurveyTypeExec.Other],
          ChartSurveyTypeExec.PEER_OTHER
        );
        if (peerOtherRating && peerOtherRating.data)
          principleScore.combined_enabler_rating.push(peerOtherRating.data);
      } else {
        const allOtherRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          principleQuestionCodes,
          [SurveyTypeExec.Peer, SurveyTypeExec.Direct, SurveyTypeExec.Other],
          ChartSurveyTypeExec.PEER_DIRECT_OTHER
        );
        if (allOtherRating && allOtherRating.data)
          principleScore.combined_enabler_rating.push(allOtherRating.data);
      }

      const allRatersRating = this.calculateRating(
        survey_instance_list,
        survey_instance_detail_list,
        principleQuestionCodes,
        [
          SurveyTypeExec.Manager,
          SurveyTypeExec.Direct,
          SurveyTypeExec.Peer,
          SurveyTypeExec.Other
        ],
        ChartSurveyTypeExec.ALL_RATERS_COMBINED
      );
      if (allRatersRating && allRatersRating.data)
        principleScore.combined_enabler_rating.push(allRatersRating.data);

      const enablers = _.groupBy(principles[principle], (q) => q.enabler);
      Object.keys(enablers).forEach((enabler) => {
        const enablerQuestionCodes = enablers[enabler].map((q) => q.code);
        const enablerScore = new EnablerQuestionScore();
        enablerScore.enabler = enabler;
        enablerScore.rating = new Array<SurveyTypeData>();

        const selfRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          enablerQuestionCodes,
          [SurveyTypeExec.Self],
          ChartSurveyTypeExec.SELF
        );
        if (selfRating && selfRating.data)
          enablerScore.rating.push(selfRating.data);

        const managerRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          enablerQuestionCodes,
          [SurveyTypeExec.Manager],
          ChartSurveyTypeExec.MANAGER
        );
        if (managerRating && managerRating.data)
          enablerScore.rating.push(managerRating.data);

        const directRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          enablerQuestionCodes,
          [SurveyTypeExec.Direct],
          ChartSurveyTypeExec.DIRECT_REPORTS
        );
        if (directRating && directRating.data && directRating.count >= 3) {
          enablerScore.rating.push(directRating.data);

          const peerOtherRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            enablerQuestionCodes,
            [SurveyTypeExec.Peer, SurveyTypeExec.Other],
            ChartSurveyTypeExec.PEER_OTHER
          );
          if (peerOtherRating && peerOtherRating.data)
            enablerScore.rating.push(peerOtherRating.data);
        } else {
          const allOtherRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            enablerQuestionCodes,
            [SurveyTypeExec.Peer, SurveyTypeExec.Direct, SurveyTypeExec.Other],
            ChartSurveyTypeExec.PEER_DIRECT_OTHER
          );
          if (allOtherRating && allOtherRating.data)
            enablerScore.rating.push(allOtherRating.data);
        }

        const allRatersRating = this.calculateRating(
          survey_instance_list,
          survey_instance_detail_list,
          enablerQuestionCodes,
          [
            SurveyTypeExec.Manager,
            SurveyTypeExec.Direct,
            SurveyTypeExec.Peer,
            SurveyTypeExec.Other
          ],
          ChartSurveyTypeExec.ALL_RATERS_COMBINED
        );
        if (allRatersRating && allRatersRating.data)
          enablerScore.rating.push(allRatersRating.data);

        enablerScore.effective_questions = enablers[enabler].map((question) => {
          const questionScore = new QuestionScore();
          questionScore.question = question.title;
          questionScore.rating = new Array<SurveyTypeData>();

          const selfRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [SurveyTypeExec.Self],
            ChartSurveyTypeExec.SELF
          );
          if (selfRating && selfRating.data)
            questionScore.rating.push(selfRating.data);

          const managerRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [SurveyTypeExec.Manager],
            ChartSurveyTypeExec.MANAGER
          );
          if (managerRating && managerRating.data)
            questionScore.rating.push(managerRating.data);

          const directRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [SurveyTypeExec.Direct],
            ChartSurveyTypeExec.DIRECT_REPORTS
          );
          if (directRating && directRating.data && directRating.count >= 3) {
            questionScore.rating.push(directRating.data);

            const peerOtherRating = this.calculateRating(
              survey_instance_list,
              survey_instance_detail_list,
              [question.code],
              [SurveyTypeExec.Peer, SurveyTypeExec.Other],
              ChartSurveyTypeExec.PEER_OTHER
            );
            if (peerOtherRating && peerOtherRating.data)
              questionScore.rating.push(peerOtherRating.data);
          } else {
            const allOtherRating = this.calculateRating(
              survey_instance_list,
              survey_instance_detail_list,
              [question.code],
              [
                SurveyTypeExec.Peer,
                SurveyTypeExec.Direct,
                SurveyTypeExec.Other
              ],
              ChartSurveyTypeExec.PEER_DIRECT_OTHER
            );
            if (allOtherRating && allOtherRating.data)
              questionScore.rating.push(allOtherRating.data);
          }

          const allRatersRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [
              SurveyTypeExec.Manager,
              SurveyTypeExec.Direct,
              SurveyTypeExec.Peer,
              SurveyTypeExec.Other
            ],
            ChartSurveyTypeExec.ALL_RATERS_COMBINED
          );
          if (allRatersRating && allRatersRating.data)
            questionScore.rating.push(allRatersRating.data);

          return questionScore;
        });

        principleScore.enabler_question_scores.push(enablerScore);
      });

      var principle_ineff_questions = ineffective_question_list.filter(
        (x) => x.principle === principle
      );
      principleScore.ineffective_questions = principle_ineff_questions.map(
        (question) => {
          const questionScore = new QuestionScore();
          questionScore.question = question.title;
          questionScore.rating = new Array<SurveyTypeData>();

          const selfRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [SurveyTypeExec.Self],
            ChartSurveyTypeExec.SELF
          );
          if (selfRating && selfRating.data)
            questionScore.rating.push(selfRating.data);

          const managerRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [SurveyTypeExec.Manager],
            ChartSurveyTypeExec.MANAGER
          );
          if (managerRating && managerRating.data)
            questionScore.rating.push(managerRating.data);

          const directRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [SurveyTypeExec.Direct],
            ChartSurveyTypeExec.DIRECT_REPORTS
          );
          if (directRating && directRating.data && directRating.count >= 3) {
            questionScore.rating.push(directRating.data);

            const peerOtherRating = this.calculateRating(
              survey_instance_list,
              survey_instance_detail_list,
              [question.code],
              [SurveyTypeExec.Peer, SurveyTypeExec.Other],
              ChartSurveyTypeExec.PEER_OTHER
            );
            if (peerOtherRating && peerOtherRating.data)
              questionScore.rating.push(peerOtherRating.data);
          } else {
            const allOtherRating = this.calculateRating(
              survey_instance_list,
              survey_instance_detail_list,
              [question.code],
              [
                SurveyTypeExec.Peer,
                SurveyTypeExec.Direct,
                SurveyTypeExec.Other
              ],
              ChartSurveyTypeExec.PEER_DIRECT_OTHER
            );
            if (allOtherRating && allOtherRating.data)
              questionScore.rating.push(allOtherRating.data);
          }

          const allRatersRating = this.calculateRating(
            survey_instance_list,
            survey_instance_detail_list,
            [question.code],
            [
              SurveyTypeExec.Manager,
              SurveyTypeExec.Direct,
              SurveyTypeExec.Peer,
              SurveyTypeExec.Other
            ],
            ChartSurveyTypeExec.ALL_RATERS_COMBINED
          );
          if (allRatersRating && allRatersRating.data)
            questionScore.rating.push(allRatersRating.data);

          return questionScore;
        }
      );

      principle_enabler_question_scores.push(principleScore);
    });

    return principle_enabler_question_scores;
  }

  private calculateRating(
    survey_instance_list: any,
    survey_instance_detail_list: any,
    questionCodes: string[],
    surveyType: SurveyTypeExec[] | null,
    chartType: ChartSurveyTypeExec
  ) {
    const instanceFilter =
      surveyType && surveyType.length > 0
        ? survey_instance_list.filter((instance: any) =>
            surveyType.includes(instance.survey_type)
          )
        : null;

    let relevantDetails = survey_instance_detail_list
      .filter(
        (detail: any) =>
          questionCodes.includes(detail.survey_question_code) &&
          (instanceFilter == null ||
            instanceFilter.some(
              (instance: any) => instance.id === detail.survey_instance_id
            ))
      )
      .map((detail: any) => detail.answer_data?.value || 0);

    relevantDetails = relevantDetails.filter((x: any) => x != -1);

    const surveyData = new SurveyTypeData();
    surveyData.type = chartType;
    surveyData.min = _.min(relevantDetails) || 0;
    surveyData.max = _.max(relevantDetails) || 0;
    surveyData.value = this.calculateAverage(relevantDetails);

    return { data: surveyData, count: instanceFilter.length };
  }

  private calculateAverage(values: number[]): number {
    values = values.filter((x) => x !== -1);
    if (values.length === 0) return 0;
    return _.round(_.sum(values) / values.length, 2);
  }

  private GetImages(FileName: string) {
    //var image = fs.readFileSync('./src/images/' + FileName, 'base64');
    var image = fs.readFileSync(this.imagespath + "/" + FileName, "base64");
    return "data:image/png;base64," + image.toString();
  }

  private GetFontBase64(FileName: string) {
    //var image = fs.readFileSync('./src/templates/fonts/' + FileName, 'base64');
    var image = fs.readFileSync(
      this.templatepath + "/fonts/" + FileName,
      "base64"
    );
    return "data:font/ttf;base64," + image.toString();
  }
}
