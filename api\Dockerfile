FROM node:lts-alpine as builder
# Create app directory
WORKDIR /usr/src/app
# Install app dependencies
COPY package*.json ./

ENV CHROME_BIN="/usr/bin/chromium-browser" \
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD="true"
RUN set -x \
    && apk update \
    && apk upgrade \
    && apk add --no-cache \
    udev \
    ttf-freefont \
    chromium 
    
RUN npm i
COPY . .
RUN npm run build


FROM node:lts-alpine
ENV NODE_ENV dev
# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./

ENV CHROME_BIN="/usr/bin/chromium-browser" \
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD="true"
RUN set -x \
    && apk update \
    && apk upgrade \
    && apk add --no-cache \
    udev \
    ttf-freefont \
    chromium 

RUN npm i --production
COPY --chown=node:node --from=builder /usr/src/app/dist ./dist
COPY --chown=node:node --from=builder /usr/src/app/src/images ./dist/images
COPY --chown=node:node --from=builder /usr/src/app/src/templates ./dist/templates
RUN chown -R node /usr/src/app
EXPOSE 3804
USER node    
CMD [ "node", "dist/app.js" ]


