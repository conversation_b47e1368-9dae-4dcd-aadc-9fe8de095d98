import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../infra/config';
import { AppModel } from '../base/AppModel';


export type survey_questionPk = "id";
export type survey_questionId = survey_question[survey_questionPk];

export class survey_question extends AppModel {
  id!: number;
  section_id?: number;
  title?: string;
  detail?: string;
  question_type?: string;
  option_list_id?: number;
  order_number?: number;
  applicable_survey?: string;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;
  max_option_selection?: number;
  survey_master_id?: number;
  applicable_pre_survey?: boolean;
  applicable_post_survey?: boolean;

  static initModel(sequelize: Sequelize.Sequelize): typeof survey_question {
    this.HasSoftDelete = true;
    survey_question.init({
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true
      },
      section_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      title: {
        type: DataTypes.STRING,
        allowNull: true
      },
      detail: {
        type: DataTypes.STRING,
        allowNull: true
      },
      question_type: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      option_list_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      order_number: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      applicable_survey: {
        type: DataTypes.STRING,
        allowNull: true
      },
      active: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      deleted: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      created_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      created_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      modified_on: {
        type: DataTypes.DATE,
        allowNull: true
      },
      modified_by: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      max_option_selection: {
        type: DataTypes.NUMBER,
        allowNull: true
      },
      survey_master_id: {
        type: DataTypes.NUMBER,
        allowNull: true
      },
      applicable_pre_survey: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      applicable_post_survey: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
    }, {
      sequelize,
      tableName: 'survey_question',
      schema: env.database.schema,
      timestamps: false,
      indexes: [
        {
          name: "survey_question_pk",
          unique: true,
          fields: [
            { name: "id" },
          ]
        },
      ]
    });
    return survey_question;
  }
}
