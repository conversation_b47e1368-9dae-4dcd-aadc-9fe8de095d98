import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../../infra/config';
import { AppModel } from '../../base/AppModel';

export type setup_dataPk = "id";
export type setup_dataId = setup_data[setup_dataPk];

export class setup_data extends AppModel {
  id!: number;
  setup_type?: string;
  setup_detail?: JSON;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof setup_data {
    setup_data.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    setup_type: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    setup_detail: {
      type: DataTypes.JSONB,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'setup_data',
    schema: env.database.schema_exec,
    timestamps: false,
    indexes: [
      {
        name: "setup_data_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return setup_data;
  }
}
