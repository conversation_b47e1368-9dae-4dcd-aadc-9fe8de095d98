import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../infra/config';
import { AppModel } from '../base/AppModel';


export type userprofilePk = "id";
export type userprofileId = userprofile[userprofilePk];

export class userprofile extends AppModel {
  id!: number;
  uid?: string;
  employee_id?: string;
  full_name?: string;
  job_title?: string;
  department?: string;
  email?: string;
  other_info?: object;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;

  static initModel(sequelize: Sequelize.Sequelize): typeof userprofile {
    this.HasSoftDelete = true;
    userprofile.init({
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    uid: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    employee_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    full_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    job_title: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    department: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    other_info: {
      type: DataTypes.JSON,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'userprofile',
    schema: env.database.schema,
    timestamps: false,
    indexes: [
      {
        name: "userprofile_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return userprofile;
  }
}
