import { survey_master } from "../../../../domain/entities/exec360/survey_master";
import { BaseRepository } from "../../../../domain/interfaces/repo/baserepository";
import { isurvey_master_execRepo } from "../../../../domain/interfaces/repo/exec360/isurvey_master_exec.repo";
import { Config } from "../../../config/model";
import { LoggerService } from "../../../services/loggerservice";

export class survey_master_execRepo extends BaseRepository<survey_master> implements isurvey_master_execRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_master);
    }
}