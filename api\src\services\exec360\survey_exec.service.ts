import excel from "exceljs";
import _ from "lodash";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { survey_instance } from "../../domain/entities/exec360/survey_instance";
import { CacheManager } from "../../infra/cachemanager";
import { env } from "../../infra/config";
import { Config } from "../../infra/config/model";
import { survey_instance_detail_exec } from "../../infra/database/init-model";
import {
  activeNonDeletedQuery,
  inQuery,
  isNullQuery,
  lessQuery,
  nonDeletedQuery,
  notQuery,
  notequalQuery,
  orQuery,
  textSearchQuery,
  textSearchQueryLike
} from "../../infra/database/query/general";
import { rating_scale_execRepo } from "../../infra/database/repository/exec360/rating_scale_exec.repo";
import { survey_instance_detail_execRepo } from "../../infra/database/repository/exec360/survey_instance_detail_exec.repo";
import { survey_instance_execRepo } from "../../infra/database/repository/exec360/survey_instance_exec.repo";
import { survey_master_execRepo } from "../../infra/database/repository/exec360/survey_master_exec.repo";
import { survey_question_execRepo } from "../../infra/database/repository/exec360/survey_question_exec.repo";
import { templates_execRepo } from "../../infra/database/repository/exec360/templates_exec.repo";
import { UserSearchService } from "../../infra/services/graphapi/usersearch.service";
import { LoggerService } from "../../infra/services/loggerservice";
import { WorkContextUser } from "../../shared/context/workcontext";
import {
  EmailTemplatesSurveyExec,
  EntityTypes,
  SetupTypeExec,
  StaticLinkExec,
  SurveyType,
  SurveyTypeExec,
  UserStatusExec,
  WorkflowScreenExecURL,
  WorkflowStatusExec
} from "../../shared/enum/general";
import {
  Attachment,
  InitiateExecSurvey,
  NotificationData,
  QuestionRatingScale,
  SurveyCategory,
  SurveyInstanceDetail,
  SurveyInstanceExec,
  SurveyInstanceExecListing,
  SurveyItem,
  UserParticipants,
  filters
} from "../../web/routes/app/survey-exec/model";
import { CommonService } from "../common.service";
import { EmployeeDataService } from "../employeeData.service";
import { ReportServiceExec } from "./report_exec.service";
import moment from "moment";
import { setup_data_execRepo } from "../../infra/database/repository/exec360/setup_data_exec.repo";

export class SurveyServiceExec {
  _logger: LoggerService;
  _cacheManager: CacheManager;
  _tennantId: string;
  _appId: string;
  _survey_instance_execRepo: survey_instance_execRepo;
  _survey_instance_detail_execRepo: survey_instance_detail_execRepo;
  _survey_question_execRepo: survey_question_execRepo;
  _employeeDataService: EmployeeDataService;
  _commonService: CommonService;
  _reportServiceExec: ReportServiceExec;
  _survey_master_execRepo: survey_master_execRepo;
  _rating_scale_execRepo: rating_scale_execRepo;
  _templates_execRepo: templates_execRepo;
  _userSearchService: UserSearchService;
  _setup_data_execRepo: setup_data_execRepo;

  private templatepath = path.join(__dirname, "..", "..", "templates");

  constructor(
    config: Config,
    logger: LoggerService,
    cacheManager: CacheManager,
    survey_instance_execRepo: survey_instance_execRepo,
    survey_instance_detail_execRepo: survey_instance_detail_execRepo,
    survey_question_execRepo: survey_question_execRepo,
    survey_master_execRepo: survey_master_execRepo,
    templates_execRepo: templates_execRepo,
    employeeDataService: EmployeeDataService,
    rating_scale_execRepo: rating_scale_execRepo,
    commonService: CommonService,
    reportServiceExec: ReportServiceExec,
    userSearchService: UserSearchService,
    setup_data_execRepo: setup_data_execRepo
  ) {
    this._logger = logger;
    this._cacheManager = cacheManager;
    this._tennantId = config.tennantId;
    this._appId = config.applicationId;
    this._survey_instance_execRepo = survey_instance_execRepo;
    this._survey_instance_detail_execRepo = survey_instance_detail_execRepo;
    this._employeeDataService = employeeDataService;
    this._survey_master_execRepo = survey_master_execRepo;
    this._survey_question_execRepo = survey_question_execRepo;
    this._rating_scale_execRepo = rating_scale_execRepo;
    this._commonService = commonService;
    this._reportServiceExec = reportServiceExec;
    this._templates_execRepo = templates_execRepo;
    this._userSearchService = userSearchService;
    this._setup_data_execRepo = setup_data_execRepo;
  }

  public async getSurveyMasters() {
    var query = { ...activeNonDeletedQuery };
    let survey_master = await this._survey_master_execRepo.find(
      query,
      null,
      null,
      null,
      null
    );
    return survey_master;
  }

  public async getSetupData(setup_type: string) {
    var query = { ...activeNonDeletedQuery, ...{ setup_type: setup_type } };
    let setup_data = await this._setup_data_execRepo.findFirst(query);
    return setup_data ? setup_data.setup_detail : [];
  }

  public async getSurveyInstanceById(id: number) {
    let survey_instance = await this._survey_instance_execRepo.getById(id);
    return survey_instance;
  }

  public async getSurveyInstanceByUniqueId(unique_id: string) {
    let survey_instance = await this._survey_instance_execRepo.findFirst({
      ...activeNonDeletedQuery,
      ...{ unique_id: unique_id }
    });
    return survey_instance;
  }

  public async getSurveyInstanceByIds(instance_ids: number[]) {
    var survey_instances = await this._survey_instance_execRepo.find({
      ...{ id: inQuery(instance_ids) }
    });
    return survey_instances;
  }

  public async getTemplates(lang_code: string) {
    let templates = await this._templates_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ lang_code: lang_code }
    });
    return templates;
  }

  public async getUserInprogressSurvey(username: string) {
    if (username) {
      let user_survey = await this._survey_instance_execRepo.find({
        ...activeNonDeletedQuery,
        ...textSearchQuery("assigned_to", username),
        ...{
          survey_type: SurveyType.Self
        },
        ...orQuery([
          textSearchQuery("workflow_status", WorkflowStatusExec.InProgress),
          textSearchQuery("workflow_status", WorkflowStatusExec.Submitted)
        ])
      });

      return user_survey && user_survey.length > 0;
    }
    return false;
  }

  public async getUserParticipants(username: string) {
    var result = new UserParticipants();
    if (this._commonService.isValidEmail(username)) {
      var user = await this._userSearchService.SearchUserByUserNameAndEmail(
        username,
        "null"
      );
      var user_data = this.createUserData(user);

      var employee_data = await this._employeeDataService.getEmployeedata(
        username
      );
      if (employee_data && employee_data.login_id) {
        var inprogress_survey = await this.getUserInprogressSurvey(
          employee_data.login_id
        );
        if (!inprogress_survey) {
          result.self = user_data;
          if (employee_data.line_manager_work_email) {
            result.manager = [];
            var linemanager =
              await this._employeeDataService.getEmployeesLineManager(
                employee_data.line_manager_work_email
              );
            if (linemanager) {
              var _lm_data =
                await this._userSearchService.SearchUserByUserNameAndEmail(
                  linemanager.login_id ? linemanager.login_id : "null",
                  linemanager.work_email ? linemanager.work_email : "null"
                );
              if (_lm_data != null) {
                result.manager.push(this.createUserData(_lm_data));
              }
            }
          }

          if (employee_data.person_number) {
            var directs = await this._employeeDataService.getEmployeesDirects(
              employee_data.person_number
            );
            result.direct = [];
            for await (let b of directs) {
              var _direct_data =
                await this._userSearchService.SearchUserByUserNameAndEmail(
                  b.login_id ? b.login_id : "null",
                  b.work_email ? b.work_email : "null"
                );
              if (_direct_data != null) {
                result.direct.push(this.createUserData(_direct_data));
              }
            }
          }

          if (employee_data.line_manager_person_number) {
            var peers = await this._employeeDataService.getEmployeesDirects(
              employee_data.line_manager_person_number
            );
            peers = peers?.filter(
              (q: any) => q.login_id !== employee_data.login_id
            );
            result.peer = [];
            for await (let c of peers) {
              var _peer_data =
                await this._userSearchService.SearchUserByUserNameAndEmail(
                  c.login_id ? c.login_id : "null",
                  c.work_email ? c.work_email : "null"
                );
              if (_peer_data != null) {
                result.peer.push(this.createUserData(_peer_data));
              }
            }
          }
        }
      } else {
        result.self = user_data;
      }
    }
    return result;
  }

  public async getAllSurveyInstances(
    filter: filters,
    workcontext: WorkContextUser
  ) {
    // var location_codes = await this._commonService.getLocationCodeByPermission(PermissionType.SurveyInitiateExec, workcontext.userName);
    // if (!location_codes || location_codes?.length === 0) {
    //     return { "data": [], "count": 0 };
    // }

    var data: any;
    var total_count = 0;
    var res = [];

    // if (location_codes.find(q => q?.toLowerCase() === filter.entity_name?.toLowerCase())) {
    var query = { ...nonDeletedQuery, ...{ survey_type: SurveyType.Self } };

    // if (filter.entity_name?.toLowerCase() !== 'group') {
    //     query = { ...query, ...textSearchQueryLike("entity_code", filter.entity_name) }
    // }

    if (!!filter.user_status) {
      if (filter.user_status === "survey_started") {
        query = {
          ...query,
          ...{
            workflow_status: inQuery([
              WorkflowStatusExec.InProgress,
              WorkflowStatusExec.Submitted
            ])
          }
        };
      } else if (filter.user_status === "pending_validation") {
        query = {
          ...query,
          ...{
            workflow_status: inQuery([WorkflowStatusExec.Pending_Validation])
          }
        };
      } else if (filter.user_status === "survey_completed") {
        query = {
          ...query,
          ...{
            workflow_status: inQuery([
              WorkflowStatusExec.Completed,
              WorkflowStatusExec.Expired
            ])
          }
        };
      } else if (filter.user_status === "completed_report_ready") {
        query = {
          ...query,
          ...{
            workflow_status: inQuery([WorkflowStatusExec.CompletedReportReady])
          }
        };
      }
    }

    if (!!filter.division) {
      query = { ...query, ...textSearchQuery("division", filter.division) };
    }

    if (!!filter.region) {
      query = { ...query, ...textSearchQuery("region", filter.region) };
    }

    if (!!filter.business_unit) {
      query = {
        ...query,
        ...textSearchQuery("business_unit", filter.business_unit)
      };
    }

    if (!!filter.user_name) {
      query = {
        ...query,
        ...textSearchQueryLike("assigned_for", filter.user_name)
      };
    }

    if (filter.page_size && filter.page_size > 0) {
      data = await this._survey_instance_execRepo.find(
        query,
        [["created_on", "DESC"]],
        filter.page_size,
        filter.page_size * filter.page_number
      );
      total_count = await this._survey_instance_execRepo.count(query);
    } else {
      data = await this._survey_instance_execRepo.find(query, null, null, null);
      total_count = await this._survey_instance_execRepo.count(query);
    }

    if (data?.length > 0) {
      var grouped_user_data = _.groupBy(data, (x) => x.assigned_to);
      const surveyInstances = Object.keys(grouped_user_data);

      const processSurveyInstance = async (element) => {
        const survey_instance_data = grouped_user_data[element].find(
          (x) => x.assigned_to?.toLowerCase() === element?.toLowerCase()
        );
        const item = new SurveyInstanceExecListing();
        item.id = survey_instance_data.id;
        item.due_date = this._commonService.dateWithoutTimezone(
          survey_instance_data.due_date
        );
        item.assigned_for = survey_instance_data.assigned_for;
        item.assigned_for_data = survey_instance_data.assigned_for_data;
        item.user_status = survey_instance_data.user_status;
        item.reminder_frequency_days =
          survey_instance_data.reminder_frequency_days;
        item.applicable_principles = survey_instance_data.applicable_principles;
        item.division = survey_instance_data.division;
        item.region = survey_instance_data.region;
        item.business_unit = survey_instance_data.business_unit;
        item.country = survey_instance_data.country;
        item.user_preferred_lang = survey_instance_data.user_preferred_lang;

        const userCompletedReport =
          await this._reportServiceExec.getUserCompletedSurvey(
            survey_instance_data.id
          );
        item.total_reponses = userCompletedReport
          ? userCompletedReport.length
          : 0;
        item.show_report =
          userCompletedReport &&
          userCompletedReport.length >= env.exec360reportgeneratecount;

        const survey_instance =
          await this._reportServiceExec.getUserSurveyInstances(
            survey_instance_data.id
          );
        item.total_invitees = survey_instance ? survey_instance.length : 0;
        const submitted_invitees = survey_instance?.filter(
          (x: any) =>
            x.workflow_status === WorkflowStatusExec.Submitted ||
            x.workflow_status === WorkflowStatusExec.Completed ||
            x.workflow_status === WorkflowStatusExec.Pending_Validation ||
            x.workflow_status === WorkflowStatusExec.CompletedReportReady
        );
        item.total_submitted = submitted_invitees
          ? submitted_invitees.length
          : 0;
        return item;
      };

      const res = await Promise.all(surveyInstances.map(processSurveyInstance));
      return { data: res, total_count: total_count };

      // for await (const resp of data) {
      //     resp.due_date = this._commonService.dateWithoutTimezone(resp.due_date);
      //     res.push(resp);
      // }
    }
    return { data: res, total_count: total_count };
    // }
    // return { "data": res, "total_count": total_count };
  }

  public async initiateSurvey(
    request: InitiateExecSurvey,
    workcontext: WorkContextUser
  ) {
    if (this._commonService.isValidEmail(request.users.self.login_id)) {
      var inprogress_survey = await this.getUserInprogressSurvey(
        request.users.self.login_id
      );
      let self_instance_id = 0;
      if (!inprogress_survey) {
        var _self_data = await this.getUserData(request.users.self.login_id);
        if (_self_data) {
          self_instance_id = await this.createSurveyInstance(
            _self_data.login_id,
            _self_data.login_id,
            _self_data,
            _self_data,
            request.survey_master_id,
            SurveyTypeExec.Self,
            request.due_date,
            request.division,
            request.region,
            request.business_unit,
            request.country,
            UserStatusExec.InProgress,
            WorkflowStatusExec.InProgress,
            workcontext.userName,
            true,
            request.applicable_principles,
            request.reminder_frequency_days,
            request.user_preferred_lang
          );
        }

        for await (let _manager of request.users.manager) {
          var _manager_data = await this.getUserData(_manager.login_id);
          if (_manager_data) {
            await this.createSurveyInstance(
              _manager_data.login_id,
              _self_data.login_id,
              _manager_data,
              _self_data,
              request.survey_master_id,
              SurveyTypeExec.Manager,
              request.due_date,
              request.division,
              request.region,
              request.business_unit,
              request.country,
              UserStatusExec.Draft,
              WorkflowStatusExec.Draft,
              workcontext.userName,
              false,
              request.applicable_principles,
              request.reminder_frequency_days,
              request.user_preferred_lang,
              self_instance_id
            );
          }
        }

        for await (let _direct of request.users.direct) {
          var _direct_data = await this.getUserData(_direct.login_id);
          if (_direct_data) {
            await this.createSurveyInstance(
              _direct_data.login_id,
              _self_data.login_id,
              _direct_data,
              _self_data,
              request.survey_master_id,
              SurveyTypeExec.Direct,
              request.due_date,
              request.division,
              request.region,
              request.business_unit,
              request.country,
              UserStatusExec.Draft,
              WorkflowStatusExec.Draft,
              workcontext.userName,
              false,
              request.applicable_principles,
              request.reminder_frequency_days,
              request.user_preferred_lang,
              self_instance_id
            );
          }
        }

        for await (let _peer of request.users.peer) {
          var _peer_data = await this.getUserData(_peer.login_id);
          if (_peer_data) {
            await this.createSurveyInstance(
              _peer_data.login_id,
              _self_data.login_id,
              _peer_data,
              _self_data,
              request.survey_master_id,
              SurveyTypeExec.Peer,
              request.due_date,
              request.division,
              request.region,
              request.business_unit,
              request.country,
              UserStatusExec.Draft,
              WorkflowStatusExec.Draft,
              workcontext.userName,
              false,
              request.applicable_principles,
              request.reminder_frequency_days,
              request.user_preferred_lang,
              self_instance_id
            );
          }
        }

        for await (let _other of request.users.other) {
          var _other_data = _other.login_id
            ? await this.getUserData(_other.login_id)
            : _other;
          if (_other_data) {
            await this.createSurveyInstance(
              _other_data.login_id
                ? _other_data.login_id
                : _other_data.work_email,
              _self_data.login_id,
              _other_data,
              _self_data,
              request.survey_master_id,
              SurveyTypeExec.Other,
              request.due_date,
              request.division,
              request.region,
              request.business_unit,
              request.country,
              UserStatusExec.Draft,
              WorkflowStatusExec.Draft,
              workcontext.userName,
              false,
              request.applicable_principles,
              request.reminder_frequency_days,
              request.user_preferred_lang,
              self_instance_id
            );
          }
        }

        var url = env.baseappurl + WorkflowScreenExecURL.LandingPage;
        var user_names = [];
        user_names.push(_self_data.login_id);

        if (user_names?.length > 0) {
          var notification_data = new NotificationData();
          notification_data.id = self_instance_id;
          notification_data.participant_name = _self_data.full_name;
          notification_data.awareness_session_link =
            StaticLinkExec.AwarnessLink;
          notification_data.is_external = false;
          notification_data.rater_email = _self_data?.work_email;
          notification_data.due_date = moment
            .utc(request.due_date)
            .format("MMM D, YYYY")
            .toLocaleString();

          const attachmentNames = request.user_preferred_lang
            ? [
                `DP World_360 Guidebook Updated 2024.${request.user_preferred_lang}.pdf`,
                `User Manual - Our Principles 360.${request.user_preferred_lang}.pdf`
              ]
            : [];
          const attachments = attachmentNames.map((name) => {
            const attachment = new Attachment();
            attachment.name = name;
            attachment.data = this._commonService.GetDocument(name);
            return attachment;
          });

          await this._commonService.AddNotification(
            notification_data,
            `${EmailTemplatesSurveyExec.InitiatedSurvey}.${request.user_preferred_lang}`,
            user_names,
            EntityTypes.SurveyInstanceExec,
            url,
            workcontext,
            [],
            attachments
          );
        }
        return { valid: true, is_exist: false };
      } else {
        return { valid: true, is_exist: true };
      }
    } else {
      return { valid: false, is_exist: false };
    }
  }

  public async editSurvey(
    request: InitiateExecSurvey,
    workcontext: WorkContextUser
  ) {
    var survey_list = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ master_id: request.id }
    });

    for await (let x of survey_list) {
      var survey_instance = await this._survey_instance_execRepo.getById(x.id);
      survey_instance.division = request.division;
      survey_instance.region = request.region;
      survey_instance.business_unit = request.business_unit;
      survey_instance.country = request.country;
      if (
        survey_instance.workflow_status === WorkflowStatusExec.InProgress ||
        survey_instance.workflow_status === WorkflowStatusExec.Submitted
      ) {
        survey_instance.due_date = request.due_date;
      }
      survey_instance.reminder_frequency_days = request.reminder_frequency_days;

      await this._survey_instance_execRepo.update(
        survey_instance,
        workcontext.userName
      );
    }
    return true;
  }

  public async editParticipantInfo(request: any, workcontext: WorkContextUser) {
    const survey_instance = await this._survey_instance_execRepo.getById(
      request.id
    );

    if (survey_instance.survey_type === SurveyTypeExec.Self) {
      const assigned_for_data = {
        login_id: survey_instance.assigned_for_data?.login_id || null,
        full_name: request.full_name,
        job_position: request.job_position,
        work_email: request.work_email,
        department: request.department
      };

      const survey_list = await this._survey_instance_execRepo.find({
        ...activeNonDeletedQuery,
        master_id: request.id
      });

      for (const x of survey_list) {
        const _instance = await this._survey_instance_execRepo.getById(x.id);
        _instance.assigned_for_data = assigned_for_data;
        if (_instance.survey_type === SurveyTypeExec.Self) {
          _instance.assigned_to_data = assigned_for_data;
        }

        await this._survey_instance_execRepo.update(
          _instance,
          workcontext.userName
        );
      }
    } else {
      let assigned_to_data = request.is_other_survey
        ? {
            login_id: null,
            full_name: request.full_name,
            work_email: request.work_email
          }
        : {
            login_id: survey_instance.assigned_to_data?.login_id || null,
            full_name: request.full_name,
            job_position: request.job_position,
            work_email: request.work_email,
            department: request.department
          };

      survey_instance.assigned_to_data = assigned_to_data;
      await this._survey_instance_execRepo.update(
        survey_instance,
        workcontext.userName
      );
    }

    return true;
  }

  public async getSurveyInstanceDetails(id: number, username: any) {
    let survey_instance: any;
    if (id > 0) {
      survey_instance = await this._survey_instance_execRepo.findFirst({
        id: id,
        ...activeNonDeletedQuery
      });
    } else {
      let user_survey = await this._survey_instance_execRepo.find({
        ...activeNonDeletedQuery,
        ...textSearchQuery("assigned_to", username),
        ...textSearchQuery("assigned_for", username),
        ...{
          survey_type: SurveyType.Self
        }
      });
      user_survey = _.orderBy(user_survey, (x) => Number(x.id), "desc");
      survey_instance =
        user_survey && user_survey.length > 0 ? user_survey[0] : null;
    }

    if (survey_instance) {
      var all_instance = await this._survey_instance_execRepo.find({
        ...activeNonDeletedQuery,
        ...{ master_id: survey_instance.id },
        ...notQuery("survey_type", SurveyTypeExec.Self)
      });

      const userCompletedReport =
        await this._reportServiceExec.getUserCompletedSurvey(
          survey_instance.id
        );

      survey_instance["show_report"] =
        userCompletedReport &&
        userCompletedReport.length >= env.exec360reportgeneratecount;

      return all_instance?.length
        ? [...all_instance, survey_instance]
        : [survey_instance];
    } else {
      return null;
    }
  }

  public async createSurvey(
    request: any,
    user_survey_self: any,
    workcontext: WorkContextUser
  ) {
    let valid_user = [];
    let invalid_user = [];

    var all_instance = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{
        master_id: user_survey_self.master_id
      }
    });

    if (all_instance?.length) {
      var inprogress_user_survey = all_instance.find(
        (w) =>
          w.assigned_to?.toLowerCase() === request.assigned_to?.toLowerCase() &&
          w.workflow_status !== WorkflowStatusExec.Draft
      );

      if (user_survey_self && !inprogress_user_survey) {
        var assigned_to_data = request.assigned_to_data
          ? request.assigned_to_data
          : await this.getUserData(request.assigned_to);

        const user_status = request.is_admin
          ? UserStatusExec.InProgress
          : UserStatusExec.Draft;

        const workflow_status = request.is_admin
          ? WorkflowStatusExec.InProgress
          : WorkflowStatusExec.Draft;

        var _id = await this.createSurveyInstance(
          request.assigned_to,
          user_survey_self.assigned_for,
          assigned_to_data,
          user_survey_self.assigned_for_data,
          user_survey_self.survey_master_id,
          request.survey_type,
          user_survey_self.due_date,
          user_survey_self.division,
          user_survey_self.region,
          user_survey_self.business_unit,
          user_survey_self.country,
          user_status,
          workflow_status,
          workcontext.userName,
          false,
          user_survey_self.applicable_principles,
          user_survey_self.reminder_frequency_days,
          user_survey_self.user_preferred_lang,
          user_survey_self.id,
        );

        valid_user.push(assigned_to_data?.full_name);

        if (request.is_admin) {
          var _instance = await this._survey_instance_execRepo.getById(_id);

          var notification_data = new NotificationData();
          notification_data.id = _id;
          notification_data.participant_name =
            _instance.assigned_for_data?.full_name;
          notification_data.awareness_session_link =
            StaticLinkExec.AwarnessLink;
          notification_data.rater_name = _instance.assigned_to_data?.full_name;
          notification_data.is_external =
            _instance.assigned_to_data?.login_id === null;
          notification_data.rater_email =
            _instance.assigned_to_data?.work_email;
          notification_data.survey_type = _instance.survey_type;
          notification_data.due_date = moment
            .utc(_instance.due_date)
            .format("MMM D, YYYY")
            .toLocaleString();

          const attachmentNames = user_survey_self.user_preferred_lang
            ? [
                `DP World_360 Guidebook Updated 2024.${user_survey_self.user_preferred_lang}.pdf`
              ]
            : [];
          const attachments = attachmentNames.map((name) => {
            const attachment = new Attachment();
            attachment.name = name;
            attachment.data = this._commonService.GetDocument(name);
            return attachment;
          });

          var url =
            env.baseappurl +
            WorkflowScreenExecURL.SubmitSurvey.replace(
              "{#Id#}",
              _instance.unique_id
            );

          await this._commonService.AddNotification(
            notification_data,
            `${EmailTemplatesSurveyExec.SubmitSurvey}.${_instance.user_preferred_lang}`,
            [request.assigned_to],
            EntityTypes.SurveyInstanceExec,
            url,
            workcontext,
            [],
            attachments
          );
        }
      } else {
        invalid_user.push(inprogress_user_survey.assigned_to_data?.full_name);
      }
    }

    return { valid_user: valid_user, invalid_user: invalid_user };
  }

  public async deleteSurveyInstance(id: number, workcontext: WorkContextUser) {
    var _instance = await this._survey_instance_execRepo.getById(id);
    if (_instance?.survey_type === SurveyTypeExec.Self) {
      var all_instance = await this._survey_instance_execRepo.find({
        ...activeNonDeletedQuery,
        ...{
          master_id: _instance.master_id
        }
      });

      for await (let item of all_instance) {
        await this._survey_instance_execRepo.delete(
          item.id,
          workcontext.userName
        );
      }
    } else {
      await this._survey_instance_execRepo.delete(id, workcontext.userName);
    }
    return true;
  }

  public async sendSurvey(
    request: any,
    user_survey_self: any,
    workcontext: WorkContextUser
  ) {
    let valid_user = [];
    let invalid_user = [];

    if (request?.length > 0) {
      var all_instance = await this._survey_instance_execRepo.find({
        ...activeNonDeletedQuery,
        ...{
          master_id: user_survey_self.master_id
        }
      });

      if (all_instance?.length > 0) {
        for await (let x of request) {
          var survey_instance = await this._survey_instance_execRepo.getById(
            x.id
          );
          if (
            survey_instance != null &&
            survey_instance.workflow_status === WorkflowStatusExec.Draft &&
            Number(survey_instance.master_id) ===
              Number(user_survey_self.master_id)
          ) {
            var inprogress_user_survey = all_instance.find(
              (w: any) =>
                w.assigned_to?.toLowerCase() ===
                  survey_instance.assigned_to?.toLowerCase() &&
                w.workflow_status !== WorkflowStatusExec.Draft
            );

            if (!inprogress_user_survey) {
              survey_instance.last_notification_sent = new Date();
              survey_instance.user_status = UserStatusExec.InProgress;
              survey_instance.workflow_status = WorkflowStatusExec.InProgress;
              await this._survey_instance_execRepo.update(
                survey_instance,
                workcontext.userName
              );

              var notification_data = new NotificationData();
              notification_data.id = survey_instance.id;
              notification_data.participant_name =
                survey_instance.assigned_for_data?.full_name;
              notification_data.awareness_session_link =
                StaticLinkExec.AwarnessLink;
              notification_data.rater_name =
                survey_instance.assigned_to_data?.full_name;
              notification_data.is_external =
                survey_instance.assigned_to_data?.login_id === null;
              notification_data.rater_email =
                survey_instance.assigned_to_data?.work_email;
              notification_data.survey_type = survey_instance.survey_type;
              notification_data.due_date = moment
                .utc(survey_instance.due_date)
                .format("MMM D, YYYY")
                .toLocaleString();

              const attachmentNames = user_survey_self.user_preferred_lang
                ? [
                    `DP World_360 Guidebook Updated 2024.${user_survey_self.user_preferred_lang}.pdf`
                  ]
                : [];
              const attachments = attachmentNames.map((name) => {
                const attachment = new Attachment();
                attachment.name = name;
                attachment.data = this._commonService.GetDocument(name);
                return attachment;
              });

              var url =
                env.baseappurl +
                WorkflowScreenExecURL.SubmitSurvey.replace(
                  "{#Id#}",
                  survey_instance.unique_id
                );

              await this._commonService.AddNotification(
                notification_data,
                `${EmailTemplatesSurveyExec.SubmitSurvey}.${survey_instance.user_preferred_lang}`,
                [survey_instance.assigned_to],
                EntityTypes.SurveyInstanceExec,
                url,
                workcontext,
                [],
                attachments
              );
              valid_user.push(survey_instance.assigned_to_data?.full_name);
            } else {
              invalid_user.push(
                inprogress_user_survey.assigned_to_data?.full_name
              );
            }
          } else {
            invalid_user.push(survey_instance.assigned_to_data?.full_name);
          }
        }
      }
    }
    return { valid_user: valid_user, invalid_user: invalid_user };
  }

  public async resendNotification(
    survey_instance: any,
    workcontext: WorkContextUser
  ) {
    var notification_data = new NotificationData();
    notification_data.id = survey_instance.id;
    notification_data.participant_name =
      survey_instance.assigned_for_data?.full_name;
    notification_data.awareness_session_link = StaticLinkExec.AwarnessLink;
    notification_data.rater_name = survey_instance.assigned_to_data?.full_name;
    notification_data.is_external =
      survey_instance.assigned_to_data?.login_id === null;
    notification_data.rater_email =
      survey_instance.assigned_to_data?.work_email;
    notification_data.survey_type = survey_instance.survey_type;
    notification_data.due_date = moment
      .utc(survey_instance.due_date)
      .format("MMM D, YYYY")
      .toLocaleString();

    const attachmentNames = survey_instance.user_preferred_lang
      ? [
          `DP World_360 Guidebook Updated 2024.${survey_instance.user_preferred_lang}.pdf`
        ]
      : [];

    const attachments = attachmentNames.map((name) => {
      const attachment = new Attachment();
      attachment.name = name;
      attachment.data = this._commonService.GetDocument(name);
      return attachment;
    });

    var url =
      env.baseappurl +
      WorkflowScreenExecURL.SubmitSurvey.replace(
        "{#Id#}",
        survey_instance.unique_id
      );

    await this._commonService.AddNotification(
      notification_data,
      `${EmailTemplatesSurveyExec.SubmitSurvey}.${survey_instance.user_preferred_lang}`,
      [survey_instance.assigned_to],
      EntityTypes.SurveyInstanceExec,
      url,
      workcontext,
      [],
      attachments
    );

    return true;
  }

  public async getSurveyGroupedQuestions(
    surveyInstance: any,
    lang: string
  ): Promise<any> {
    var applicable_principles = _.split(
      surveyInstance.applicable_principles,
      ","
    );

    let principle_data = await this.getSetupData(SetupTypeExec.PRINCIPLES_DATA);
    var principle_data_lang = principle_data.find(
      (x: any) => x.lang === lang
    )?.principle_data; 
    
    let principles = principle_data_lang.filter(x => applicable_principles.includes(x.code)).map(x => x.name);

    const query = {
      ...activeNonDeletedQuery,
      ...textSearchQueryLike(
        "applicable_survey_type",
        surveyInstance.survey_type
      ),
      survey_master_id: surveyInstance.survey_master_id,
      lang_code: lang,
      ...orQuery([
        { principle: inQuery(principles) },
        isNullQuery("principle")
      ])
    };

    let questionList = await this._survey_question_execRepo.find(query);
    questionList = _.orderBy(questionList, (x) => x.principle, "asc");
    const ratingScaleList = await this.getSortedRatingScales(lang);

    const effectiveRatingScale = ratingScaleList.find(
      (x) => x.system_name === "EFFECTIVE_BEHAVIOUR"
    );
    const ineffectiveRatingScale = ratingScaleList.find(
      (x) => x.system_name === "INEFFECTIVE_BEHAVIOUR"
    );

    const groupedByPrinciple = _.groupBy(
      questionList.filter((x) => x.principle !== null),
      (question) => question.principle
    );
    const principleQuestions = this._commonService.createPrincipleQuestions(
      groupedByPrinciple,
      effectiveRatingScale,
      ineffectiveRatingScale
    );

    const freeTextQuestions = questionList.filter(
      (question) => question.question_type === "free_text"
    );

    return {
      principle_questions: principleQuestions,
      free_text_questions: freeTextQuestions
    };
  }

  public async submitSurvey(
    request: SurveyInstanceDetail[],
    survey_instance: any,
    lang_code: string,
    user_name: string
  ) {
    const query = {
      ...activeNonDeletedQuery,
      ...textSearchQueryLike(
        "applicable_survey_type",
        survey_instance.survey_type
      ),
      survey_master_id: survey_instance.survey_master_id,
      lang_code: lang_code
    };

    let questionList = await this._survey_question_execRepo.find(query);
    for await (let a of request) {
      let question = questionList.find((p) => p.id === a.survey_question_id);
      if (question) {
        var data = survey_instance_detail_exec.build();
        data.survey_instance_id = survey_instance.id;
        data.survey_question_id = a.survey_question_id;
        data.survey_question_code = question.code;
        data.question_type = question.question_type;
        data.answer_data = a.answer_data;
        await this._survey_instance_detail_execRepo.create(data, user_name);
      }
    }

    survey_instance.user_status = UserStatusExec.Submitted;
    survey_instance.workflow_status = WorkflowStatusExec.Submitted;
    await this._survey_instance_execRepo.update(survey_instance, user_name);
  }

  public async reopenSurvey(
    survey_instance: any,
    request: any,
    workcontext: WorkContextUser
  ) {
    var all_instance = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{
        master_id: survey_instance.master_id
      }
    });

    // self expired to in-progress
    var self_exipred = all_instance.find(
      (x: any) =>
        x.survey_type === SurveyType.Self &&
        x.workflow_status === WorkflowStatusExec.Expired
    );
    if (self_exipred) {
      var item = await this._survey_instance_execRepo.getById(self_exipred.id);
      item.user_status = UserStatusExec.InProgress;
      item.workflow_status = WorkflowStatusExec.InProgress;
      item.due_date = request.due_date;
      item.reminder_frequency_days = request.reminder_frequency_days;
      await this._survey_instance_execRepo.update(item, workcontext.userName);
    }

    //all cancelled to in-progress
    var all_cancelled = all_instance.filter(
      (x: any) => x.workflow_status === WorkflowStatusExec.Cancelled
    );
    for await (const survey_instance of all_cancelled) {
      var item = await this._survey_instance_execRepo.getById(
        survey_instance.id
      );
      item.user_status = UserStatusExec.InProgress;
      item.workflow_status = WorkflowStatusExec.InProgress;
      item.due_date = request.due_date;
      item.reminder_frequency_days = request.reminder_frequency_days;
      await this._survey_instance_execRepo.update(item, workcontext.userName);
    }

    //all completed to submitted
    var all_completed = all_instance.filter(
      (x: any) =>
        x.workflow_status === WorkflowStatusExec.Completed ||
        x.workflow_status === WorkflowStatusExec.Pending_Validation ||
        x.workflow_status === WorkflowStatusExec.CompletedReportReady
    );
    for await (const survey_instance1 of all_completed) {
      var item = await this._survey_instance_execRepo.getById(
        survey_instance1.id
      );
      item.user_status = UserStatusExec.Submitted;
      item.workflow_status = WorkflowStatusExec.Submitted;
      item.due_date = request.due_date;
      item.reminder_frequency_days = request.reminder_frequency_days;
      await this._survey_instance_execRepo.update(item, workcontext.userName);
    }

    return true;
  }

  public async getAssignedSurvey(workcontext: WorkContextUser) {
    let user_survey = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{
        workflow_status: WorkflowStatusExec.InProgress
      },
      ...textSearchQuery("assigned_to", workcontext.userName)
      //   ...{ survey_type: notequalQuery(SurveyType.Self) }
    });
    return user_survey;
  }

  public async getAllInvitees(id: number) {
    var survey_instance = await this._survey_instance_execRepo.getById(id);
    if (survey_instance != null) {
      let invitees = await this._survey_instance_execRepo.find({
        ...activeNonDeletedQuery,
        ...{ master_id: survey_instance.master_id },
        ...notQuery("workflow_status", WorkflowStatusExec.Draft)
      });

      return invitees;
    }
    return null;
  }

  public async downloadInviteesBySurveyId(survey_id: number) {
    const excelWorkbook = new excel.Workbook();
    await excelWorkbook.xlsx.readFile(
      this.templatepath + "/SurveyExec-Invitees.xlsx"
    );

    var data_response = await this.getAllInvitees(survey_id);

    const worksheet = excelWorkbook.getWorksheet("Survey-Invitees");
    worksheet.columns = [
      { header: "Name", key: "user_name", width: 40 },
      { header: "Email", key: "user_email", width: 40 },
      { header: "Survey Type", key: "survey_type", width: 15 },
      { header: "Status", key: "user_status", width: 20 }
    ];

    var grouped_data = _.groupBy(data_response, (x) => x.survey_type);

    Object.keys(grouped_data).forEach((survey_type, index) => {
      var result = data_response?.filter((q) => q.survey_type === survey_type);
      result?.forEach((x: any) => {
        var data = {
          user_name: x.assigned_to_data?.full_name,
          user_email: x.assigned_to_data?.work_email,
          survey_type: x.survey_type,
          user_status:
            x.user_status === UserStatusExec.InProgress ||
            x.user_status === UserStatusExec.Cancelled
              ? "Not Submitted"
              : "Submitted"
        };
        worksheet.addRow(data);
      });
    });

    return await excelWorkbook.xlsx.writeBuffer();
  }

  public async bulkReopenSurvey(
    survey_instance_ids: any,
    due_date: any,
    workcontext: WorkContextUser
  ) {
    var all_instance = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ master_id: inQuery(survey_instance_ids) }
    });

    for await (const survey_instance of survey_instance_ids) {
      let _survey_instances = all_instance.filter(
        (x) => x.master_id === survey_instance
      );

      let self_survey = _survey_instances.find(
        (x: any) => x.survey_type === SurveyType.Self
      );

      if (
        self_survey.workflow_status === WorkflowStatusExec.Completed ||
        self_survey.workflow_status === WorkflowStatusExec.Pending_Validation ||
        self_survey.workflow_status === WorkflowStatusExec.CompletedReportReady
      ) {
        // self expired to in-progress
        if (self_survey.workflow_status === WorkflowStatusExec.Expired) {
          var item = await this._survey_instance_execRepo.getById(
            self_survey.id
          );
          item.user_status = UserStatusExec.InProgress;
          item.workflow_status = WorkflowStatusExec.InProgress;
          item.due_date = due_date;
          await this._survey_instance_execRepo.update(
            item,
            workcontext.userName
          );
        }

        //all cancelled to in-progress
        var all_cancelled = _survey_instances.filter(
          (x: any) => x.workflow_status === WorkflowStatusExec.Cancelled
        );
        for await (const survey_instance of all_cancelled) {
          var item = await this._survey_instance_execRepo.getById(
            survey_instance.id
          );
          item.user_status = UserStatusExec.InProgress;
          item.workflow_status = WorkflowStatusExec.InProgress;
          item.due_date = due_date;
          await this._survey_instance_execRepo.update(
            item,
            workcontext.userName
          );
        }

        //all completed to submitted
        var all_completed = _survey_instances.filter(
          (x: any) =>
            x.workflow_status === WorkflowStatusExec.Completed ||
            x.workflow_status === WorkflowStatusExec.Pending_Validation ||
            x.workflow_status === WorkflowStatusExec.CompletedReportReady
        );
        for await (const survey_instance1 of all_completed) {
          var item = await this._survey_instance_execRepo.getById(
            survey_instance1.id
          );
          item.user_status = UserStatusExec.Submitted;
          item.workflow_status = WorkflowStatusExec.Submitted;
          item.due_date = due_date;
          await this._survey_instance_execRepo.update(
            item,
            workcontext.userName
          );
        }
      }
    }

    return true;
  }

  public async bulkChangeDueDate(
    survey_instance_ids: any,
    due_date: any,
    workcontext: WorkContextUser
  ) {
    var all_instance = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ master_id: inQuery(survey_instance_ids) }
    });

    for await (const survey_instance of survey_instance_ids) {
      let _survey_instances = all_instance.filter(
        (x) => x.master_id === survey_instance
      );

      var self_not_completed = _survey_instances.find(
        (x: any) =>
          x.survey_type === SurveyType.Self &&
          (x.workflow_status !== WorkflowStatusExec.Completed ||
            x.workflow_status !== WorkflowStatusExec.Pending_Validation ||
            x.workflow_status === WorkflowStatusExec.CompletedReportReady)
      );
      if (self_not_completed != null) {
        for await (const survey_instance1 of _survey_instances) {
          var item = await this._survey_instance_execRepo.getById(
            survey_instance1.id
          );
          item.due_date = due_date;
          await this._survey_instance_execRepo.update(
            item,
            workcontext.userName
          );
        }
      }
    }

    return true;
  }

  public async validateReport(
    survey_instance_id: number,
    workcontext: WorkContextUser
  ) {
    var all_instance = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ master_id: survey_instance_id },
      ...{ workflow_status: WorkflowStatusExec.Pending_Validation }
    });

    const userCompletedReport =
      await this._reportServiceExec.getUserCompletedSurvey(survey_instance_id);

    var valid_generate_report =
      userCompletedReport &&
      userCompletedReport.length >= env.exec360reportgeneratecount;

    for await (const survey_instance of all_instance) {
      var item = await this._survey_instance_execRepo.getById(
        survey_instance.id
      );

      if (valid_generate_report) {
        item.user_status = UserStatusExec.CompletedReportReady;
        item.workflow_status = WorkflowStatusExec.CompletedReportReady;
      } else {
        item.user_status = UserStatusExec.Completed;
        item.workflow_status = WorkflowStatusExec.Completed;
      }
      await this._survey_instance_execRepo.update(item, workcontext.userName);
    }

    var self_survey = all_instance.find(
      (x: any) => x.survey_type === SurveyType.Self
    );
    if (self_survey) {
      var notification_data = new NotificationData();
      notification_data.id = self_survey.id;
      notification_data.participant_name =
        self_survey.assigned_for_data?.full_name;
      notification_data.awareness_session_link = StaticLinkExec.AwarnessLink;
      notification_data.guide_book_link = StaticLinkExec.GuideBook;
      notification_data.is_external = false;
      // notification_data.due_date = moment.utc(self_survey.due_date).format("MMM D, YYYY").toLocaleString();

      var url =
        env.baseappurl +
        WorkflowScreenExecURL.UserReport.replace("{#Id#}", self_survey.id);

      const userCompletedReport =
        await this._reportServiceExec.getUserCompletedSurvey(self_survey.id);
      try {
        if (userCompletedReport?.length >= env.exec360reportgeneratecount) {
          await this._commonService.AddNotification(
            notification_data,
            `${EmailTemplatesSurveyExec.SurveyFinalReport}.${self_survey.user_preferred_lang}`,
            [self_survey.assigned_for],
            EntityTypes.SurveyInstanceExec,
            url,
            null,
            [],
            null
          );
        }
      } catch (ex) {
        this._logger.error(ex, ex);
      }
    }
    return true;
  }

  public async completedWithoutNotGenerated(
    survey_instance_id: number,
    workcontext: WorkContextUser
  ) {
    var all_instance = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{ master_id: survey_instance_id }
    });

    var pending_validation_instance = all_instance.filter(
      (r) => r.workflow_status === WorkflowStatusExec.Pending_Validation
    );

    if (
      !pending_validation_instance ||
      pending_validation_instance?.length < 5
    ) {
      for await (const survey_instance of pending_validation_instance) {
        var item = await this._survey_instance_execRepo.getById(
          survey_instance.id
        );
        item.user_status = UserStatusExec.Completed;
        item.workflow_status = WorkflowStatusExec.Completed;

        await this._survey_instance_execRepo.update(item, workcontext.userName);
      }

      // var self_survey = all_instance.find(
      //   (x: any) => x.survey_type === SurveyType.Self
      // );
      // if (self_survey) {
      //   var notification_data = new NotificationData();
      //   notification_data.id = self_survey.id;
      //   notification_data.participant_name =
      //     self_survey.assigned_for_data?.full_name;
      //   notification_data.awareness_session_link = StaticLinkExec.AwarnessLink;
      //   notification_data.guide_book_link = StaticLinkExec.GuideBook;
      //   notification_data.is_external = false;
      //   // notification_data.due_date = moment.utc(self_survey.due_date).format("MMM D, YYYY").toLocaleString();

      //   var url =
      //     env.baseappurl +
      //     WorkflowScreenExecURL.UserReport.replace("{#Id#}", self_survey.id);

      //   try {
      //     await this._commonService.AddNotification(
      //       notification_data,
      //       EmailTemplatesSurveyExec.SurveyReportNotGenerated,
      //       [self_survey.assigned_for],
      //       EntityTypes.SurveyInstanceExec,
      //       url,
      //       null,
      //       [],
      //       null
      //     );
      //   } catch (ex) {
      //     this._logger.error(ex, ex);
      //   }
      // }
    }
    return true;
  }

  public async ExecSurveyJob() {
    await this.SendReminderNotification();
    await this.completeSurveys();
  }

  public async SendReminderNotification() {
    let all_surveys = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...textSearchQuery("workflow_status", WorkflowStatusExec.InProgress)
    });

    for await (const item of all_surveys) {
      if (
        item.last_notification_sent == null ||
        this._commonService.addDaysToDate(
          item.last_notification_sent,
          item.reminder_frequency_days
        ) <= new Date()
      ) {
        try {
          var notification_data = new NotificationData();
          notification_data.id = item.id;
          notification_data.participant_name =
            item.assigned_for_data?.full_name;
          notification_data.awareness_session_link =
            StaticLinkExec.AwarnessLink;
          notification_data.rater_name = item.assigned_to_data?.full_name;
          notification_data.is_external =
            item.assigned_to_data?.login_id === null;
          notification_data.rater_email = item.assigned_to_data?.work_email;
          notification_data.survey_type = item.survey_type;
          notification_data.due_date = moment
            .utc(item.due_date)
            .format("MMM D, YYYY")
            .toLocaleString();

          var url =
            env.baseappurl +
            WorkflowScreenExecURL.SubmitSurvey.replace(
              "{#Id#}",
              item.unique_id
            );

          if (item.survey_type === SurveyTypeExec.Self) {
            const attachmentNames = item.user_preferred_lang
              ? [
                  `DP World_360 Guidebook Updated 2024.${item.user_preferred_lang}.pdf`,
                  `User Manual - Our Principles 360.${item.user_preferred_lang}.pdf`
                ]
              : [];
            const attachments = attachmentNames.map((name) => {
              const attachment = new Attachment();
              attachment.name = name;
              attachment.data = this._commonService.GetDocument(name);
              return attachment;
            });

            await this._commonService.AddNotification(
              notification_data,
              `${EmailTemplatesSurveyExec.SurveyParticipantReminder}.${item.user_preferred_lang}`,
              [item.assigned_to],
              EntityTypes.SurveyInstanceExec,
              url,
              null,
              [],
              attachments
            );

            var url1 = env.baseappurl + WorkflowScreenExecURL.LandingPage;

            await this._commonService.AddNotification(
              notification_data,
              `${EmailTemplatesSurveyExec.SurveyParticipantAddRaterReminder}.${item.user_preferred_lang}`,
              [item.assigned_to],
              EntityTypes.SurveyInstanceExec,
              url1,
              null,
              [],
              attachments
            );
          } else {
            const attachmentNames1 = item.user_preferred_lang
              ? [
                  `DP World_360 Guidebook Updated 2024.${item.user_preferred_lang}.pdf`
                ]
              : [];
            const attachments1 = attachmentNames1.map((name) => {
              const attachment = new Attachment();
              attachment.name = name;
              attachment.data = this._commonService.GetDocument(name);
              return attachment;
            });

            await this._commonService.AddNotification(
              notification_data,
              `${EmailTemplatesSurveyExec.SurveyRaterReminder}.${item.user_preferred_lang}`,
              [item.assigned_to],
              EntityTypes.SurveyInstanceExec,
              url,
              null,
              [],
              attachments1
            );
          }
          var survey_instance = await this._survey_instance_execRepo.getById(
            item.id
          );
          survey_instance.last_notification_sent = new Date();
          await this._survey_instance_execRepo.update(
            survey_instance,
            "System"
          );
        } catch (ex) {
          this._logger.error(ex, ex);
        }
      }
    }
  }

  public async completeSurveys() {
    let deadline_completed_surveys = await this._survey_instance_execRepo.find({
      ...activeNonDeletedQuery,
      ...{
        due_date: lessQuery(
          new Date().toISOString().split("T")[0] + "T00:00:00.000Z"
        )
      }
    });

    deadline_completed_surveys?.forEach((s) => {
      s.due_date = this._commonService.dateWithoutTimezone(s.due_date);
    });

    let submitted_surveys = deadline_completed_surveys.filter(
      (x: any) => x.workflow_status === WorkflowStatusExec.Submitted
    );

    for await (const submitted_survey of submitted_surveys) {
      var survey_instance = await this._survey_instance_execRepo.getById(
        Number(submitted_survey.id)
      );
      survey_instance.user_status = UserStatusExec.Pending_Validation;
      survey_instance.workflow_status = WorkflowStatusExec.Pending_Validation;
      await this._survey_instance_execRepo.update(survey_instance, "System");
    }

    await this.cancelSurveyReports(deadline_completed_surveys);

    // var self_submitted_reports = submitted_surveys.filter((x: any) => x.survey_type === SurveyType.Self);
    // for await (const self_survey of self_submitted_reports) {
    //     var notification_data = new NotificationData();
    //     notification_data.id = self_survey.id;
    //     notification_data.participant_name = self_survey.assigned_for_data?.full_name;
    //     notification_data.awareness_session_link = StaticLink.AwarnessLink;
    //     notification_data.rater_name = self_survey.assigned_to_data?.full_name;
    //     notification_data.is_external = false;
    //     notification_data.rater_email = self_survey.assigned_to_data?.work_email;
    //     notification_data.due_date = moment.utc(self_survey.due_date).format("MMM D, YYYY").toLocaleString();

    //     var url = env.baseappurl + WorkflowScreenExecURL.UserReport.replace('{#Id#}', self_survey.id);

    //     const userCompletedReport = await this._reportServiceExec.getUserCompletedSurvey(self_survey.id);
    //     try {
    //         if (userCompletedReport?.length >= 3) {
    //             await this._commonService.AddNotification(notification_data, EmailTemplatesSurveyExec.SurveyFinalReport, [self_survey.assigned_for], EntityTypes.SurveyInstanceExec, url, null, [], null);
    //         } else {
    //             await this._commonService.AddNotification(notification_data, EmailTemplatesSurveyExec.SurveyReportNotGenerated, [self_survey.assigned_for], EntityTypes.SurveyInstanceExec, url, null, [], null);
    //         }
    //     }
    //     catch (ex) {
    //         this._logger.error(ex, ex);
    //     }

    // }
  }

  public async cancelSurveyReports(all_surveys: any) {
    let self_inprogress_surveys = all_surveys.filter(
      (x: any) =>
        x.survey_type === SurveyType.Self &&
        (x.workflow_status === WorkflowStatusExec.Draft ||
          x.workflow_status === WorkflowStatusExec.InProgress)
    );

    for await (const item of self_inprogress_surveys) {
      var survey_instance = await this._survey_instance_execRepo.getById(
        Number(item.id)
      );
      survey_instance.user_status = UserStatusExec.Expired;
      survey_instance.workflow_status = WorkflowStatusExec.Expired;
      await this._survey_instance_execRepo.update(survey_instance, "System");
    }

    let other_inprogress_surveys = all_surveys.filter(
      (x: any) =>
        x.survey_type !== SurveyType.Self &&
        x.workflow_status === WorkflowStatusExec.InProgress
    );

    for await (const item of other_inprogress_surveys) {
      var survey_instance = await this._survey_instance_execRepo.getById(
        Number(item.id)
      );
      survey_instance.user_status = UserStatusExec.Cancelled;
      survey_instance.workflow_status = WorkflowStatusExec.Cancelled;
      await this._survey_instance_execRepo.update(survey_instance, "System");
    }

    let other_draft_surveys = all_surveys.filter(
      (x: any) =>
        x.survey_type !== SurveyType.Self &&
        x.workflow_status === WorkflowStatusExec.Draft
    );

    for await (const item of other_draft_surveys) {
      var survey_instance = await this._survey_instance_execRepo.getById(
        Number(item.id)
      );
      survey_instance.active = false;
      survey_instance.deleted = true;
      await this._survey_instance_execRepo.update(survey_instance, "System");
    }
  }

  private async getSortedRatingScales(lang: string) {
    const ratingScales = await this._rating_scale_execRepo.find({
      ...activeNonDeletedQuery,
      lang_code: lang
    });
    return _.orderBy(ratingScales, (rating) => Number(rating.id), "asc");
  }

  private async createSurveyInstance(
    assigned_to: string,
    assigned_for: string,
    assigned_to_data: any,
    assigned_for_data: any,
    survey_master_id: number,
    survey_type: string,
    due_date: any,
    division: string,
    region: string,
    business_unit: string,
    country: string,
    user_status: string,
    workflow_status: string,
    username: string,
    update_last_notification_sent: boolean,
    applicable_principles: string,
    reminder_frequency_days: number,
    user_preferred_lang: string,
    master_id?: number
  ) {
    var survey = survey_instance.build();
    survey.assigned_to = assigned_to;
    survey.assigned_for = assigned_for;
    survey.survey_type = survey_type;
    survey.due_date = due_date;
    survey.division = division;
    survey.region = region;
    survey.user_status = user_status;
    survey.workflow_status = workflow_status;
    survey.assigned_to_data = assigned_to_data;
    survey.assigned_for_data = assigned_for_data;
    survey.survey_master_id = survey_master_id;
    survey.applicable_principles = applicable_principles;
    survey.reminder_frequency_days = reminder_frequency_days;
    survey.unique_id = uuidv4();
    survey.business_unit = business_unit;
    survey.country = country;
    survey.user_preferred_lang = user_preferred_lang ?? "en";

    if (update_last_notification_sent) {
      survey.last_notification_sent = new Date();
    }

    if (master_id) {
      survey.master_id = master_id;
    }
    var result = await this._survey_instance_execRepo.create(survey, username);
    if (!master_id) {
      var surevy_instance = await this._survey_instance_execRepo.getById(
        Number(result.id)
      );
      surevy_instance.master_id = result.id;
      await this._survey_instance_execRepo.update(surevy_instance, username);
    }
    return result.id;
  }

  private async getUserData(login_id: string) {
    var user = await this._userSearchService.SearchUserByUserNameAndEmail(
      login_id,
      "null"
    );
    if (user != null) {
      // var employee_data = await this._employeeDataService.getEmployeedata(login_id);
      // if (employee_data != null) {
      //     employee_data.login_id = user.userPrincipalName;
      //     return this.createUserData(employee_data);
      // } else {
      var user_data = {
        login_id: user.userPrincipalName,
        full_name: user.displayName,
        job_position: user.jobTitle,
        work_email: user.mail,
        department: user.department
      };
      return user_data;
      // }
    }
    return null;
  }

  private createUserData(user: any) {
    var user_data = {
      login_id: user.userPrincipalName,
      full_name: user.displayName,
      job_position: user.jobTitle,
      work_email: user.mail,
      department: user.department
    };

    return user_data;
  }
}
