import { Server } from "./web";
import { LoggerService } from "./infra/services/loggerservice";
import Database from "./infra/database";
import { CacheService } from "./infra/services/cache/service";

export class Application {
    _server: Server;
    _database: Database;
    _logger: LoggerService;
    _cache: CacheService

    constructor(server: Server, logger: LoggerService, database: Database, cacheService: CacheService) {
      this._server = server;
      this._database = database;
      this._logger = logger;
      this._cache = cacheService;
      // if(database && database.options.logging) {
      //   database.options.logging = logger.ainfo.bind(logger);
      // }
    }
  
    async start() {
      await this._database.connect();
      this._server.start();
      await this._cache.Startup();
    }
  }
