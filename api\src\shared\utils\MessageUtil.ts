import moment from "moment";
import { adminApiPost } from "../../infra/services/adminapi/adminApi";
import { AdminUrl } from "../enum/adminurl";
import { env } from "../../infra/config";


export class MessageUtil {

    public static async GenerateMessageFromTemplate(templatename: string, data: any, url?: string) {

        var noticationtemplate = await adminApiPost(AdminUrl.GetNotificationTemplate, { "system_name": templatename });

        noticationtemplate.placeholders.forEach((item: any) => {
            var dataitem: any = data;
            var value = '';

            var placeholders = item.replace('#', '').split('.');

            for (let index = 0; index < placeholders.length; index++) {
                if (placeholders.length == index + 1) {
                    value = dataitem[placeholders[index]];
                }
                else {
                    dataitem = dataitem[placeholders[index]];
                }
            }
            if (value != '' && value != undefined && value != 'undefined') {
                noticationtemplate.subject = noticationtemplate.subject.replaceAll('{#' + item + '#}', value);
                noticationtemplate.body = noticationtemplate.body.replaceAll('{#' + item + '#}', value);
            }
        });

        noticationtemplate.body = noticationtemplate.body.replaceAll('{#Url#}', url);
        noticationtemplate.body = noticationtemplate.body.replaceAll('{#url#}', url);
        if (data.due_date) {
            var due_date = moment.utc(data.due_date).format("MMM D, YYYY").toLocaleString();
            noticationtemplate.body = noticationtemplate.body.replaceAll('{#Deadline#}', due_date);
        }
        if (data.post_program_due_date) {
            var post_program_due_date = moment.utc(data.post_program_due_date).format("MMM D, YYYY").toLocaleString();
            noticationtemplate.body = noticationtemplate.body.replaceAll('{#PostDeadline#}', post_program_due_date);
        }

        
        noticationtemplate.body = noticationtemplate.body.replaceAll('{#ParticipantName#}', data.assigned_for_data?.full_name);
        noticationtemplate.subject = noticationtemplate.subject.replaceAll('{#ParticipantName#}', data.assigned_for_data?.full_name);

        return noticationtemplate;
    }


}