import * as axios from "axios";
import { env } from "../../config";

const tenantId = env.tennantId;
const applicationId = env.applicationId;
const adminapiurl = env.adminapiurl;

const adminApiGet = async (path: string) => {
  const options: axios.AxiosRequestConfig = {
    method: "GET",
    headers: {
      tennantId: tenantId,
      applicationid: applicationId,
      Accept: "text/html, application/json"
    }
  };

  var res = await axios.default.get(`${adminapiurl}${path}`, options);

  return res.data;
};

const adminApiPost = async (path: string, data: any) => {
  const options: axios.AxiosRequestConfig = {
    method: "POST",
    headers: {
      tennantId: tenantId,
      applicationid: applicationId,
      Accept: "text/html, application/json"
    }
  };

  var res = await axios.default.post(`${adminapiurl}${path}`, data, options);

  return res.data;
};

export { adminApiGet, adminApiPost };
