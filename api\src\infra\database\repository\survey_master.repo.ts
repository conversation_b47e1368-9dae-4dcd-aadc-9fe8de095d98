import { survey_master } from "../../../domain/entities/survey_master";
import { BaseRepository } from "../../../domain/interfaces/repo/baserepository";
import { isurvey_masterRepo } from "../../../domain/interfaces/repo/isurvey_master.repo";
import { Config } from "../../config/model";
import { LoggerService } from "../../services/loggerservice";

export class survey_masterRepo extends BaseRepository<survey_master> implements isurvey_masterRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_master);
    }
}