export enum RoleType {
    Admin = 'Admin',
}
export enum PermissionType {
    SurveyInitiate = '360.Initiate',
    SurveyAccess = '360.Access',
    SurveyInitiateExec = '360.InitiateExec',
    SurveyAccessExec = '360.AccessExec'
}
export enum NavType {
    Side = 'side',
    Top = 'top'
}

export enum EntityTypes {
    SurveyInstance = 'SurveyInstance',
    SurveyInstanceExec = 'SurveyInstanceExec'
}

export enum ActionType {
    Submit = 'Submit'
}
export enum ScreenURL {
    PerformSurvey = 'surveys/perform/{#Id#}'
}
export enum EmailTemplatesSurvey {
    SubmitSurvey = '360Assessment.SubmitSurvey',
    InitiatedSurvey = '360Assessment.InitiatedSurvey',
    SurveyReminder = '360Assessment.SurveyReminder',
    SurveyFinalReport = '360Assessment.SurveyFinalReport',
    SurveyReportNotGenerated = '360Assessment.SurveyReportNotGenerated',
    PostInitiatedSurvey = '360Assessment.PostInitiatedSurvey',
    PostSurveyReminder = '360Assessment.PostSurveyReminder',
    PostSubmitSurvey = '360Assessment.PostSubmitSurvey',
    PostSurveyFinalReport = '360Assessment.PostSurveyFinalReport',
    PostSurveyReportNotGenerated = '360Assessment.PostSurveyReportNotGenerated',
}
export enum TaskStatus {
    Pending = 'Pending',
    Inprogress = 'Inprogress',
    Completed = 'Completed',
    Cancelled = 'Cancelled'
}
export enum ActionPerfomed {
    Submitted = 'Submitted',
    Completed = 'Completed',
    Cancelled = 'Cancelled'
}

export enum TagsToReplace {
    AssignedFor = '{#AssignedFor#}',
}

export enum UserStatus {
    Draft = 'Draft',
    Submitted = 'Submitted',
    InProgress = 'In Progress',
    Completed = 'Completed',
    Archived = 'Archived',
    Cancelled = 'Cancelled',
    Expired = 'Expired',
    ArchivedExpired = 'Expired and Archived'
}

export enum WorkflowStatus {
    Draft = 'draft',
    Submitted = 'submitted',
    InProgress = 'in_progress',
    Completed = 'completed',
    Archived = 'archived',
    Cancelled = 'cancelled',
    Expired = 'expired',
    ArchivedExpired = 'expired_archived',
}

export enum UserStatus_Cohort {
    Open = 'Open',
    SurveyCompleted = 'Completed',
    PostSurvey = 'Post Survey',
    Closed = 'Closed'
}

export enum WorkflowStatus_Cohort {
    Open = 'open',
    SurveyCompleted = 'completed',
    PostSurvey = 'post_survey_started',
    Closed = 'closed'
}

export enum SurveyType {
    Self = 'Self',
    Direct = 'Direct',
    Manager = 'Manager',
    Peer = 'Peer',
    Other = 'Other'
}

export enum WorkflowScreenURL {
    SubmitSurvey = '/submit-survey/{#Id#}',
    InitiatedSurvey = '/user-landing',
    UserReport = '/user-report/{#Id#}',
    UserReportWay2 = '/user-report-way2/{#Id#}',
}

export enum SurveyTypeExec {
    Self = 'Self',
    Direct = 'Direct',
    Manager = 'Manager',
    Peer = 'Peer',
    Other = 'Other'
}

export enum UserStatusExec {
    Draft = 'Draft',
    InProgress = 'In Progress',
    Submitted = 'Submitted',
    Completed = 'Completed',
    Cancelled = 'Cancelled',
    Expired = 'Expired',
    Pending_Validation = 'Pending Validation',
    CompletedReportReady = 'Completed & Report Ready',
}

export enum WorkflowStatusExec {
    Draft = 'draft',
    InProgress = 'in_progress',
    Submitted = 'submitted',
    Completed = 'completed',
    Cancelled = 'cancelled',
    Expired = 'expired',
    Pending_Validation = 'pending_validation',
    CompletedReportReady = 'completed_report_ready',
}

export enum EmailTemplatesSurveyExec {
    InitiatedSurvey = '360Exec.InitiatedSurvey',
    SurveyParticipantReminder = '360Exec.SurveyParticipantReminder',
    SurveyParticipantAddRaterReminder = '360Exec.SurveyParticipantAddRaterReminder',
    SubmitSurvey = '360Exec.SubmitSurvey',
    SurveyRaterReminder = '360Exec.SurveyRaterReminder',
    SurveyFinalReport = '360Exec.SurveyFinalReport',
    SurveyReportNotGenerated = '360Exec.SurveyReportNotGenerated',
}

export enum WorkflowScreenExecURL {
    SubmitSurvey = '/exec360-user/submit-survey/{#Id#}',
    LandingPage = '/exec360/user-landing',
    UserReport = '/exec360/user-report/{#Id#}'
}

export enum StaticLink {
    AwarnessLink = 'https://dpworld.sharepoint.com/:v:/s/SpringPointDPW/EXhgzzMExhxImgVfmZqlFjABEX4EZbk-MJvRY-N4CEj-jw?e=07TCr4'
}

export enum StaticLinkExec {
    GuideBook = 'https://dpworld.sharepoint.com/sites/DPWI/Leadership%20Materials/Forms/AllItems.aspx?id=%2Fsites%2FDPWI%2FLeadership%20Materials%2FTalent%20%26%20Leadership%20Team%2F15%2E%20Assessments%2F360%20Talogy%202024%2FDP%20World%5F360%20Guidebook%20Updated%202024%2Epdf&parent=%2Fsites%2FDPWI%2FLeadership%20Materials%2FTalent%20%26%20Leadership%20Team%2F15%2E%20Assessments%2F360%20Talogy%202024&p=true&ga=1',
    AwarnessLink = 'https://dpworld.sharepoint.com/:p:/s/DPWI/ESmbsiUXDU5NtdMUWndquwkBAzV_HxbUWMcKmIoQRT4pjA?e=emBY8R'
}

export enum ChartSurveyTypeExec {
    SELF = 'Self',
    MANAGER = 'Manager',
    PEER_DIRECT_OTHER = 'Peers (Internal and External), Direct Reports, Other',
    PEER_OTHER = 'Peers (Internal and External), Other',
    DIRECT_REPORTS = 'Direct Reports',
    ALL_RATERS_COMBINED = 'ALL RATERS COMBINED',
}

export const PrincipleData = [
    { name: 'Adapt and Evolve', color: '#3230BE', image: 'Adapt and Evolve white' },
    { name: 'Build for a Better Future', color: '#00E68C', image: 'Build for a Better Future white' },
    { name: 'Collaborate to Win', color: '#FF3C14', image: 'Collaborate to Win white' },
    { name: 'Deliver Growth', color: '#1E1450', image: 'Deliver Growth white' },
    { name: 'Prioritise Customers', color: '#FF2261', image: 'Prioritise Customers white' },
];

export enum SetupTypeExec {
    PRINCIPLES_DATA = 'PRINCIPLES_DATA'
}