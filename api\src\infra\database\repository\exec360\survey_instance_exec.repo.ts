import { survey_instance } from "../../../../domain/entities/exec360/survey_instance";
import { BaseRepository } from "../../../../domain/interfaces/repo/baserepository";
import { isurvey_instance_execRepo } from "../../../../domain/interfaces/repo/exec360/isurvey_instance_exec.repo";
import { Config } from "../../../config/model";
import { LoggerService } from "../../../services/loggerservice";

export class survey_instance_execRepo extends BaseRepository<survey_instance> implements isurvey_instance_execRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, survey_instance);
    }
}