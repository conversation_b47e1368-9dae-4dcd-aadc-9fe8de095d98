{"environment": "uat", "tennantId": "ebd93e46-23b9-45fb-93de-75dadf77efe0", "applicationId": "9c5c8f47-67c3-47a3-b270-75b96e519e01", "adminapiurl": "http://adminapi.uat-admin.svc.cluster.local/api/", "adminrequestapiurl": "http://requestapi.uat-admin.svc.cluster.local/api/", "notificationserviceurl": "http://notification.uat-admin.svc.cluster.local/api/", "baseappurl": "https://hoappsuat.dpworld.com/360", "employeedataapiurl": "http://employeedata.uat-admin.svc.cluster.local/api/", "pdfserviceurl": "http://pdfserviceapiuat.uat-admin.svc.cluster.local/api/", "log": {"level": "info"}, "app": {"url": "http://localhost", "port": 3604, "allowedOrigins": ["http://localhost:3604"]}, "database": {"server": "psql-hoapps-nonprod.postgres.database.azure.com", "name": "Lead360", "schema": "assessment_uat", "schema_exec": "exec360_uat", "username": "cadevuser", "password": "LG(h]8SV5X", "port": 5432}, "azure": {"ad": {"clientid": "1adb1c92-7181-447b-ae47-d5d5bbab1b65", "clientsecret": "*************************************", "audience": "api://1adb1c92-7181-447b-ae47-d5d5bbab1b65", "scope": ["General"], "tennantid": "2bd16c9b-7e21-4274-9c06-7919f7647bbb", "authority": "sts.windows.net", "discovery": ".well-known/openid-configuration", "version": "v2.0"}}, "cache": {"type": "node-cache", "server": "localhost", "port": 6379, "password": "", "expirationtime": 300}, "exec360reportgeneratecount": 5}