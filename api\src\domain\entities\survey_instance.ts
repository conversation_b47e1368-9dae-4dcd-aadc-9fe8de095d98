import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import { env } from '../../infra/config';
import { AppModel } from '../base/AppModel';

export type survey_instancePk = "id";
export type survey_instanceId = survey_instance[survey_instancePk];

export class survey_instance extends AppModel {
  id!: number;
  assigned_to?: string;
  assigned_for?: string;
  survey_type?: string;
  user_status?: string;
  workflow_status?: string;
  active?: boolean;
  deleted?: boolean;
  created_on?: Date;
  created_by?: string;
  modified_on?: Date;
  modified_by?: string;
  assigned_to_data?: object;
  assigned_for_data?: object;
  master_id?: number;
  cohort_id?: number;
  is_post_survey?: boolean;
  last_notification_sent?: Date;

  static initModel(sequelize: Sequelize.Sequelize): typeof survey_instance {
    this.HasSoftDelete = true;
    survey_instance.init({
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    assigned_to: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    assigned_for: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    survey_type: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    user_status: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    workflow_status: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    created_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    modified_on: {
      type: DataTypes.DATE,
      allowNull: true
    },
    modified_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    assigned_to_data: {
      type: DataTypes.JSON,
      allowNull: true
    },
    assigned_for_data: {
      type: DataTypes.JSON,
      allowNull: true
    },
    master_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    cohort_id: {
      type: DataTypes.NUMBER,
      allowNull: true
    },
    is_post_survey: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    last_notification_sent: {
      type: DataTypes.DATE,
      allowNull: true
    },
  }, {
    sequelize,
    tableName: 'survey_instance',
    schema: env.database.schema,
    timestamps: false,
    indexes: [
      {
        name: "survey_instance_pk",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  return survey_instance;
  }
}
