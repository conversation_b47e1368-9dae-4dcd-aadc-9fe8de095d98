import { rating_scale } from "../../../../domain/entities/exec360/rating_scale";
import { BaseRepository } from "../../../../domain/interfaces/repo/baserepository";
import { irating_scale_execRepo } from "../../../../domain/interfaces/repo/exec360/irating_scale_exec.repo";
import { Config } from "../../../config/model";
import { LoggerService } from "../../../services/loggerservice";

export class rating_scale_execRepo extends BaseRepository<rating_scale> implements irating_scale_execRepo {

    constructor(config: Config, logger: LoggerService) {
        super(config, logger, rating_scale);
    }
}